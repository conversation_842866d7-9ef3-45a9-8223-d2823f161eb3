"""
Machine Learning Configuration for MealMind
Contains settings for ML models, API keys, and processing parameters
"""

import os
from pathlib import Path

# Base directories
BASE_DIR = Path(__file__).parent
MODELS_DIR = BASE_DIR / 'models'
DATA_DIR = BASE_DIR / 'data'
UPLOADS_DIR = BASE_DIR / 'uploads'

# Create directories if they don't exist
MODELS_DIR.mkdir(exist_ok=True)
DATA_DIR.mkdir(exist_ok=True)
UPLOADS_DIR.mkdir(exist_ok=True)

# API Configuration
class APIConfig:
    # Google Cloud Vision API
    GOOGLE_VISION_API_KEY = os.environ.get('GOOGLE_VISION_API_KEY', '')
    GOOGLE_APPLICATION_CREDENTIALS = os.environ.get('GOOGLE_APPLICATION_CREDENTIALS', '')
    
    # Azure Computer Vision
    AZURE_VISION_ENDPOINT = os.environ.get('AZURE_VISION_ENDPOINT', '')
    AZURE_VISION_KEY = os.environ.get('AZURE_VISION_KEY', '')
    
    # Hugging Face (for transformers)
    HUGGINGFACE_API_KEY = os.environ.get('HUGGINGFACE_API_KEY', '')

# Computer Vision Configuration
class VisionConfig:
    # YOLOv8 model settings
    YOLO_MODEL_PATH = MODELS_DIR / 'yolov8n.pt'
    YOLO_CONFIDENCE_THRESHOLD = 0.5
    YOLO_IOU_THRESHOLD = 0.45
    
    # Image preprocessing settings
    MAX_IMAGE_SIZE = (1024, 1024)
    IMAGE_QUALITY = 95
    SUPPORTED_FORMATS = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    
    # Food detection classes (subset of COCO classes relevant to food)
    FOOD_CLASSES = [
        'apple', 'banana', 'orange', 'broccoli', 'carrot', 'hot dog', 'pizza',
        'donut', 'cake', 'chair', 'dining table', 'bowl', 'cup', 'fork',
        'knife', 'spoon', 'bottle', 'wine glass', 'sandwich', 'person'
    ]

# NLP Configuration
class NLPConfig:
    # spaCy model
    SPACY_MODEL = 'en_core_web_sm'
    
    # Fuzzy matching settings
    FUZZY_MATCH_THRESHOLD = 80
    FUZZY_MATCH_LIMIT = 5
    
    # Ingredient standardization
    INGREDIENT_SYNONYMS = {
        'tomato': ['tomatoes', 'cherry tomatoes', 'roma tomatoes', 'beefsteak tomatoes'],
        'onion': ['onions', 'yellow onion', 'white onion', 'red onion', 'sweet onion'],
        'garlic': ['garlic cloves', 'garlic bulb', 'minced garlic'],
        'chicken': ['chicken breast', 'chicken thigh', 'chicken leg', 'whole chicken'],
        'beef': ['ground beef', 'beef steak', 'beef roast', 'beef chuck'],
        'cheese': ['cheddar cheese', 'mozzarella cheese', 'parmesan cheese', 'swiss cheese'],
        'milk': ['whole milk', 'skim milk', '2% milk', 'almond milk', 'soy milk'],
        'oil': ['olive oil', 'vegetable oil', 'canola oil', 'coconut oil'],
        'pepper': ['bell pepper', 'red pepper', 'green pepper', 'yellow pepper'],
        'rice': ['white rice', 'brown rice', 'jasmine rice', 'basmati rice']
    }
    
    # Common ingredient categories
    INGREDIENT_CATEGORIES = {
        'proteins': ['chicken', 'beef', 'pork', 'fish', 'eggs', 'tofu', 'beans', 'lentils'],
        'vegetables': ['tomato', 'onion', 'carrot', 'broccoli', 'spinach', 'pepper', 'mushroom'],
        'grains': ['rice', 'pasta', 'bread', 'quinoa', 'oats', 'flour'],
        'dairy': ['milk', 'cheese', 'butter', 'yogurt', 'cream'],
        'spices': ['salt', 'pepper', 'garlic', 'ginger', 'cumin', 'paprika', 'oregano'],
        'oils': ['olive oil', 'vegetable oil', 'butter', 'coconut oil']
    }

# Recommendation System Configuration
class RecommendationConfig:
    # Collaborative filtering settings
    N_FACTORS = 50  # Number of latent factors for matrix factorization
    N_EPOCHS = 20   # Training epochs
    LEARNING_RATE = 0.005
    REGULARIZATION = 0.02
    
    # Content-based filtering
    MIN_INGREDIENT_MATCH = 0.3  # Minimum match percentage to consider a recipe
    MAX_RECOMMENDATIONS = 20    # Maximum number of recipes to return
    
    # User preference weights
    PREFERENCE_WEIGHTS = {
        'ingredient_match': 0.4,
        'cuisine_preference': 0.2,
        'difficulty_preference': 0.15,
        'dietary_restrictions': 0.15,
        'user_ratings': 0.1
    }
    
    # Recipe similarity features
    SIMILARITY_FEATURES = [
        'cuisine_type',
        'difficulty_level',
        'prep_time',
        'diet_type',
        'ingredient_categories',
        'cooking_methods'
    ]

# Model Paths and URLs
class ModelPaths:
    # Pre-trained models
    YOLO_MODEL_URL = 'https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt'
    FOOD_CLASSIFIER_URL = 'https://huggingface.co/nateraw/food'
    
    # Local model paths
    INGREDIENT_CLASSIFIER = MODELS_DIR / 'ingredient_classifier.pkl'
    RECIPE_EMBEDDINGS = MODELS_DIR / 'recipe_embeddings.pkl'
    USER_PREFERENCES = MODELS_DIR / 'user_preferences.pkl'
    INGREDIENT_STANDARDIZER = MODELS_DIR / 'ingredient_standardizer.pkl'

# Processing Configuration
class ProcessingConfig:
    # Batch processing
    BATCH_SIZE = 32
    MAX_WORKERS = 4
    
    # Caching
    CACHE_ENABLED = True
    CACHE_EXPIRY = 3600  # 1 hour in seconds
    
    # Logging
    LOG_LEVEL = 'INFO'
    LOG_FILE = BASE_DIR / 'logs' / 'ml_processing.log'

# Feature Engineering Configuration
class FeatureConfig:
    # Image features
    IMAGE_FEATURE_SIZE = 512
    USE_PRETRAINED_FEATURES = True
    
    # Text features
    TEXT_EMBEDDING_SIZE = 384
    MAX_SEQUENCE_LENGTH = 512
    
    # Recipe features
    RECIPE_FEATURE_COLUMNS = [
        'prep_time_normalized',
        'calories_normalized',
        'difficulty_encoded',
        'cuisine_encoded',
        'diet_type_encoded',
        'ingredient_count',
        'protein_ratio',
        'vegetable_ratio',
        'grain_ratio'
    ]

# Development and Testing Configuration
class DevConfig:
    # Mock data for testing
    USE_MOCK_VISION_API = True
    USE_MOCK_RECOMMENDATIONS = False
    
    # Sample ingredients for testing
    SAMPLE_INGREDIENTS = [
        'chicken breast', 'tomatoes', 'onion', 'garlic', 'olive oil',
        'rice', 'cheese', 'eggs', 'milk', 'bread'
    ]
    
    # Test image paths
    TEST_IMAGES_DIR = BASE_DIR / 'test_images'
    
# Export configurations
__all__ = [
    'APIConfig',
    'VisionConfig', 
    'NLPConfig',
    'RecommendationConfig',
    'ModelPaths',
    'ProcessingConfig',
    'FeatureConfig',
    'DevConfig'
]
