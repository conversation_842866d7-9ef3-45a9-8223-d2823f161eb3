"""
Machine Learning Configuration for MealMind
Contains settings for ML models, API keys, and processing parameters
"""

import os
from pathlib import Path

# Base directories
BASE_DIR = Path(__file__).parent
MODELS_DIR = BASE_DIR / 'models'
DATA_DIR = BASE_DIR / 'data'
UPLOADS_DIR = BASE_DIR / 'uploads'

# Create directories if they don't exist
MODELS_DIR.mkdir(exist_ok=True)
DATA_DIR.mkdir(exist_ok=True)
UPLOADS_DIR.mkdir(exist_ok=True)

# API Configuration
class APIConfig:
    # Google Cloud Vision API
    GOOGLE_VISION_API_KEY = os.environ.get('GOOGLE_VISION_API_KEY', '')
    GOOGLE_APPLICATION_CREDENTIALS = os.environ.get('GOOGLE_APPLICATION_CREDENTIALS', '')
    
    # Azure Computer Vision
    AZURE_VISION_ENDPOINT = os.environ.get('AZURE_VISION_ENDPOINT', '')
    AZURE_VISION_KEY = os.environ.get('AZURE_VISION_KEY', '')
    
    # Hugging Face (for transformers)
    HUGGINGFACE_API_KEY = os.environ.get('HUGGINGFACE_API_KEY', '')

# Computer Vision Configuration
class VisionConfig:
    # YOLOv8 model settings
    YOLO_MODEL_PATH = MODELS_DIR / 'yolov8n.pt'
    YOLO_CONFIDENCE_THRESHOLD = 0.5
    YOLO_IOU_THRESHOLD = 0.45
    
    # Image preprocessing settings
    MAX_IMAGE_SIZE = (1024, 1024)
    IMAGE_QUALITY = 95
    SUPPORTED_FORMATS = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    
    # Food detection classes (subset of COCO classes relevant to food)
    FOOD_CLASSES = [
        'apple', 'banana', 'orange', 'broccoli', 'carrot', 'hot dog', 'pizza',
        'donut', 'cake', 'chair', 'dining table', 'bowl', 'cup', 'fork',
        'knife', 'spoon', 'bottle', 'wine glass', 'sandwich', 'person'
    ]

# NLP Configuration
class NLPConfig:
    # spaCy model
    SPACY_MODEL = 'en_core_web_sm'
    
    # Fuzzy matching settings
    FUZZY_MATCH_THRESHOLD = 80
    FUZZY_MATCH_LIMIT = 5
    
    # Enhanced ingredient standardization with expanded database
    INGREDIENT_SYNONYMS = {
        'tomato': ['tomatoes', 'cherry tomatoes', 'roma tomatoes', 'beefsteak tomatoes', 'tomatos'],
        'onion': ['onions', 'yellow onion', 'white onion', 'red onion', 'sweet onion', 'onions diced'],
        'garlic': ['garlic cloves', 'garlic bulb', 'minced garlic', 'garlic minced', 'fresh garlic'],
        'chicken': ['chicken breast', 'chicken thigh', 'chicken leg', 'whole chicken', 'chicken pieces'],
        'beef': ['ground beef', 'beef steak', 'beef roast', 'beef chuck', 'minced beef'],
        'cheese': ['cheddar cheese', 'mozzarella cheese', 'parmesan cheese', 'swiss cheese', 'cheese shredded'],
        'milk': ['whole milk', 'skim milk', '2% milk', 'almond milk', 'soy milk', 'fresh milk'],
        'oil': ['olive oil', 'vegetable oil', 'canola oil', 'coconut oil', 'cooking oil'],
        'pepper': ['bell pepper', 'red pepper', 'green pepper', 'yellow pepper', 'capsicum'],
        'rice': ['white rice', 'brown rice', 'jasmine rice', 'basmati rice', 'long grain rice'],

        # Indian ingredients
        'paneer': ['paneer cubes', 'cottage cheese', 'fresh paneer', 'paneer pieces'],
        'capsicum': ['bell pepper', 'red capsicum', 'green capsicum', 'yellow capsicum', 'pepper'],
        'garam masala': ['garam masala powder', 'whole garam masala', 'garam masala spice'],
        'turmeric': ['turmeric powder', 'haldi', 'ground turmeric', 'fresh turmeric'],
        'cumin': ['cumin seeds', 'jeera', 'ground cumin', 'cumin powder'],
        'coriander': ['coriander seeds', 'dhania', 'ground coriander', 'coriander powder'],
        'mustard seeds': ['mustard seed', 'sarson', 'black mustard seeds', 'yellow mustard seeds'],
        'curry leaves': ['curry leaf', 'kadi patta', 'fresh curry leaves'],
        'cardamom': ['green cardamom', 'cardamom pods', 'elaichi', 'ground cardamom'],
        'cinnamon': ['cinnamon stick', 'dalchini', 'ground cinnamon', 'cinnamon powder'],

        # Additional proteins
        'fish': ['fish fillet', 'fresh fish', 'fish pieces', 'white fish'],
        'shrimp': ['prawns', 'jumbo shrimp', 'medium shrimp', 'cooked shrimp'],
        'eggs': ['egg', 'fresh eggs', 'large eggs', 'whole eggs'],
        'tofu': ['firm tofu', 'soft tofu', 'silken tofu', 'extra firm tofu'],
        'mutton': ['lamb', 'mutton pieces', 'lamb chops', 'ground lamb'],

        # Vegetables
        'potato': ['potatoes', 'russet potatoes', 'red potatoes', 'baby potatoes'],
        'carrot': ['carrots', 'baby carrots', 'carrot sticks', 'fresh carrots'],
        'spinach': ['fresh spinach', 'baby spinach', 'spinach leaves', 'frozen spinach'],
        'mushroom': ['mushrooms', 'button mushrooms', 'cremini mushrooms', 'fresh mushrooms'],
        'broccoli': ['broccoli florets', 'fresh broccoli', 'frozen broccoli'],
        'cauliflower': ['cauliflower florets', 'fresh cauliflower', 'cauliflower head'],
        'eggplant': ['brinjal', 'aubergine', 'baby eggplant', 'japanese eggplant'],
        'okra': ['bhindi', 'lady fingers', 'fresh okra', 'okra pods']
    }
    
    # Common ingredient categories
    INGREDIENT_CATEGORIES = {
        'proteins': ['chicken', 'beef', 'pork', 'fish', 'eggs', 'tofu', 'beans', 'lentils'],
        'vegetables': ['tomato', 'onion', 'carrot', 'broccoli', 'spinach', 'pepper', 'mushroom'],
        'grains': ['rice', 'pasta', 'bread', 'quinoa', 'oats', 'flour'],
        'dairy': ['milk', 'cheese', 'butter', 'yogurt', 'cream'],
        'spices': ['salt', 'pepper', 'garlic', 'ginger', 'cumin', 'paprika', 'oregano'],
        'oils': ['olive oil', 'vegetable oil', 'butter', 'coconut oil']
    }

# Recommendation System Configuration
class RecommendationConfig:
    # Collaborative filtering settings
    N_FACTORS = 50  # Number of latent factors for matrix factorization
    N_EPOCHS = 20   # Training epochs
    LEARNING_RATE = 0.005
    REGULARIZATION = 0.02
    
    # Content-based filtering
    MIN_INGREDIENT_MATCH = 0.3  # Minimum match percentage to consider a recipe
    MAX_RECOMMENDATIONS = 20    # Maximum number of recipes to return
    
    # User preference weights
    PREFERENCE_WEIGHTS = {
        'ingredient_match': 0.4,
        'cuisine_preference': 0.2,
        'difficulty_preference': 0.15,
        'dietary_restrictions': 0.15,
        'user_ratings': 0.1
    }
    
    # Recipe similarity features
    SIMILARITY_FEATURES = [
        'cuisine_type',
        'difficulty_level',
        'prep_time',
        'diet_type',
        'ingredient_categories',
        'cooking_methods'
    ]

# Model Paths and URLs
class ModelPaths:
    # Pre-trained models
    YOLO_MODEL_URL = 'https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt'
    FOOD_CLASSIFIER_URL = 'https://huggingface.co/nateraw/food'
    
    # Local model paths
    INGREDIENT_CLASSIFIER = MODELS_DIR / 'ingredient_classifier.pkl'
    RECIPE_EMBEDDINGS = MODELS_DIR / 'recipe_embeddings.pkl'
    USER_PREFERENCES = MODELS_DIR / 'user_preferences.pkl'
    INGREDIENT_STANDARDIZER = MODELS_DIR / 'ingredient_standardizer.pkl'

# Processing Configuration
class ProcessingConfig:
    # Batch processing
    BATCH_SIZE = 32
    MAX_WORKERS = 4
    
    # Caching
    CACHE_ENABLED = True
    CACHE_EXPIRY = 3600  # 1 hour in seconds
    
    # Logging
    LOG_LEVEL = 'INFO'
    LOG_FILE = BASE_DIR / 'logs' / 'ml_processing.log'

# Feature Engineering Configuration
class FeatureConfig:
    # Image features
    IMAGE_FEATURE_SIZE = 512
    USE_PRETRAINED_FEATURES = True
    
    # Text features
    TEXT_EMBEDDING_SIZE = 384
    MAX_SEQUENCE_LENGTH = 512
    
    # Recipe features
    RECIPE_FEATURE_COLUMNS = [
        'prep_time_normalized',
        'calories_normalized',
        'difficulty_encoded',
        'cuisine_encoded',
        'diet_type_encoded',
        'ingredient_count',
        'protein_ratio',
        'vegetable_ratio',
        'grain_ratio'
    ]

# Development and Testing Configuration
class DevConfig:
    # Mock data for testing
    USE_MOCK_VISION_API = True
    USE_MOCK_RECOMMENDATIONS = False
    
    # Sample ingredients for testing
    SAMPLE_INGREDIENTS = [
        'chicken breast', 'tomatoes', 'onion', 'garlic', 'olive oil',
        'rice', 'cheese', 'eggs', 'milk', 'bread'
    ]
    
    # Test image paths
    TEST_IMAGES_DIR = BASE_DIR / 'test_images'
    
# Export configurations
__all__ = [
    'APIConfig',
    'VisionConfig', 
    'NLPConfig',
    'RecommendationConfig',
    'ModelPaths',
    'ProcessingConfig',
    'FeatureConfig',
    'DevConfig'
]
