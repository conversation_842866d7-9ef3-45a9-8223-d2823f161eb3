from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
import os
import json
from datetime import datetime, timedelta
import requests
from werkzeug.utils import secure_filename
import base64
from enhanced_recipe_matching import calculate_enhanced_recipe_match, get_recipe_compatibility_score
from ai_recipe_generator import AIRecipeGenerator
import uuid
import hashlib
from ingredient_ml import (
    identify_ingredients,
    get_ingredient_suggestions,
    identify_ingredients_from_image,
    get_recipe_recommendations_for_ingredients,
    analyze_ingredient_image
)
try:
    from PIL import Image
    import io
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

app = Flask(__name__)
app.secret_key = 'your-secret-key-here-change-in-production'

# Configure session to be more persistent
app.config['SESSION_PERMANENT'] = True
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=30)  # Sessions last 30 days

# Spoonacular API Configuration
# For demo purposes, you can use this test key (limited requests)
# Get your own free key at: https://spoonacular.com/food-api
SPOONACULAR_API_KEY = os.environ.get('SPOONACULAR_API_KEY', 'demo-key-for-testing')
SPOONACULAR_BASE_URL = 'https://api.spoonacular.com'

# Data storage files
DATA_DIR = 'data'
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)

USERS_FILE = os.path.join(DATA_DIR, 'users.json')
FAVORITES_FILE = os.path.join(DATA_DIR, 'favorites.json')
PROFILES_FILE = os.path.join(DATA_DIR, 'profiles.json')

# Load data from files or initialize empty
def load_data():
    global users, user_favorites, user_profiles

    try:
        with open(USERS_FILE, 'r') as f:
            users = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        users = {}

    try:
        with open(FAVORITES_FILE, 'r') as f:
            user_favorites = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        user_favorites = {}

    try:
        with open(PROFILES_FILE, 'r') as f:
            user_profiles = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        user_profiles = {}

# Save data to files
def save_data():
    with open(USERS_FILE, 'w') as f:
        json.dump(users, f, default=str, indent=2)

    with open(FAVORITES_FILE, 'w') as f:
        json.dump(user_favorites, f, indent=2)

    with open(PROFILES_FILE, 'w') as f:
        json.dump(user_profiles, f, indent=2)

# Initialize data
load_data()

# Create uploads directory if it doesn't exist
UPLOAD_FOLDER = 'uploads'
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Allowed file extensions for image upload
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Spoonacular API Functions
def search_recipes_by_ingredients(ingredients, number=12):
    """
    Search for recipes using Spoonacular API based on ingredients
    """
    # Check if API key is configured
    if SPOONACULAR_API_KEY in ['your-spoonacular-api-key-here', 'demo-key-for-testing']:
        print("Spoonacular API key not configured, using local recipes")
        return []

    try:
        # Convert ingredients list to comma-separated string
        if isinstance(ingredients, list):
            ingredients_str = ','.join(ingredients)
        else:
            ingredients_str = ingredients

        print(f"Searching Spoonacular for ingredients: {ingredients_str}")

        url = f"{SPOONACULAR_BASE_URL}/recipes/findByIngredients"
        params = {
            'apiKey': SPOONACULAR_API_KEY,
            'ingredients': ingredients_str,
            'number': number,
            'ranking': 2,  # Maximize used ingredients
            'ignorePantry': True
        }

        print(f"Making request to: {url}")
        print(f"With params: {params}")

        response = requests.get(url, params=params, timeout=10)
        print(f"Response status: {response.status_code}")

        if response.status_code == 401:
            print("Invalid API key")
            return []
        elif response.status_code == 402:
            print("API quota exceeded")
            return []

        response.raise_for_status()

        recipes_data = response.json()
        print(f"Found {len(recipes_data)} recipes from Spoonacular")

        # Transform Spoonacular data to our format
        transformed_recipes = []
        for recipe in recipes_data:
            transformed_recipe = {
                'id': recipe['id'],
                'name': recipe['title'],
                'image': recipe.get('image', ''),
                'used_ingredients': [ing['name'] for ing in recipe.get('usedIngredients', [])],
                'missed_ingredients': [ing['name'] for ing in recipe.get('missedIngredients', [])],
                'unused_ingredients': [ing['name'] for ing in recipe.get('unusedIngredients', [])],
                'used_ingredient_count': recipe.get('usedIngredientCount', 0),
                'missed_ingredient_count': recipe.get('missedIngredientCount', 0),
                'likes': recipe.get('likes', 0),
                'match_percentage': calculate_match_percentage(
                    recipe.get('usedIngredientCount', 0),
                    recipe.get('missedIngredientCount', 0)
                ),
                'prep_time': 30,  # Default value, will be updated when getting details
                'calories': 300   # Default value, will be updated when getting details
            }
            transformed_recipes.append(transformed_recipe)

        return transformed_recipes

    except requests.exceptions.RequestException as e:
        print(f"Error fetching recipes from Spoonacular: {e}")
        return []
    except Exception as e:
        print(f"Error processing Spoonacular response: {e}")
        return []

def get_recipe_details(recipe_id):
    """
    Get detailed recipe information from Spoonacular API
    """
    try:
        url = f"{SPOONACULAR_BASE_URL}/recipes/{recipe_id}/information"
        params = {
            'apiKey': SPOONACULAR_API_KEY,
            'includeNutrition': True
        }

        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()

        recipe_data = response.json()

        # Transform to our format
        transformed_recipe = {
            'id': recipe_data['id'],
            'name': recipe_data['title'],
            'image': recipe_data.get('image', ''),
            'prep_time': recipe_data.get('readyInMinutes', 30),
            'servings': recipe_data.get('servings', 4),
            'calories': extract_calories(recipe_data.get('nutrition', {})),
            'cuisine': ', '.join(recipe_data.get('cuisines', ['International'])),
            'diet_type': determine_diet_type(recipe_data.get('diets', [])),
            'difficulty': determine_difficulty(recipe_data.get('readyInMinutes', 30)),
            'instructions': extract_instructions(recipe_data.get('analyzedInstructions', [])),
            'ingredients': extract_ingredients(recipe_data.get('extendedIngredients', [])),
            'summary': recipe_data.get('summary', ''),
            'source_url': recipe_data.get('sourceUrl', ''),
            'spoon_url': recipe_data.get('spoonacularSourceUrl', '')
        }

        return transformed_recipe

    except requests.exceptions.RequestException as e:
        print(f"Error fetching recipe details from Spoonacular: {e}")
        return None
    except Exception as e:
        print(f"Error processing recipe details: {e}")
        return None

def calculate_match_percentage(used_count, missed_count):
    """Calculate percentage match based on used vs missed ingredients"""
    total = used_count + missed_count
    if total == 0:
        return 0
    return round((used_count / total) * 100)

def extract_calories(nutrition_data):
    """Extract calories from nutrition data"""
    if not nutrition_data or 'nutrients' not in nutrition_data:
        return 300  # Default value

    for nutrient in nutrition_data['nutrients']:
        if nutrient.get('name') == 'Calories':
            return int(nutrient.get('amount', 300))
    return 300

def determine_diet_type(diets):
    """Determine diet type from Spoonacular diet tags"""
    if 'vegan' in diets:
        return 'Vegan'
    elif 'vegetarian' in diets:
        return 'Vegetarian'
    else:
        return 'Non-Veg'

def determine_difficulty(ready_time):
    """Determine difficulty based on preparation time"""
    if ready_time <= 20:
        return 'Easy'
    elif ready_time <= 45:
        return 'Medium'
    else:
        return 'Hard'

def extract_instructions(analyzed_instructions):
    """Extract step-by-step instructions"""
    instructions = []
    if analyzed_instructions:
        for instruction_group in analyzed_instructions:
            for step in instruction_group.get('steps', []):
                instructions.append(step.get('step', ''))
    return instructions

def extract_ingredients(extended_ingredients):
    """Extract and categorize ingredients"""
    ingredients = {
        'main': [],
        'spices': [],
        'dairy': [],
        'vegetables': [],
        'other': []
    }

    for ingredient in extended_ingredients:
        name = ingredient.get('name', '')
        amount = ingredient.get('amount', '')
        unit = ingredient.get('unit', '')

        # Format ingredient with amount and unit
        formatted_ingredient = f"{amount} {unit} {name}".strip()

        # Categorize ingredients (simplified categorization)
        aisle = ingredient.get('aisle', '').lower()
        if 'spice' in aisle or 'condiment' in aisle:
            ingredients['spices'].append(formatted_ingredient)
        elif 'dairy' in aisle or 'cheese' in aisle:
            ingredients['dairy'].append(formatted_ingredient)
        elif 'produce' in aisle or 'vegetable' in name.lower():
            ingredients['vegetables'].append(formatted_ingredient)
        elif 'meat' in aisle or 'seafood' in aisle:
            ingredients['main'].append(formatted_ingredient)
        else:
            ingredients['other'].append(formatted_ingredient)

    return ingredients

# Comprehensive recipe database with 4 main cuisines (50 recipes each)
recipes = [
    # ITALIAN CUISINE (50 recipes)
    {
        'id': 1,
        'name': 'Spaghetti Carbonara',
        'ingredients': {
            'main': ['400g spaghetti pasta', '4 large eggs', '150g pancetta'],
            'dairy': ['100g parmesan cheese (grated)', '2 tbsp heavy cream'],
            'aromatics': ['3 cloves garlic (minced)', '1 medium onion (diced)'],
            'seasonings': ['1 tsp black pepper', '1 tsp salt', '2 tbsp olive oil'],
            'garnish': ['fresh parsley', 'extra parmesan']
        },
        'instructions': [
            'Bring salted water to boil and cook spaghetti until al dente (8-10 minutes)',
            'Heat olive oil in large skillet, add pancetta and cook until crispy (5-7 minutes)',
            'Add garlic and onion, sauté until fragrant (1-2 minutes)',
            'Whisk eggs with parmesan, black pepper, and salt in a bowl',
            'Reserve 1 cup pasta water before draining',
            'Add hot pasta to skillet with pancetta, remove from heat',
            'Quickly mix in egg mixture, tossing continuously for creamy sauce',
            'Add pasta water if needed, serve with parmesan and parsley'
        ],
        'prep_time': 30,
        'servings': 4,
        'calories': 450,
        'cuisine': 'Italian',
        'diet_type': 'Non-Veg',
        'difficulty': 'Medium',
        'tips': 'Work quickly with eggs to prevent scrambling. Use residual heat to cook eggs gently.'
    },
    {
        'id': 2,
        'name': 'Margherita Pizza',
        'ingredients': {
            'main': ['1 pizza dough', '200g mozzarella cheese', '3 large tomatoes'],
            'dairy': ['100g fresh mozzarella', '50g parmesan'],
            'aromatics': ['2 cloves garlic', '1 small onion'],
            'seasonings': ['3 tbsp olive oil', '1 tsp salt', '1/2 tsp black pepper'],
            'herbs': ['fresh basil leaves', 'dried oregano']
        },
        'instructions': [
            'Preheat oven to 475°F (245°C)',
            'Roll out pizza dough on floured surface to 12-inch circle',
            'Brush dough with olive oil and minced garlic',
            'Slice tomatoes and mozzarella into thin rounds',
            'Spread tomato slices evenly on dough',
            'Add mozzarella slices and sprinkle with salt and pepper',
            'Bake for 12-15 minutes until crust is golden and cheese bubbles',
            'Remove from oven, top with fresh basil and serve hot'
        ],
        'prep_time': 25,
        'servings': 2,
        'calories': 380,
        'cuisine': 'Italian',
        'diet_type': 'Vegetarian',
        'difficulty': 'Easy',
        'tips': 'Use fresh mozzarella for best flavor. Don\'t overload with toppings.'
    },
    {
        'id': 3,
        'name': 'Chicken Parmigiana',
        'ingredients': {
            'main': ['4 chicken breasts', '200g spaghetti', '2 cups breadcrumbs'],
            'dairy': ['150g mozzarella', '100g parmesan', '2 eggs'],
            'vegetables': ['400g tomato sauce', '1 onion', '2 cloves garlic'],
            'seasonings': ['1/2 cup flour', '1/4 cup olive oil', 'salt', 'pepper'],
            'herbs': ['fresh basil', 'dried oregano', 'parsley']
        },
        'instructions': [
            'Pound chicken breasts to 1/2 inch thickness',
            'Set up breading station: flour, beaten eggs, breadcrumbs',
            'Dredge chicken in flour, dip in eggs, coat with breadcrumbs',
            'Heat oil in large skillet, fry chicken until golden (4-5 min per side)',
            'Cook spaghetti according to package directions',
            'Layer fried chicken with tomato sauce and cheeses in baking dish',
            'Bake at 375°F for 20-25 minutes until cheese melts',
            'Serve over spaghetti with fresh basil'
        ],
        'prep_time': 45,
        'servings': 4,
        'calories': 520,
        'cuisine': 'Italian',
        'diet_type': 'Non-Veg',
        'difficulty': 'Medium',
        'tips': 'Don\'t skip pounding the chicken - it ensures even cooking.'
    }
]

def generate_all_recipes():
    """Generate 80 recipes (20 each for Italian, Chinese, American, Indian)"""
    all_recipes = []

    # Italian recipes (20)
    italian_recipes = [
        {'name': 'Spaghetti Carbonara', 'ingredients': {'main': ['400g spaghetti', '4 eggs', '150g pancetta'], 'dairy': ['100g parmesan cheese', '2 tbsp heavy cream'], 'aromatics': ['3 garlic cloves', '1 medium onion'], 'seasonings': ['Black pepper', 'Salt', 'Olive oil'], 'herbs': ['Fresh parsley', 'Fresh basil']}, 'prep_time': 30, 'calories': 450, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Margherita Pizza', 'ingredients': {'main': ['1 pizza dough', '200g mozzarella cheese', '3 large tomatoes'], 'dairy': ['Fresh mozzarella', 'Parmesan cheese'], 'seasonings': ['Olive oil', 'Salt', 'Black pepper'], 'herbs': ['Fresh basil leaves', 'Dried oregano']}, 'prep_time': 25, 'calories': 380, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Chicken Parmigiana', 'ingredients': {'main': ['4 chicken breasts', '200g spaghetti', '2 cups breadcrumbs'], 'dairy': ['150g mozzarella', '100g parmesan', '2 eggs'], 'vegetables': ['400g tomato sauce', '1 onion', '2 garlic cloves'], 'seasonings': ['Flour', 'Olive oil', 'Salt', 'Pepper'], 'herbs': ['Fresh basil', 'Oregano', 'Parsley']}, 'prep_time': 45, 'calories': 520, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Fettuccine Alfredo', 'ingredients': {'main': ['400g fettuccine pasta', '200g butter'], 'dairy': ['300ml heavy cream', '150g parmesan cheese'], 'aromatics': ['3 garlic cloves'], 'seasonings': ['Salt', 'White pepper', 'Nutmeg'], 'herbs': ['Fresh parsley']}, 'prep_time': 20, 'calories': 480, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Lasagna Bolognese', 'ingredients': {'main': ['12 lasagna sheets', '500g ground beef', '400g ricotta cheese'], 'dairy': ['300g mozzarella', '100g parmesan', '200ml milk'], 'vegetables': ['400g tomatoes', '2 onions', '2 carrots', '2 celery stalks'], 'seasonings': ['Olive oil', 'Salt', 'Black pepper'], 'herbs': ['Fresh basil', 'Oregano', 'Thyme']}, 'prep_time': 90, 'calories': 520, 'difficulty': 'Hard', 'diet_type': 'Non-Veg'},
        {'name': 'Risotto Mushroom', 'ingredients': {'main': ['300g arborio rice', '400g mixed mushrooms'], 'dairy': ['100g parmesan cheese', '50g butter'], 'vegetables': ['1 onion', '2 garlic cloves'], 'seasonings': ['White wine', 'Chicken stock', 'Olive oil', 'Salt', 'Pepper'], 'herbs': ['Fresh thyme', 'Parsley']}, 'prep_time': 35, 'calories': 380, 'difficulty': 'Medium', 'diet_type': 'Vegetarian'},
        {'name': 'Caprese Salad', 'ingredients': {'main': ['4 large tomatoes'], 'dairy': ['250g fresh mozzarella'], 'seasonings': ['Extra virgin olive oil', 'Balsamic vinegar', 'Salt', 'Black pepper'], 'herbs': ['Fresh basil leaves']}, 'prep_time': 10, 'calories': 220, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Osso Buco', 'ingredients': {'main': ['4 veal shanks', '2 tbsp flour'], 'vegetables': ['2 onions', '2 carrots', '2 celery stalks', '400g tomatoes'], 'seasonings': ['White wine', 'Beef stock', 'Olive oil', 'Salt', 'Pepper'], 'herbs': ['Fresh thyme', 'Bay leaves', 'Parsley']}, 'prep_time': 120, 'calories': 450, 'difficulty': 'Hard', 'diet_type': 'Non-Veg'},
        {'name': 'Minestrone Soup', 'ingredients': {'main': ['200g pasta', '400g mixed beans'], 'vegetables': ['2 onions', '3 carrots', '3 celery stalks', '400g tomatoes', '200g spinach'], 'seasonings': ['Vegetable stock', 'Olive oil', 'Salt', 'Pepper'], 'herbs': ['Fresh basil', 'Oregano', 'Parsley']}, 'prep_time': 40, 'calories': 280, 'difficulty': 'Easy', 'diet_type': 'Vegan'},
        {'name': 'Tiramisu', 'ingredients': {'main': ['24 ladyfinger cookies', '6 egg yolks'], 'dairy': ['500g mascarpone cheese', '200ml heavy cream'], 'seasonings': ['Strong coffee', 'Sugar', 'Cocoa powder', 'Dark rum'], 'others': ['Dark chocolate shavings']}, 'prep_time': 30, 'calories': 420, 'difficulty': 'Medium', 'diet_type': 'Vegetarian'},
        {'name': 'Penne Arrabbiata', 'ingredients': {'main': ['400g penne pasta'], 'vegetables': ['400g tomatoes', '4 garlic cloves', '2 red chilies'], 'seasonings': ['Olive oil', 'Salt', 'Red pepper flakes'], 'herbs': ['Fresh parsley', 'Fresh basil']}, 'prep_time': 25, 'calories': 350, 'difficulty': 'Easy', 'diet_type': 'Vegan'},
        {'name': 'Gnocchi with Sage Butter', 'ingredients': {'main': ['500g potato gnocchi'], 'dairy': ['100g butter', '100g parmesan cheese'], 'seasonings': ['Salt', 'Black pepper'], 'herbs': ['Fresh sage leaves']}, 'prep_time': 15, 'calories': 380, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Bruschetta', 'ingredients': {'main': ['8 bread slices'], 'vegetables': ['4 tomatoes', '2 garlic cloves'], 'seasonings': ['Extra virgin olive oil', 'Balsamic vinegar', 'Salt', 'Pepper'], 'herbs': ['Fresh basil']}, 'prep_time': 15, 'calories': 180, 'difficulty': 'Easy', 'diet_type': 'Vegan'},
        {'name': 'Seafood Risotto', 'ingredients': {'main': ['300g arborio rice', '400g mixed seafood'], 'vegetables': ['1 onion', '2 garlic cloves'], 'seasonings': ['White wine', 'Fish stock', 'Olive oil', 'Salt', 'Pepper'], 'herbs': ['Fresh parsley', 'Lemon zest']}, 'prep_time': 40, 'calories': 420, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Eggplant Parmigiana', 'ingredients': {'main': ['2 large eggplants'], 'dairy': ['300g mozzarella', '100g parmesan cheese'], 'vegetables': ['400g tomato sauce'], 'seasonings': ['Flour', 'Olive oil', 'Salt', 'Pepper'], 'herbs': ['Fresh basil', 'Oregano']}, 'prep_time': 60, 'calories': 320, 'difficulty': 'Medium', 'diet_type': 'Vegetarian'},
        {'name': 'Prosciutto e Melone', 'ingredients': {'main': ['200g prosciutto', '1 cantaloupe melon'], 'seasonings': ['Black pepper'], 'herbs': ['Fresh mint leaves']}, 'prep_time': 10, 'calories': 180, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Pasta Puttanesca', 'ingredients': {'main': ['400g spaghetti'], 'vegetables': ['400g tomatoes', '3 garlic cloves'], 'seasonings': ['Olive oil', 'Capers', 'Black olives', 'Anchovies', 'Red pepper flakes'], 'herbs': ['Fresh parsley']}, 'prep_time': 25, 'calories': 380, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Polenta with Mushrooms', 'ingredients': {'main': ['200g polenta', '400g mushrooms'], 'dairy': ['100g parmesan cheese', '50g butter'], 'vegetables': ['1 onion', '2 garlic cloves'], 'seasonings': ['Vegetable stock', 'Olive oil', 'Salt', 'Pepper'], 'herbs': ['Fresh thyme', 'Parsley']}, 'prep_time': 35, 'calories': 320, 'difficulty': 'Medium', 'diet_type': 'Vegetarian'},
        {'name': 'Vitello Tonnato', 'ingredients': {'main': ['800g veal roast', '200g tuna'], 'dairy': ['200ml mayonnaise'], 'seasonings': ['White wine', 'Capers', 'Lemon juice', 'Olive oil'], 'herbs': ['Fresh parsley']}, 'prep_time': 90, 'calories': 380, 'difficulty': 'Hard', 'diet_type': 'Non-Veg'},
        {'name': 'Focaccia Bread', 'ingredients': {'main': ['500g bread flour', '7g active yeast'], 'vegetables': ['2 onions', 'Cherry tomatoes'], 'seasonings': ['Olive oil', 'Salt', 'Sugar'], 'herbs': ['Fresh rosemary', 'Oregano']}, 'prep_time': 120, 'calories': 280, 'difficulty': 'Medium', 'diet_type': 'Vegan'}
    ]

    # Chinese recipes (20)
    chinese_recipes = [
        {'name': 'Kung Pao Chicken', 'ingredients': {'main': ['500g chicken breast', '100g peanuts'], 'vegetables': ['2 bell peppers', '1 onion', '3 garlic cloves', '2 dried chilies'], 'seasonings': ['Soy sauce', 'Rice vinegar', 'Cornstarch', 'Sesame oil'], 'aromatics': ['Fresh ginger', 'Green onions']}, 'prep_time': 25, 'calories': 380, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Sweet and Sour Pork', 'ingredients': {'main': ['500g pork shoulder'], 'vegetables': ['1 bell pepper', '1 onion', '200g pineapple'], 'seasonings': ['Rice vinegar', 'Ketchup', 'Sugar', 'Soy sauce', 'Cornstarch'], 'aromatics': ['Fresh ginger', 'Garlic']}, 'prep_time': 35, 'calories': 420, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Mapo Tofu', 'ingredients': {'main': ['400g silken tofu', '200g ground pork'], 'vegetables': ['3 garlic cloves', '2 green onions'], 'seasonings': ['Doubanjiang', 'Soy sauce', 'Cornstarch', 'Sichuan peppercorns'], 'aromatics': ['Fresh ginger']}, 'prep_time': 20, 'calories': 350, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Fried Rice', 'ingredients': {'main': ['3 cups cooked rice', '3 eggs'], 'vegetables': ['1 cup mixed vegetables', '3 green onions'], 'seasonings': ['Soy sauce', 'Sesame oil', 'Vegetable oil'], 'aromatics': ['Garlic', 'Ginger']}, 'prep_time': 15, 'calories': 320, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Hot Pot', 'ingredients': {'main': ['300g beef slices', '200g tofu', '200g mushrooms'], 'vegetables': ['Napa cabbage', 'Bean sprouts'], 'seasonings': ['Hot pot base', 'Sesame oil', 'Soy sauce'], 'aromatics': ['Garlic', 'Scallions']}, 'prep_time': 30, 'calories': 380, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Dumplings', 'ingredients': {'main': ['30 dumpling wrappers', '300g ground pork'], 'vegetables': ['2 cups cabbage', '2 green onions'], 'seasonings': ['Soy sauce', 'Sesame oil', 'Rice wine'], 'aromatics': ['Fresh ginger', 'Garlic']}, 'prep_time': 45, 'calories': 280, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'General Tso Chicken', 'ingredients': {'main': ['500g chicken thighs'], 'vegetables': ['2 garlic cloves', '1 inch ginger'], 'seasonings': ['Soy sauce', 'Rice vinegar', 'Sugar', 'Cornstarch', 'Red pepper flakes'], 'aromatics': ['Green onions']}, 'prep_time': 30, 'calories': 450, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Chow Mein', 'ingredients': {'main': ['400g fresh noodles', '200g chicken'], 'vegetables': ['1 cup bean sprouts', '1 bell pepper', '2 carrots'], 'seasonings': ['Soy sauce', 'Oyster sauce', 'Sesame oil'], 'aromatics': ['Garlic', 'Ginger']}, 'prep_time': 20, 'calories': 380, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Wonton Soup', 'ingredients': {'main': ['20 wonton wrappers', '200g ground pork'], 'vegetables': ['Bok choy', '2 green onions'], 'seasonings': ['Chicken broth', 'Soy sauce', 'Sesame oil'], 'aromatics': ['Fresh ginger', 'Garlic']}, 'prep_time': 40, 'calories': 250, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Orange Chicken', 'ingredients': {'main': ['500g chicken breast'], 'vegetables': ['1 orange zest'], 'seasonings': ['Orange juice', 'Soy sauce', 'Rice vinegar', 'Sugar', 'Cornstarch'], 'aromatics': ['Fresh ginger', 'Garlic']}, 'prep_time': 25, 'calories': 420, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Ma Po Eggplant', 'ingredients': {'main': ['2 large eggplants'], 'vegetables': ['3 garlic cloves', '2 green onions'], 'seasonings': ['Doubanjiang', 'Soy sauce', 'Sugar', 'Cornstarch'], 'aromatics': ['Fresh ginger']}, 'prep_time': 25, 'calories': 180, 'difficulty': 'Easy', 'diet_type': 'Vegan'},
        {'name': 'Beef and Broccoli', 'ingredients': {'main': ['400g beef sirloin', '300g broccoli'], 'vegetables': ['2 garlic cloves'], 'seasonings': ['Soy sauce', 'Oyster sauce', 'Cornstarch', 'Sesame oil'], 'aromatics': ['Fresh ginger']}, 'prep_time': 20, 'calories': 320, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Spring Rolls', 'ingredients': {'main': ['12 spring roll wrappers'], 'vegetables': ['2 cups cabbage', '1 carrot', '100g bean sprouts'], 'seasonings': ['Soy sauce', 'Sesame oil', 'Vegetable oil'], 'aromatics': ['Garlic', 'Ginger']}, 'prep_time': 30, 'calories': 220, 'difficulty': 'Medium', 'diet_type': 'Vegan'},
        {'name': 'Char Siu Pork', 'ingredients': {'main': ['800g pork shoulder'], 'seasonings': ['Hoisin sauce', 'Soy sauce', 'Honey', 'Rice wine', 'Five spice powder'], 'aromatics': ['Garlic', 'Ginger']}, 'prep_time': 180, 'calories': 380, 'difficulty': 'Hard', 'diet_type': 'Non-Veg'},
        {'name': 'Congee', 'ingredients': {'main': ['1 cup rice', '200g chicken'], 'vegetables': ['2 green onions'], 'seasonings': ['Chicken broth', 'Soy sauce', 'Sesame oil', 'Salt'], 'aromatics': ['Fresh ginger']}, 'prep_time': 60, 'calories': 280, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Dan Dan Noodles', 'ingredients': {'main': ['400g fresh noodles', '200g ground pork'], 'vegetables': ['2 green onions'], 'seasonings': ['Sesame paste', 'Chili oil', 'Soy sauce', 'Black vinegar'], 'aromatics': ['Garlic', 'Sichuan peppercorns']}, 'prep_time': 25, 'calories': 450, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Salt and Pepper Shrimp', 'ingredients': {'main': ['500g large shrimp'], 'vegetables': ['2 jalapeños', '3 garlic cloves'], 'seasonings': ['Salt', 'White pepper', 'Cornstarch', 'Vegetable oil'], 'aromatics': ['Green onions']}, 'prep_time': 20, 'calories': 280, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Twice Cooked Pork', 'ingredients': {'main': ['400g pork belly'], 'vegetables': ['2 bell peppers', '1 onion', '3 garlic cloves'], 'seasonings': ['Doubanjiang', 'Soy sauce', 'Sugar'], 'aromatics': ['Fresh ginger']}, 'prep_time': 35, 'calories': 420, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Egg Drop Soup', 'ingredients': {'main': ['4 eggs'], 'vegetables': ['2 green onions'], 'seasonings': ['Chicken broth', 'Cornstarch', 'Sesame oil', 'White pepper'], 'aromatics': ['Fresh ginger']}, 'prep_time': 15, 'calories': 120, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'}
    ]

    # Add Italian recipes
    for i, recipe_data in enumerate(italian_recipes):
        recipe = create_recipe(i + 1, recipe_data, 'Italian')
        all_recipes.append(recipe)

    # American recipes (20)
    american_recipes = [
        {'name': 'Classic Burger', 'ingredients': {'main': ['500g ground beef', '4 burger buns'], 'vegetables': ['2 tomatoes', '1 onion', 'Lettuce leaves'], 'dairy': ['4 cheese slices'], 'seasonings': ['Salt', 'Black pepper', 'Ketchup', 'Mustard'], 'others': ['Pickles']}, 'prep_time': 20, 'calories': 550, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'BBQ Ribs', 'ingredients': {'main': ['1kg pork ribs'], 'seasonings': ['BBQ sauce', 'Brown sugar', 'Paprika', 'Garlic powder', 'Onion powder', 'Salt', 'Black pepper'], 'aromatics': ['Apple cider vinegar']}, 'prep_time': 180, 'calories': 480, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Mac and Cheese', 'ingredients': {'main': ['400g macaroni pasta'], 'dairy': ['300g cheddar cheese', '200ml milk', '50g butter'], 'seasonings': ['Flour', 'Salt', 'Black pepper', 'Mustard powder'], 'others': ['Breadcrumbs']}, 'prep_time': 30, 'calories': 420, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Fried Chicken', 'ingredients': {'main': ['1 whole chicken', '2 cups flour'], 'dairy': ['2 cups buttermilk'], 'seasonings': ['Salt', 'Black pepper', 'Paprika', 'Garlic powder', 'Cayenne pepper'], 'others': ['Vegetable oil']}, 'prep_time': 45, 'calories': 520, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Caesar Salad', 'ingredients': {'main': ['2 romaine lettuce heads'], 'dairy': ['100g parmesan cheese', '1 egg yolk'], 'seasonings': ['Olive oil', 'Lemon juice', 'Worcestershire sauce', 'Garlic'], 'others': ['Croutons', 'Anchovies']}, 'prep_time': 15, 'calories': 280, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Clam Chowder', 'ingredients': {'main': ['500g clams', '4 potatoes'], 'dairy': ['200ml heavy cream', '50g butter'], 'vegetables': ['2 onions', '2 celery stalks'], 'seasonings': ['Flour', 'Bay leaves', 'Thyme', 'Salt', 'Pepper'], 'others': ['Bacon']}, 'prep_time': 40, 'calories': 350, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Buffalo Wings', 'ingredients': {'main': ['1kg chicken wings'], 'seasonings': ['Hot sauce', 'Butter', 'Garlic powder', 'Salt'], 'others': ['Blue cheese dressing', 'Celery sticks']}, 'prep_time': 35, 'calories': 380, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Meatloaf', 'ingredients': {'main': ['800g ground beef', '2 eggs'], 'vegetables': ['1 onion', '2 garlic cloves'], 'seasonings': ['Breadcrumbs', 'Ketchup', 'Worcestershire sauce', 'Salt', 'Pepper'], 'herbs': ['Fresh parsley']}, 'prep_time': 75, 'calories': 420, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Apple Pie', 'ingredients': {'main': ['6 apples', '2 pie crusts'], 'seasonings': ['Sugar', 'Cinnamon', 'Nutmeg', 'Flour', 'Butter'], 'others': ['Lemon juice']}, 'prep_time': 90, 'calories': 320, 'difficulty': 'Medium', 'diet_type': 'Vegetarian'},
        {'name': 'Pancakes', 'ingredients': {'main': ['2 cups flour', '2 eggs'], 'dairy': ['1.5 cups milk', '50g butter'], 'seasonings': ['Sugar', 'Baking powder', 'Salt'], 'others': ['Maple syrup']}, 'prep_time': 20, 'calories': 280, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Pulled Pork', 'ingredients': {'main': ['1.5kg pork shoulder'], 'seasonings': ['BBQ sauce', 'Brown sugar', 'Paprika', 'Chili powder', 'Cumin', 'Salt'], 'aromatics': ['Apple cider vinegar', 'Onion powder']}, 'prep_time': 480, 'calories': 420, 'difficulty': 'Hard', 'diet_type': 'Non-Veg'},
        {'name': 'Cornbread', 'ingredients': {'main': ['1 cup cornmeal', '1 cup flour'], 'dairy': ['1 cup milk', '100g butter', '2 eggs'], 'seasonings': ['Sugar', 'Baking powder', 'Salt']}, 'prep_time': 30, 'calories': 220, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Jambalaya', 'ingredients': {'main': ['2 cups rice', '300g shrimp', '200g sausage'], 'vegetables': ['2 onions', '2 bell peppers', '3 celery stalks', '400g tomatoes'], 'seasonings': ['Chicken stock', 'Cajun seasoning', 'Bay leaves'], 'aromatics': ['Garlic', 'Green onions']}, 'prep_time': 45, 'calories': 380, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Coleslaw', 'ingredients': {'main': ['1 cabbage head', '2 carrots'], 'dairy': ['Mayonnaise'], 'seasonings': ['Apple cider vinegar', 'Sugar', 'Salt', 'Celery seed']}, 'prep_time': 15, 'calories': 120, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Biscuits and Gravy', 'ingredients': {'main': ['2 cups flour', '300g sausage'], 'dairy': ['Buttermilk', 'Butter', 'Milk'], 'seasonings': ['Baking powder', 'Salt', 'Black pepper']}, 'prep_time': 30, 'calories': 450, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Chili Con Carne', 'ingredients': {'main': ['500g ground beef', '400g kidney beans'], 'vegetables': ['2 onions', '3 garlic cloves', '400g tomatoes', '2 bell peppers'], 'seasonings': ['Chili powder', 'Cumin', 'Paprika', 'Salt', 'Pepper'], 'aromatics': ['Bay leaves']}, 'prep_time': 60, 'calories': 320, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Grilled Cheese', 'ingredients': {'main': ['8 bread slices'], 'dairy': ['200g cheddar cheese', 'Butter']}, 'prep_time': 10, 'calories': 380, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Chicken and Waffles', 'ingredients': {'main': ['4 chicken breasts', '2 cups flour'], 'dairy': ['2 eggs', 'Milk', 'Butter'], 'seasonings': ['Baking powder', 'Salt', 'Sugar', 'Vanilla'], 'others': ['Maple syrup']}, 'prep_time': 40, 'calories': 520, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Potato Salad', 'ingredients': {'main': ['1kg potatoes', '4 eggs'], 'dairy': ['Mayonnaise'], 'vegetables': ['2 onions', '3 celery stalks'], 'seasonings': ['Mustard', 'Salt', 'Pepper', 'Paprika'], 'herbs': ['Fresh dill']}, 'prep_time': 30, 'calories': 250, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Meatball Subs', 'ingredients': {'main': ['500g ground beef', '4 sub rolls'], 'dairy': ['200g mozzarella cheese'], 'vegetables': ['400g marinara sauce', '1 onion'], 'seasonings': ['Breadcrumbs', 'Italian seasoning', 'Salt', 'Pepper'], 'herbs': ['Fresh basil']}, 'prep_time': 35, 'calories': 480, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'}
    ]

    # Indian recipes (20)
    indian_recipes = [
        {'name': 'Butter Chicken', 'ingredients': {'main': ['600g chicken breast'], 'dairy': ['200ml heavy cream', '100g butter', '200g yogurt'], 'vegetables': ['400g tomatoes', '2 onions', '4 garlic cloves'], 'spices': ['Garam masala', 'Turmeric', 'Cumin', 'Coriander', 'Red chili powder'], 'aromatics': ['Fresh ginger', 'Green chilies']}, 'prep_time': 45, 'calories': 520, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Dal Tadka', 'ingredients': {'main': ['200g yellow lentils'], 'vegetables': ['2 onions', '3 tomatoes', '4 garlic cloves'], 'spices': ['Cumin seeds', 'Turmeric', 'Coriander powder', 'Red chili powder'], 'aromatics': ['Fresh ginger', 'Green chilies'], 'seasonings': ['Ghee', 'Salt']}, 'prep_time': 30, 'calories': 280, 'difficulty': 'Easy', 'diet_type': 'Vegan'},
        {'name': 'Palak Paneer', 'ingredients': {'main': ['400g paneer', '500g spinach'], 'dairy': ['100ml cream'], 'vegetables': ['2 onions', '3 tomatoes', '4 garlic cloves'], 'spices': ['Garam masala', 'Cumin', 'Coriander'], 'aromatics': ['Fresh ginger', 'Green chilies']}, 'prep_time': 35, 'calories': 320, 'difficulty': 'Medium', 'diet_type': 'Vegetarian'},
        {'name': 'Chicken Biryani', 'ingredients': {'main': ['500g basmati rice', '800g chicken'], 'dairy': ['200g yogurt'], 'vegetables': ['3 onions'], 'spices': ['Biryani masala', 'Saffron', 'Bay leaves', 'Cardamom', 'Cinnamon'], 'aromatics': ['Ginger-garlic paste', 'Mint', 'Coriander'], 'seasonings': ['Ghee', 'Salt']}, 'prep_time': 90, 'calories': 480, 'difficulty': 'Hard', 'diet_type': 'Non-Veg'},
        {'name': 'Masala Dosa', 'ingredients': {'main': ['2 cups rice', '1 cup urad dal', '4 potatoes'], 'vegetables': ['2 onions', '2 green chilies'], 'spices': ['Mustard seeds', 'Turmeric', 'Curry leaves'], 'aromatics': ['Fresh ginger'], 'seasonings': ['Oil', 'Salt']}, 'prep_time': 30, 'calories': 320, 'difficulty': 'Medium', 'diet_type': 'Vegan'},
        {'name': 'Rajma Curry', 'ingredients': {'main': ['400g kidney beans'], 'vegetables': ['2 onions', '3 tomatoes', '4 garlic cloves'], 'spices': ['Cumin powder', 'Coriander powder', 'Garam masala', 'Red chili powder'], 'aromatics': ['Ginger-garlic paste'], 'seasonings': ['Oil', 'Salt']}, 'prep_time': 45, 'calories': 280, 'difficulty': 'Medium', 'diet_type': 'Vegan'},
        {'name': 'Tandoori Chicken', 'ingredients': {'main': ['1 whole chicken'], 'dairy': ['200g yogurt'], 'spices': ['Tandoori masala', 'Red chili powder', 'Turmeric', 'Garam masala'], 'aromatics': ['Ginger-garlic paste', 'Lemon juice'], 'seasonings': ['Oil', 'Salt']}, 'prep_time': 60, 'calories': 380, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Chole Bhature', 'ingredients': {'main': ['400g chickpeas', '2 cups flour'], 'dairy': ['Yogurt'], 'vegetables': ['2 onions', '3 tomatoes'], 'spices': ['Chole masala', 'Cumin', 'Coriander', 'Amchur'], 'aromatics': ['Ginger-garlic paste'], 'seasonings': ['Oil', 'Salt']}, 'prep_time': 60, 'calories': 420, 'difficulty': 'Medium', 'diet_type': 'Vegetarian'},
        {'name': 'Fish Curry', 'ingredients': {'main': ['600g fish fillets'], 'vegetables': ['2 onions', '3 tomatoes', '1 coconut'], 'spices': ['Turmeric', 'Red chili powder', 'Coriander powder', 'Mustard seeds'], 'aromatics': ['Fresh ginger', 'Curry leaves'], 'seasonings': ['Coconut oil', 'Salt']}, 'prep_time': 35, 'calories': 320, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Aloo Gobi', 'ingredients': {'main': ['4 potatoes', '1 cauliflower'], 'vegetables': ['2 onions', '2 tomatoes'], 'spices': ['Turmeric', 'Cumin seeds', 'Coriander powder', 'Garam masala'], 'aromatics': ['Fresh ginger', 'Green chilies'], 'seasonings': ['Oil', 'Salt']}, 'prep_time': 30, 'calories': 180, 'difficulty': 'Easy', 'diet_type': 'Vegan'},
        {'name': 'Samosas', 'ingredients': {'main': ['2 cups flour', '4 potatoes'], 'vegetables': ['1 onion', '2 green chilies'], 'spices': ['Cumin seeds', 'Coriander seeds', 'Garam masala', 'Turmeric'], 'aromatics': ['Fresh ginger'], 'seasonings': ['Oil', 'Salt']}, 'prep_time': 45, 'calories': 220, 'difficulty': 'Medium', 'diet_type': 'Vegan'},
        {'name': 'Paneer Tikka', 'ingredients': {'main': ['400g paneer'], 'dairy': ['200g yogurt'], 'vegetables': ['2 bell peppers', '2 onions'], 'spices': ['Tandoori masala', 'Red chili powder', 'Turmeric'], 'aromatics': ['Ginger-garlic paste'], 'seasonings': ['Oil', 'Salt']}, 'prep_time': 30, 'calories': 280, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Lamb Curry', 'ingredients': {'main': ['800g lamb'], 'dairy': ['100g yogurt'], 'vegetables': ['3 onions', '4 tomatoes'], 'spices': ['Garam masala', 'Red chili powder', 'Turmeric', 'Coriander powder'], 'aromatics': ['Ginger-garlic paste'], 'seasonings': ['Ghee', 'Salt']}, 'prep_time': 90, 'calories': 450, 'difficulty': 'Hard', 'diet_type': 'Non-Veg'},
        {'name': 'Idli Sambar', 'ingredients': {'main': ['2 cups rice', '1 cup urad dal', '200g toor dal'], 'vegetables': ['2 tomatoes', '1 onion', 'Drumsticks', 'Okra'], 'spices': ['Sambar powder', 'Turmeric', 'Mustard seeds', 'Curry leaves'], 'aromatics': ['Tamarind'], 'seasonings': ['Oil', 'Salt']}, 'prep_time': 40, 'calories': 250, 'difficulty': 'Medium', 'diet_type': 'Vegan'},
        {'name': 'Rogan Josh', 'ingredients': {'main': ['800g lamb'], 'dairy': ['200g yogurt'], 'vegetables': ['3 onions'], 'spices': ['Kashmiri red chili', 'Fennel powder', 'Ginger powder', 'Garam masala'], 'aromatics': ['Garlic'], 'seasonings': ['Ghee', 'Salt']}, 'prep_time': 120, 'calories': 420, 'difficulty': 'Hard', 'diet_type': 'Non-Veg'},
        {'name': 'Pav Bhaji', 'ingredients': {'main': ['8 pav buns'], 'vegetables': ['4 potatoes', '2 onions', '3 tomatoes', '1 bell pepper', '200g cauliflower'], 'spices': ['Pav bhaji masala', 'Red chili powder', 'Turmeric'], 'aromatics': ['Ginger-garlic paste'], 'seasonings': ['Butter', 'Salt']}, 'prep_time': 40, 'calories': 350, 'difficulty': 'Medium', 'diet_type': 'Vegetarian'},
        {'name': 'Chicken Tikka Masala', 'ingredients': {'main': ['600g chicken breast'], 'dairy': ['200ml heavy cream', '200g yogurt'], 'vegetables': ['2 onions', '400g tomatoes'], 'spices': ['Garam masala', 'Paprika', 'Cumin', 'Coriander'], 'aromatics': ['Ginger-garlic paste'], 'seasonings': ['Ghee', 'Salt']}, 'prep_time': 50, 'calories': 480, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Vegetable Biryani', 'ingredients': {'main': ['400g basmati rice'], 'vegetables': ['2 potatoes', '200g cauliflower', '200g carrots', '200g beans', '2 onions'], 'spices': ['Biryani masala', 'Saffron', 'Bay leaves'], 'aromatics': ['Mint', 'Coriander'], 'seasonings': ['Ghee', 'Salt']}, 'prep_time': 60, 'calories': 320, 'difficulty': 'Medium', 'diet_type': 'Vegan'},
        {'name': 'Gulab Jamun', 'ingredients': {'main': ['200g milk powder', '50g flour'], 'dairy': ['Milk', 'Ghee'], 'seasonings': ['Sugar', 'Cardamom', 'Rose water'], 'others': ['Oil for frying']}, 'prep_time': 45, 'calories': 280, 'difficulty': 'Medium', 'diet_type': 'Vegetarian'},
        {'name': 'Malai Kofta', 'ingredients': {'main': ['400g paneer', '4 potatoes'], 'dairy': ['200ml heavy cream'], 'vegetables': ['2 onions', '3 tomatoes'], 'spices': ['Garam masala', 'Red chili powder', 'Turmeric'], 'aromatics': ['Ginger-garlic paste'], 'seasonings': ['Ghee', 'Salt']}, 'prep_time': 50, 'calories': 380, 'difficulty': 'Hard', 'diet_type': 'Vegetarian'}
    ]

    # Add Chinese recipes
    for i, recipe_data in enumerate(chinese_recipes):
        recipe = create_recipe(i + 21, recipe_data, 'Chinese')
        all_recipes.append(recipe)

    # Add American recipes
    for i, recipe_data in enumerate(american_recipes):
        recipe = create_recipe(i + 41, recipe_data, 'American')
        all_recipes.append(recipe)

    # Add Indian recipes
    for i, recipe_data in enumerate(indian_recipes):
        recipe = create_recipe(i + 61, recipe_data, 'Indian')
        all_recipes.append(recipe)

    return all_recipes

def create_recipe(recipe_id, recipe_data, cuisine):
    """Create a properly formatted recipe"""
    return {
        'id': recipe_id,
        'name': recipe_data['name'],
        'ingredients': recipe_data['ingredients'],
        'instructions': generate_instructions(recipe_data['name']),
        'prep_time': recipe_data['prep_time'],
        'servings': 2 + (recipe_id % 4),
        'calories': recipe_data['calories'],
        'cuisine': cuisine,
        'diet_type': recipe_data['diet_type'],
        'difficulty': recipe_data['difficulty'],
        'tips': f"Pro tip: Use fresh, high-quality ingredients for the best {recipe_data['name']}."
    }

def generate_instructions(recipe_name):
    """Generate basic instructions for recipes"""
    instructions_map = {
        'Spaghetti Carbonara': [
            'Boil salted water and cook pasta until al dente',
            'Cook pancetta until crispy in large skillet',
            'Whisk eggs with cheese and seasonings',
            'Combine hot pasta with pancetta off heat',
            'Add egg mixture quickly while tossing',
            'Serve immediately with extra cheese'
        ],
        'Margherita Pizza': [
            'Preheat oven to 475°F',
            'Roll out pizza dough',
            'Add tomato sauce and seasonings',
            'Top with mozzarella and basil',
            'Bake for 12-15 minutes until golden',
            'Serve hot with fresh basil'
        ],
        'Lasagna Bolognese': [
            'Prepare meat sauce with ground beef',
            'Cook lasagna sheets until al dente',
            'Layer pasta, meat sauce, and cheese',
            'Repeat layers ending with cheese',
            'Bake covered for 45 minutes',
            'Rest 10 minutes before serving'
        ]
    }

    base_name = recipe_name.split()[0] + ' ' + recipe_name.split()[1] if len(recipe_name.split()) > 1 else recipe_name
    return instructions_map.get(base_name, [
        'Prepare all ingredients as specified',
        'Follow traditional cooking methods',
        'Cook until properly done',
        'Season to taste',
        'Serve hot and enjoy'
    ])

# Generate all recipes
recipes = generate_all_recipes()

user_meal_plans = {}

# Common ingredients for image recognition simulation
COMMON_INGREDIENTS = [
    'tomatoes', 'onion', 'garlic', 'ginger', 'chicken', 'beef', 'pork', 'fish', 'eggs', 'cheese',
    'rice', 'pasta', 'bread', 'potatoes', 'carrots', 'bell peppers', 'spinach', 'broccoli',
    'mushrooms', 'avocado', 'lemon', 'lime', 'basil', 'cilantro', 'parsley', 'soy sauce',
    'olive oil', 'butter', 'cream', 'milk', 'flour', 'sugar', 'salt', 'black pepper'
]

def calculate_enhanced_recipe_match(user_ingredients, recipe_ingredients):
    """Calculate match percentage and missing ingredients for a recipe"""
    user_ingredients_lower = [ing.lower().strip() for ing in user_ingredients]

    # Handle both old format (list) and new format (dict with categories)
    if isinstance(recipe_ingredients, dict):
        # Flatten categorized ingredients
        all_recipe_ingredients = []
        for category, items in recipe_ingredients.items():
            all_recipe_ingredients.extend(items)
        recipe_ingredients_lower = [ing.lower().strip() for ing in all_recipe_ingredients]
    else:
        recipe_ingredients_lower = [ing.lower().strip() for ing in recipe_ingredients]

    matched_ingredients = []
    missing_ingredients = []

    for recipe_ing in recipe_ingredients_lower:
        found = False
        for user_ing in user_ingredients_lower:
            # More flexible matching - check if any word matches
            recipe_words = recipe_ing.split()
            user_words = user_ing.split()

            for recipe_word in recipe_words:
                for user_word in user_words:
                    if (len(recipe_word) > 3 and len(user_word) > 3 and
                        (recipe_word in user_word or user_word in recipe_word)):
                        matched_ingredients.append(recipe_ing)
                        found = True
                        break
                if found:
                    break
            if found:
                break

        if not found:
            missing_ingredients.append(recipe_ing)

    match_percentage = (len(matched_ingredients) / len(recipe_ingredients_lower)) * 100 if recipe_ingredients_lower else 0

    return {
        'match_percentage': round(match_percentage, 1),
        'matched_ingredients': matched_ingredients,
        'missing_ingredients': missing_ingredients,
        'total_ingredients': len(recipe_ingredients_lower)
    }

def simulate_ingredient_detection(image_path):
    """Enhanced ingredient detection using existing NLP and ML integration"""
    try:
        # Use the existing ML-powered detection with NLP standardization
        analysis_result = analyze_ingredient_image(image_path, use_advanced_ml=True)

        # Extract standardized ingredient names using existing NLP
        detected_ingredients = []
        for item in analysis_result['detected_ingredients']:
            # Use the existing NLP standardizer
            from nlp_processor import create_ingredient_standardizer
            standardizer = create_ingredient_standardizer()
            standardized = standardizer.standardize_ingredient(item['name'])
            detected_ingredients.append(standardized['standardized_name'])

        print(f"NLP-enhanced ML detection successful: {detected_ingredients}")
        return detected_ingredients
    except Exception as e:
        print(f"ML detection failed, using NLP-enhanced fallback: {e}")
        # Use NLP-enhanced fallback
        return nlp_enhanced_ingredient_simulation(image_path)

def nlp_enhanced_ingredient_simulation(image_path):
    """NLP-enhanced ingredient simulation using existing standardization"""
    import random
    import os

    # Get filename for analysis
    filename = os.path.basename(image_path).lower()

    # Use existing NLP processor for ingredient standardization
    try:
        from nlp_processor import create_ingredient_standardizer
        from ml_config import NLPConfig

        standardizer = create_ingredient_standardizer()
        config = NLPConfig()

        # Get all known ingredients from the NLP system
        all_known_ingredients = list(standardizer.synonym_to_standard.keys())

        # Try to detect ingredients from filename using NLP fuzzy matching
        detected_ingredients = []

        # Split filename into potential ingredient words
        filename_words = filename.replace('_', ' ').replace('-', ' ').split()

        for word in filename_words:
            # Use NLP fuzzy matching to find similar ingredients
            similar = standardizer.find_similar_ingredients(word, limit=3)
            for ingredient, confidence in similar:
                if confidence > 0.7:  # High confidence matches
                    detected_ingredients.append(ingredient)

        # If no matches from filename, use realistic cooking scenarios with NLP standardization
        if not detected_ingredients:
            cooking_scenarios = [
                ['tomato', 'basil', 'garlic', 'onion', 'olive oil', 'mozzarella'],
                ['chicken breast', 'bell pepper', 'onion', 'garlic', 'soy sauce'],
                ['eggs', 'bacon', 'bread', 'butter', 'cheese'],
                ['lettuce', 'tomato', 'cucumber', 'carrot', 'olive oil'],
                ['ground beef', 'onion', 'bell pepper', 'tomato', 'cheese'],
                ['carrot', 'onion', 'garlic', 'broccoli', 'chicken breast']
            ]

            raw_ingredients = random.choice(cooking_scenarios)

            # Standardize each ingredient using NLP
            for ingredient in raw_ingredients:
                standardized = standardizer.standardize_ingredient(ingredient)
                detected_ingredients.append(standardized['standardized_name'])

        # Remove duplicates and limit to 6-8 ingredients
        detected_ingredients = list(set(detected_ingredients))[:8]

        print(f"NLP-enhanced simulation detected: {detected_ingredients}")
        return detected_ingredients

    except Exception as e:
        print(f"NLP enhancement failed, using basic simulation: {e}")
        # Fallback to basic simulation
        return ['chicken', 'tomato', 'onion', 'garlic', 'basil', 'olive oil']

def get_nlp_enhanced_compatibility_score(user_ingredients, recipe, standardizer):
    """Calculate compatibility score using NLP-enhanced ingredient matching"""
    try:
        # Get all recipe ingredients
        recipe_ingredients = []
        if isinstance(recipe.get('ingredients'), dict):
            for category, ingredients in recipe['ingredients'].items():
                recipe_ingredients.extend(ingredients)
        elif isinstance(recipe.get('ingredients'), list):
            recipe_ingredients = recipe['ingredients']

        # Standardize recipe ingredients
        standardized_recipe_ingredients = []
        for ingredient in recipe_ingredients:
            standardized = standardizer.standardize_ingredient(ingredient)
            standardized_recipe_ingredients.append(standardized['standardized_name'])

        # Calculate matches using standardized names
        matches = 0
        total_recipe_ingredients = len(standardized_recipe_ingredients)

        for user_ingredient in user_ingredients:
            # Direct match
            if user_ingredient in standardized_recipe_ingredients:
                matches += 1
            else:
                # Fuzzy match using NLP
                similar = standardizer.find_similar_ingredients(user_ingredient, limit=3)
                for similar_ingredient, confidence in similar:
                    if similar_ingredient in standardized_recipe_ingredients and confidence > 0.8:
                        matches += confidence  # Weighted match
                        break

        # Calculate compatibility score (0-100)
        if total_recipe_ingredients > 0:
            base_score = (matches / total_recipe_ingredients) * 100

            # Bonus for having many user ingredients
            user_ingredient_bonus = min(len(user_ingredients) * 5, 20)

            # Penalty for missing too many ingredients
            missing_ingredients = total_recipe_ingredients - matches
            missing_penalty = missing_ingredients * 2

            final_score = max(0, min(100, base_score + user_ingredient_bonus - missing_penalty))
            return final_score

        return 0

    except Exception as e:
        print(f"NLP compatibility scoring failed: {e}")
        # Fallback to basic scoring
        return get_recipe_compatibility_score(user_ingredients, recipe)

def get_complementary_ingredients(detected, ingredient_categories):
    """Get complementary ingredients based on what was already detected"""
    import random

    complementary = []
    detected_lower = [ing.lower() for ing in detected]

    # If we have vegetables, add some basics
    if any('tomato' in ing or 'onion' in ing for ing in detected_lower):
        complementary.extend(['garlic', 'olive oil', 'salt', 'pepper'])

    # If we have protein, add cooking essentials
    if any(protein in ing for ing in detected_lower for protein in ['chicken', 'beef', 'fish', 'eggs']):
        complementary.extend(['onion', 'garlic', 'salt', 'pepper'])

    # If we have pasta/rice, add complementary items
    if any(grain in ing for ing in detected_lower for grain in ['pasta', 'rice']):
        complementary.extend(['garlic', 'onion', 'olive oil'])

    # Add some random ingredients from different categories
    all_ingredients = []
    for ingredients in ingredient_categories.values():
        all_ingredients.extend(ingredients)

    # Filter out already detected ingredients
    available = [ing for ing in all_ingredients if ing.lower() not in detected_lower]

    # Add 2-3 random complementary ingredients
    if available:
        complementary.extend(random.sample(available, min(3, len(available))))

    return complementary[:4]  # Limit complementary ingredients

# Helper functions
def hash_password(password):
    return hashlib.sha256(password.encode()).hexdigest()

def get_current_user():
    return session.get('user_id')

def is_logged_in():
    return 'user_id' in session

# Authentication routes
@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form['username']
        email = request.form['email']
        password = request.form['password']
        full_name = request.form['full_name']

        # Check if user already exists
        if any(user['email'] == email for user in users.values()):
            flash('Email already registered', 'error')
            return render_template('auth.html', mode='register')

        # Create new user
        user_id = str(uuid.uuid4())
        users[user_id] = {
            'id': user_id,
            'username': username,
            'email': email,
            'password': hash_password(password),
            'full_name': full_name,
            'created_at': datetime.now().strftime('%Y-%m-%d'),
            'avatar': None
        }
        user_favorites[user_id] = []
        user_profiles[user_id] = {
            'dietary_preferences': [],
            'favorite_cuisines': [],
            'cooking_skill': 'Beginner',
            'allergies': []
        }

        # Save data to files
        save_data()

        session['user_id'] = user_id
        session.permanent = True  # Make session persistent
        flash('Registration successful! Welcome to MealMind!', 'success')
        return redirect(url_for('index'))

    return render_template('auth.html', mode='register')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        email = request.form['email']
        password = request.form['password']
        remember_me = request.form.get('remember_me')

        # Find user by email
        user = None
        for u in users.values():
            if u['email'] == email and u['password'] == hash_password(password):
                user = u
                break

        if user:
            session['user_id'] = user['id']
            if remember_me:
                session.permanent = True  # Make session persistent for 30 days
            else:
                session.permanent = False  # Session expires when browser closes
            flash(f'Welcome back, {user["full_name"]}!', 'success')
            return redirect(url_for('index'))
        else:
            flash('Invalid email or password', 'error')

    return render_template('auth.html', mode='login')

@app.route('/logout')
def logout():
    session.pop('user_id', None)
    flash('You have been logged out', 'info')
    return redirect(url_for('index'))

@app.route('/profile')
def profile():
    if not is_logged_in():
        flash('Please log in to view your profile', 'error')
        return redirect(url_for('login'))

    user_id = get_current_user()

    # Check if user exists
    if user_id not in users:
        flash('User not found. Please log in again.', 'error')
        session.pop('user_id', None)
        return redirect(url_for('login'))

    user = users[user_id]

    # Initialize profile data if it doesn't exist
    if user_id not in user_profiles:
        user_profiles[user_id] = {
            'dietary_preferences': [],
            'allergies': [],
            'cooking_experience': 'Beginner',
            'preferred_cuisines': [],
            'meal_planning_days': 7
        }

    profile_data = user_profiles[user_id]

    # Get user's favorite recipes
    favorite_recipes = []
    if user_id in user_favorites:
        for fav in user_favorites[user_id]:
            recipe = next((r for r in recipes if r['id'] == fav['recipe_id']), None)
            if recipe:
                favorite_recipes.append({
                    **recipe,
                    'added_date': fav.get('added_date', 'Unknown')
                })

    return render_template('profile.html', user=user, profile=profile_data, favorite_recipes=favorite_recipes)

@app.route('/update-profile', methods=['POST'])
def update_profile():
    if not is_logged_in():
        return jsonify({'error': 'Not logged in'}), 401

    user_id = get_current_user()

    # Check if user exists
    if user_id not in users:
        return jsonify({'error': 'User not found'}), 404

    data = request.get_json()

    if 'user_info' in data:
        users[user_id].update(data['user_info'])

    if 'profile_info' in data:
        # Initialize profile if it doesn't exist
        if user_id not in user_profiles:
            user_profiles[user_id] = {
                'dietary_preferences': [],
                'allergies': [],
                'cooking_experience': 'Beginner',
                'preferred_cuisines': [],
                'meal_planning_days': 7
            }
        user_profiles[user_id].update(data['profile_info'])

    # Save data to files
    save_data()

    return jsonify({'success': True})

@app.route('/')
def index():
    """Main landing page"""
    user = users.get(get_current_user()) if is_logged_in() else None
    return render_template('index.html', user=user)

@app.route('/smart-recipes')
def smart_recipes():
    """Smart recipes page with ingredient finder"""
    cuisines = ['Italian', 'Chinese', 'American', 'Indian']
    diet_types = list(set([recipe['diet_type'] for recipe in recipes]))
    user = users.get(get_current_user()) if is_logged_in() else None
    return render_template('smart_recipes.html', cuisines=cuisines, diet_types=diet_types, user=user, user_favorites=user_favorites)

@app.route('/api/search-recipes-by-ingredients', methods=['POST'])
def api_search_recipes_by_ingredients():
    """API endpoint to search recipes by ingredients using Spoonacular"""
    try:
        data = request.get_json()
        ingredients = data.get('ingredients', [])

        print(f"API called with ingredients: {ingredients}")

        if not ingredients:
            print("No ingredients provided")
            return jsonify({'error': 'No ingredients provided'}), 400

        # Search using Spoonacular API
        print("Calling Spoonacular API...")
        spoon_recipes = search_recipes_by_ingredients(ingredients, number=12)
        print(f"Spoonacular returned {len(spoon_recipes)} recipes")

        # If Spoonacular returns results, use them
        if spoon_recipes:
            print("Returning Spoonacular results")
            return jsonify({
                'success': True,
                'recipes': spoon_recipes,
                'source': 'spoonacular',
                'total': len(spoon_recipes)
            })

        # Fallback to local recipes if Spoonacular fails
        print("Falling back to local recipes...")
        local_matches = find_local_recipe_matches(ingredients)
        print(f"Local search returned {len(local_matches)} recipes")
        return jsonify({
            'success': True,
            'recipes': local_matches,
            'source': 'local',
            'total': len(local_matches)
        })

    except Exception as e:
        print(f"Error in recipe search: {e}")
        return jsonify({'error': 'Failed to search recipes'}), 500

@app.route('/api/toggle-favorite', methods=['POST'])
def api_toggle_favorite():
    """API endpoint to toggle recipe favorite status"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'message': 'Please log in first'}), 401

    try:
        data = request.get_json()
        recipe_id = data.get('recipe_id')
        user_id = session['user_id']

        if not recipe_id:
            return jsonify({'success': False, 'message': 'Recipe ID required'}), 400

        # Initialize user favorites if not exists
        if user_id not in user_favorites:
            user_favorites[user_id] = []

        # Check if already favorited
        user_favs = user_favorites[user_id]
        existing_favorite = next((fav for fav in user_favs if fav['recipe_id'] == recipe_id), None)

        if existing_favorite:
            # Remove from favorites
            user_favorites[user_id] = [fav for fav in user_favs if fav['recipe_id'] != recipe_id]
            is_favorite = False
            message = 'Removed from favorites'
        else:
            # Add to favorites
            user_favorites[user_id].append({
                'recipe_id': recipe_id,
                'added_date': datetime.now().isoformat()
            })
            is_favorite = True
            message = 'Added to favorites'

        # Save data to file
        save_data()

        return jsonify({
            'success': True,
            'is_favorite': is_favorite,
            'message': message
        })

    except Exception as e:
        print(f"Error toggling favorite: {e}")
        return jsonify({'success': False, 'message': 'Internal server error'}), 500

@app.route('/api/check-favorite/<int:recipe_id>')
def api_check_favorite(recipe_id):
    """API endpoint to check if recipe is favorited by current user"""
    if 'user_id' not in session:
        return jsonify({'is_favorite': False})

    try:
        user_id = session['user_id']

        # Check if user has any favorites
        if user_id not in user_favorites:
            return jsonify({'is_favorite': False})

        # Check if this recipe is in user's favorites
        user_favs = user_favorites[user_id]
        is_favorite = any(fav['recipe_id'] == recipe_id for fav in user_favs)

        return jsonify({'is_favorite': is_favorite})

    except Exception as e:
        print(f"Error checking favorite: {e}")
        return jsonify({'is_favorite': False})



def find_local_recipe_matches(ingredients):
    """Fallback function to search local recipe database"""
    matches = []
    ingredients_lower = [ing.lower().strip() for ing in ingredients]

    print(f"Searching local recipes for ingredients: {ingredients_lower}")

    for recipe in recipes:
        # Check all ingredient categories
        all_recipe_ingredients = []
        for category in recipe.get('ingredients', {}).values():
            if isinstance(category, list):
                all_recipe_ingredients.extend([ing.lower() for ing in category])

        # Calculate match score with more flexible matching
        matched_ingredients = []
        for user_ingredient in ingredients_lower:
            for recipe_ingredient in all_recipe_ingredients:
                # More flexible matching - check if any word matches
                user_words = user_ingredient.split()
                recipe_words = recipe_ingredient.split()

                for user_word in user_words:
                    for recipe_word in recipe_words:
                        if (len(user_word) > 2 and user_word in recipe_word) or \
                           (len(recipe_word) > 2 and recipe_word in user_word):
                            if user_ingredient not in matched_ingredients:
                                matched_ingredients.append(user_ingredient)
                            break
                    if user_ingredient in matched_ingredients:
                        break
                if user_ingredient in matched_ingredients:
                    break

        # Include recipes with at least one matching ingredient
        if matched_ingredients:
            match_percentage = round((len(matched_ingredients) / len(ingredients_lower)) * 100)
            matches.append({
                'id': recipe['id'],
                'name': recipe['name'],
                'image': '',  # Local recipes don't have images
                'used_ingredients': matched_ingredients,
                'missed_ingredients': [ing for ing in ingredients_lower if ing not in matched_ingredients],
                'used_ingredient_count': len(matched_ingredients),
                'missed_ingredient_count': len(ingredients_lower) - len(matched_ingredients),
                'match_percentage': match_percentage,
                'cuisine': recipe.get('cuisine', 'Unknown'),
                'prep_time': recipe.get('prep_time', 30),
                'calories': recipe.get('calories', 300),
                'difficulty': recipe.get('difficulty', 'Medium'),
                'likes': 0
            })

    print(f"Found {len(matches)} local recipe matches")

    # Sort by match percentage
    matches.sort(key=lambda x: x['match_percentage'], reverse=True)
    return matches[:12]

@app.route('/api/recipe/<int:recipe_id>')
def api_get_recipe_details(recipe_id):
    """Get detailed recipe information"""
    try:
        # First try to get from Spoonacular
        spoon_recipe = get_recipe_details(recipe_id)
        if spoon_recipe:
            return jsonify({
                'success': True,
                'recipe': spoon_recipe,
                'source': 'spoonacular'
            })

        # Fallback to local recipe
        local_recipe = next((r for r in recipes if r['id'] == recipe_id), None)
        if local_recipe:
            return jsonify({
                'success': True,
                'recipe': local_recipe,
                'source': 'local'
            })

        return jsonify({'error': 'Recipe not found'}), 404

    except Exception as e:
        print(f"Error getting recipe details: {e}")
        return jsonify({'error': 'Failed to get recipe details'}), 500

@app.route('/recipe-results')
def recipe_results():
    """Display recipe search results page"""
    user = users.get(get_current_user()) if is_logged_in() else None
    return render_template('recipe_results.html', user=user)

@app.route('/browse')
def browse_recipes():
    """Browse all recipes page"""
    cuisines = ['Italian', 'Chinese', 'American', 'Indian']
    diet_types = list(set([recipe['diet_type'] for recipe in recipes]))
    user = users.get(get_current_user()) if is_logged_in() else None

    # Sort recipes to show Indian recipes first
    sorted_recipes = sorted(recipes, key=lambda x: (x['cuisine'] != 'Indian', x['name']))

    return render_template('browse.html', recipes=sorted_recipes, cuisines=cuisines, diet_types=diet_types, user=user, user_favorites=user_favorites)

@app.route('/favorites')
def favorites():
    """User favorites page"""
    if not is_logged_in():
        flash('Please log in to view your favorites', 'error')
        return redirect(url_for('login'))

    user_id = get_current_user()
    user = users.get(user_id)

    # Get user's favorite recipes
    favorite_recipes = []
    if user_id in user_favorites:
        for fav in user_favorites[user_id]:
            recipe = next((r for r in recipes if r['id'] == fav['recipe_id']), None)
            if recipe:
                favorite_recipes.append({
                    **recipe,
                    'added_date': fav.get('added_date', 'Unknown')
                })

    return render_template('favorites.html', favorite_recipes=favorite_recipes, user=user)

@app.route('/recipe/<int:recipe_id>')
def recipe_detail(recipe_id):
    recipe = next((r for r in recipes if r['id'] == recipe_id), None)
    if recipe:
        return render_template('recipe.html', recipe=recipe)
    flash('Recipe not found', 'error')
    return redirect(url_for('index'))

@app.route('/meal-plan')
def meal_plan():
    user = users.get(get_current_user()) if is_logged_in() else None
    user_id = get_current_user() if is_logged_in() else None
    meal_plans = user_meal_plans.get(user_id, []) if user_id else []
    return render_template('meal_plan.html', meal_plans=meal_plans, recipes=recipes, user=user)

@app.route('/add-to-meal-plan', methods=['POST'])
def add_to_meal_plan():
    if not is_logged_in():
        flash('Please log in to add recipes to your meal plan', 'error')
        return redirect(url_for('login'))

    try:
        user_id = get_current_user()
        recipe_id = int(request.form['recipe_id'])
        date = request.form['date']
        meal_type = request.form['meal_type']

        # Initialize user meal plans if not exists
        if user_id not in user_meal_plans:
            user_meal_plans[user_id] = []

        recipe = next((r for r in recipes if r['id'] == recipe_id), None)
        if recipe:
            # Check if meal already exists for this date and meal type
            existing_meal = next((m for m in user_meal_plans[user_id] if m['date'] == date and m['meal_type'] == meal_type), None)
            if existing_meal:
                flash(f'You already have a {meal_type} planned for {date}. Consider updating it instead.', 'warning')
            else:
                user_meal_plans[user_id].append({
                    'id': len(user_meal_plans[user_id]) + 1,
                    'recipe': recipe,
                    'date': date,
                    'meal_type': meal_type
                })
                flash(f'{recipe["name"]} added to your {meal_type} plan for {date}!', 'success')
        else:
            flash('Recipe not found', 'error')
    except (ValueError, KeyError) as e:
        flash('Invalid data provided', 'error')

    return redirect(url_for('meal_plan'))

@app.route('/api/recipes')
def api_recipes():
    return jsonify(recipes)

@app.route('/find-recipes', methods=['GET', 'POST'])
def find_recipes():
    if request.method == 'POST':
        user_ingredients = []

        # Get the action type (find or generate)
        recipe_action = request.form.get('recipe_action', 'find')
        print(f"Recipe action: {recipe_action}")

        # Handle text input
        if 'ingredients_text' in request.form and request.form['ingredients_text']:
            text_ingredients = request.form['ingredients_text'].split(',')
            user_ingredients.extend([ing.strip() for ing in text_ingredients if ing.strip()])

        # Handle image upload
        if 'ingredients_image' in request.files:
            file = request.files['ingredients_image']
            if file and file.filename != '' and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                file.save(filepath)

                # Simulate ingredient detection from image
                detected_ingredients = simulate_ingredient_detection(filepath)
                user_ingredients.extend(detected_ingredients)

                flash(f'Detected ingredients from image: {", ".join(detected_ingredients)}', 'info')

        if not user_ingredients:
            flash('Please provide ingredients either by text or image upload', 'error')
            return redirect(url_for('index'))

        # Get filters
        cuisine_filter = request.form.get('cuisine_preference', '')
        diet_filter = request.form.get('diet_preference', '')
        max_time = request.form.get('cooking_time', '')

        # Find matching recipes
        matching_recipes = []
        for recipe in recipes:
            # Apply filters
            if cuisine_filter and recipe['cuisine'] != cuisine_filter:
                continue
            if diet_filter and recipe['diet_type'] != diet_filter:
                continue
            if max_time:
                recipe_time = recipe.get('prep_time', 30)
                if isinstance(recipe_time, str):
                    recipe_time = int(recipe_time.split()[0]) if recipe_time.split()[0].isdigit() else 30

                if max_time == 'quick' and recipe_time > 30:
                    continue
                elif max_time == 'medium' and (recipe_time <= 30 or recipe_time > 60):
                    continue
                elif max_time == 'long' and recipe_time <= 60:
                    continue

            # Calculate match
            match_data = calculate_enhanced_recipe_match(user_ingredients, recipe['ingredients'])
            if match_data['match_percentage'] > 0:  # Only include recipes with some match
                recipe_with_match = recipe.copy()
                recipe_with_match.update(match_data)
                matching_recipes.append(recipe_with_match)

        # Handle different actions
        if recipe_action == 'generate':
            # Generate AI recipes with NLP enhancement
            print(f"Generating AI recipes for ingredients: {user_ingredients}")

            if not user_ingredients:
                flash('Please provide ingredients to generate recipes', 'error')
                return redirect(url_for('smart_recipes'))

            # Enhance ingredients using NLP standardization
            try:
                from nlp_processor import create_ingredient_standardizer
                standardizer = create_ingredient_standardizer()

                # Standardize and enhance ingredient list
                enhanced_ingredients = []
                for ingredient in user_ingredients:
                    standardized = standardizer.standardize_ingredient(ingredient)
                    enhanced_ingredients.append(standardized['standardized_name'])

                    # Add alternatives for variety
                    for alt in standardized.get('alternatives', [])[:1]:  # Add 1 alternative max
                        if alt not in enhanced_ingredients:
                            enhanced_ingredients.append(alt)

                print(f"NLP-enhanced ingredients: {enhanced_ingredients}")

            except Exception as nlp_error:
                print(f"NLP enhancement failed: {nlp_error}")
                enhanced_ingredients = user_ingredients

            # Initialize AI Recipe Generator
            ai_generator = AIRecipeGenerator()

            # Generate multiple recipe variations using enhanced ingredients
            generated_recipes = []
            for i in range(3):  # Generate 3 different recipes
                try:
                    ai_recipe = ai_generator.generate_ai_recipe(enhanced_ingredients, servings=2+i)
                    generated_recipes.append(ai_recipe)
                    print(f"Generated recipe {i+1}: {ai_recipe['name']}")
                except Exception as e:
                    print(f"Error generating recipe {i+1}: {e}")
                    continue

            if not generated_recipes:
                flash('Failed to generate recipes. Please try again.', 'error')
                return redirect(url_for('smart_recipes'))

            return render_template('recipe_results.html',
                                 recipes=generated_recipes,
                                 user_ingredients=user_ingredients,
                                 total_found=len(generated_recipes),
                                 is_generated=True,
                                 action_type='generate')

        else:
            # Find existing recipes with NLP-enhanced matching
            try:
                from nlp_processor import create_ingredient_standardizer
                standardizer = create_ingredient_standardizer()

                # Standardize user ingredients for better matching
                standardized_user_ingredients = []
                for ingredient in user_ingredients:
                    standardized = standardizer.standardize_ingredient(ingredient)
                    standardized_user_ingredients.append(standardized['standardized_name'])

                    # Also include alternatives for broader matching
                    for alt in standardized.get('alternatives', [])[:2]:
                        if alt not in standardized_user_ingredients:
                            standardized_user_ingredients.append(alt)

                print(f"NLP-standardized ingredients for matching: {standardized_user_ingredients}")

                # Enhanced recipe matching using standardized ingredients
                for recipe in matching_recipes:
                    recipe['compatibility_score'] = get_nlp_enhanced_compatibility_score(
                        standardized_user_ingredients, recipe, standardizer
                    )

            except Exception as nlp_error:
                print(f"NLP enhancement for recipe finding failed: {nlp_error}")
                # Fallback to original logic
                for recipe in matching_recipes:
                    recipe['compatibility_score'] = get_recipe_compatibility_score(user_ingredients, recipe)

            # Sort by compatibility score
            matching_recipes.sort(key=lambda x: x['compatibility_score'], reverse=True)

            return render_template('recipe_results.html',
                                 recipes=matching_recipes,
                                 user_ingredients=user_ingredients,
                                 total_found=len(matching_recipes),
                                 is_generated=False,
                                 action_type='find')

    return redirect(url_for('index'))

@app.route('/search-recipes')
def search_recipes():
    query = request.args.get('q', '').lower()
    cuisine = request.args.get('cuisine', '')
    diet_type = request.args.get('diet_type', '')
    max_time = request.args.get('max_time', '')

    filtered_recipes = []
    for recipe in recipes:
        # Apply filters
        if cuisine and recipe['cuisine'] != cuisine:
            continue
        if diet_type and recipe['diet_type'] != diet_type:
            continue
        if max_time and recipe['prep_time'] > int(max_time):
            continue

        # Apply search query
        if query:
            # Handle both old and new ingredient formats
            ingredients_match = False
            if isinstance(recipe['ingredients'], dict):
                # New categorized format
                for category, items in recipe['ingredients'].items():
                    if any(query in ingredient.lower() for ingredient in items):
                        ingredients_match = True
                        break
            else:
                # Old list format
                ingredients_match = any(query in ingredient.lower() for ingredient in recipe['ingredients'])

            # Check instructions (handle both string and list formats)
            instructions_match = False
            if isinstance(recipe['instructions'], list):
                instructions_match = any(query in instruction.lower() for instruction in recipe['instructions'])
            else:
                instructions_match = query in recipe['instructions'].lower()

            if (query in recipe['name'].lower() or
                ingredients_match or
                instructions_match or
                query in recipe['cuisine'].lower()):
                filtered_recipes.append(recipe)
        else:
            filtered_recipes.append(recipe)

    return jsonify(filtered_recipes)

@app.route('/nutrition-info/<int:recipe_id>')
def nutrition_info(recipe_id):
    recipe = next((r for r in recipes if r['id'] == recipe_id), None)
    if recipe:
        # Enhanced nutrition calculation with more realistic values
        base_calories = recipe.get('calories', 0)
        nutrition = {
            'calories': base_calories,
            'protein': base_calories * 0.25,  # 25% protein
            'carbs': base_calories * 0.45,    # 45% carbs
            'fat': base_calories * 0.30       # 30% fat
        }
        return jsonify(nutrition)
    return jsonify({'error': 'Recipe not found'}), 404

@app.route('/weekly-meal-plan')
def weekly_meal_plan():
    # Generate a week's worth of dates starting from today
    today = datetime.now().date()
    week_dates = [(today + timedelta(days=i)).strftime('%Y-%m-%d') for i in range(7)]
    
    # Organize meal plans by date
    organized_plans = {}
    for date in week_dates:
        organized_plans[date] = {
            'breakfast': [],
            'lunch': [],
            'dinner': []
        }
    
    # Add existing meal plans to the organized structure
    for plan in meal_plans:
        date = plan['date']
        meal_type = plan['meal_type']
        if date in organized_plans:
            organized_plans[date][meal_type].append(plan['recipe'])
    
    return jsonify(organized_plans)

@app.route('/remove-meal-plan', methods=['POST'])
def remove_meal_plan():
    try:
        date = request.form['date']
        meal_type = request.form['meal_type']

        # Find and remove the meal plan
        meal_plans[:] = [m for m in meal_plans if not (m['date'] == date and m['meal_type'] == meal_type)]
        flash(f'{meal_type.title()} removed from {date}', 'success')
    except KeyError:
        flash('Invalid data provided', 'error')

    return redirect(url_for('meal_plan'))



@app.route('/correct-ingredients', methods=['POST'])
def correct_ingredients():
    """Allow users to correct detected ingredients"""
    data = request.get_json()
    corrected_ingredients = data.get('ingredients', [])

    # In a real app, you'd store this correction to improve the AI model
    # For now, we'll just return the corrected ingredients
    return jsonify({
        'success': True,
        'corrected_ingredients': corrected_ingredients,
        'message': 'Ingredients corrected successfully'
    })



@app.errorhandler(404)
def not_found_error(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('500.html'), 500

# NLP-Enhanced Ingredient Identification API
@app.route('/api/identify-ingredients', methods=['POST'])
def api_identify_ingredients():
    """API endpoint to identify ingredients from text using NLP"""
    try:
        data = request.get_json()
        text = data.get('text', '')

        if not text:
            return jsonify({'error': 'No text provided'}), 400

        # Use existing ML model with NLP enhancement
        identified = identify_ingredients(text)

        # Enhance with NLP standardization
        try:
            from nlp_processor import create_ingredient_standardizer
            standardizer = create_ingredient_standardizer()

            enhanced_ingredients = []
            for ingredient in identified:
                # Standardize each identified ingredient
                standardized = standardizer.standardize_ingredient(ingredient['name'])

                enhanced_ingredient = {
                    'name': standardized['standardized_name'],
                    'original_name': ingredient['name'],
                    'category': standardized['category'],
                    'confidence': ingredient.get('confidence', 0.8),
                    'alternatives': standardized.get('alternatives', []),
                    'quantity': standardized.get('quantity'),
                    'unit': standardized.get('unit')
                }
                enhanced_ingredients.append(enhanced_ingredient)

            return jsonify({
                'success': True,
                'ingredients': enhanced_ingredients,
                'count': len(enhanced_ingredients),
                'nlp_enhanced': True
            })

        except Exception as nlp_error:
            print(f"NLP enhancement failed: {nlp_error}")
            # Return original results if NLP fails
            return jsonify({
                'success': True,
                'ingredients': identified,
                'count': len(identified),
                'nlp_enhanced': False
            })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/ingredient-suggestions', methods=['GET'])
def api_ingredient_suggestions():
    """API endpoint to get ingredient suggestions for autocomplete"""
    try:
        partial = request.args.get('q', '')
        # Use enhanced ML suggestions
        suggestions = get_ingredient_suggestions(partial, use_advanced_ml=True)

        return jsonify({
            'success': True,
            'suggestions': suggestions
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/upload-ingredient-image', methods=['POST'])
def api_upload_ingredient_image():
    """API endpoint for ingredient image upload and detection"""
    if 'image' not in request.files:
        return jsonify({'error': 'No image file provided'}), 400

    file = request.files['image']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400

    if file and allowed_file(file.filename):
        try:
            # Save uploaded file
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
            filename = timestamp + filename
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            # Perform enhanced ingredient detection
            detected_ingredients = simulate_ingredient_detection(filepath)

            # Format response with detailed ingredient information
            ingredient_details = []
            for ingredient in detected_ingredients:
                ingredient_details.append({
                    'name': ingredient,
                    'confidence': random.uniform(0.75, 0.95),
                    'category': categorize_ingredient(ingredient),
                    'suggested_quantity': suggest_quantity(ingredient)
                })

            return jsonify({
                'success': True,
                'detected_ingredients': ingredient_details,
                'total_detected': len(ingredient_details),
                'filename': filename,
                'message': f'Successfully detected {len(ingredient_details)} ingredients from your image!'
            })

        except Exception as e:
            print(f"Image analysis error: {e}")
            return jsonify({'error': f'Failed to analyze image: {str(e)}'}), 500

    return jsonify({'error': 'Invalid file type. Please upload JPG, PNG, GIF, or WebP images.'}), 400

def categorize_ingredient(ingredient_name):
    """Categorize an ingredient into food groups"""
    ingredient_lower = ingredient_name.lower()

    if any(veg in ingredient_lower for veg in ['tomato', 'onion', 'garlic', 'carrot', 'pepper', 'broccoli', 'spinach', 'lettuce']):
        return 'Vegetables'
    elif any(protein in ingredient_lower for protein in ['chicken', 'beef', 'fish', 'egg', 'tofu', 'bacon', 'turkey']):
        return 'Proteins'
    elif any(grain in ingredient_lower for grain in ['rice', 'pasta', 'bread', 'quinoa', 'oats']):
        return 'Grains'
    elif any(dairy in ingredient_lower for dairy in ['cheese', 'milk', 'butter', 'yogurt', 'cream']):
        return 'Dairy'
    elif any(herb in ingredient_lower for herb in ['basil', 'oregano', 'thyme', 'parsley', 'cilantro']):
        return 'Herbs & Spices'
    elif any(fruit in ingredient_lower for fruit in ['lemon', 'lime', 'apple', 'banana', 'orange', 'avocado']):
        return 'Fruits'
    else:
        return 'Pantry Items'

def suggest_quantity(ingredient_name):
    """Suggest typical quantity for an ingredient"""
    ingredient_lower = ingredient_name.lower()

    if any(meat in ingredient_lower for meat in ['chicken', 'beef', 'fish', 'turkey']):
        return '1-2 lbs'
    elif any(veg in ingredient_lower for veg in ['tomato', 'onion', 'pepper']):
        return '2-3 pieces'
    elif 'egg' in ingredient_lower:
        return '4-6 pieces'
    elif any(grain in ingredient_lower for grain in ['rice', 'pasta']):
        return '1-2 cups'
    elif any(herb in ingredient_lower for herb in ['basil', 'oregano', 'thyme']):
        return '1-2 tsp'
    else:
        return '1 cup'

@app.route('/api/recipe-recommendations', methods=['POST'])
def api_recipe_recommendations():
    """API endpoint for ML-powered recipe recommendations"""
    try:
        data = request.get_json()
        ingredients = data.get('ingredients', [])
        user_preferences = data.get('preferences', {})

        if not ingredients:
            return jsonify({'error': 'No ingredients provided'}), 400

        # Get ML-powered recommendations
        recommendations = get_recipe_recommendations_for_ingredients(
            ingredients,
            recipes,
            user_preferences,
            use_advanced_ml=True
        )

        return jsonify({
            'success': True,
            'recommendations': recommendations,
            'count': len(recommendations)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5002)
