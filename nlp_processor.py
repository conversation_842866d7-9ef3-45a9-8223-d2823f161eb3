"""
Natural Language Processing Module for Ingredient Standardization
Handles ingredient name normalization, fuzzy matching, and text processing
"""

import re
import logging
from typing import List, Dict, Tuple, Optional, Set
from collections import defaultdict
import json
from pathlib import Path

from ml_config import NLPConfig

# Set up logging
logger = logging.getLogger(__name__)

class IngredientStandardizer:
    """Standardizes ingredient names using NLP techniques"""
    
    def __init__(self):
        self.config = NLPConfig()
        self.nlp = None
        self.fuzzy_matcher = None
        
        # Initialize components
        self._initialize_nlp()
        self._initialize_fuzzy_matcher()
        
        # Load ingredient database
        self.ingredient_synonyms = self.config.INGREDIENT_SYNONYMS
        self.ingredient_categories = self.config.INGREDIENT_CATEGORIES
        
        # Build reverse lookup for synonyms
        self.synonym_to_standard = self._build_synonym_lookup()
        
        # Common units and measurements
        self.units = {
            'weight': ['g', 'gram', 'grams', 'kg', 'kilogram', 'kilograms', 'lb', 'lbs', 'pound', 'pounds', 'oz', 'ounce', 'ounces'],
            'volume': ['ml', 'milliliter', 'milliliters', 'l', 'liter', 'liters', 'cup', 'cups', 'tbsp', 'tablespoon', 'tablespoons', 'tsp', 'teaspoon', 'teaspoons', 'pint', 'pints', 'quart', 'quarts', 'gallon', 'gallons'],
            'count': ['piece', 'pieces', 'item', 'items', 'whole', 'half', 'quarter', 'slice', 'slices', 'clove', 'cloves']
        }
        
        # Common descriptors to remove
        self.descriptors_to_remove = {
            'fresh', 'dried', 'frozen', 'canned', 'organic', 'raw', 'cooked',
            'chopped', 'diced', 'sliced', 'minced', 'grated', 'shredded',
            'large', 'medium', 'small', 'extra', 'baby', 'young',
            'lean', 'boneless', 'skinless', 'whole', 'ground'
        }
    
    def _initialize_nlp(self):
        """Initialize spaCy NLP model"""
        try:
            import spacy
            
            # Try to load the model
            try:
                self.nlp = spacy.load(self.config.SPACY_MODEL)
                logger.info("spaCy model loaded successfully")
            except OSError:
                logger.warning(f"spaCy model '{self.config.SPACY_MODEL}' not found. Using basic processing.")
                self.nlp = None
                
        except ImportError:
            logger.warning("spaCy not installed. Using basic text processing.")
            self.nlp = None
    
    def _initialize_fuzzy_matcher(self):
        """Initialize fuzzy string matching"""
        try:
            from fuzzywuzzy import fuzz, process
            self.fuzzy_matcher = process
            self.fuzz = fuzz
            logger.info("Fuzzy matching initialized")
        except ImportError:
            logger.warning("fuzzywuzzy not installed. Fuzzy matching unavailable.")
            self.fuzzy_matcher = None
    
    def _build_synonym_lookup(self) -> Dict[str, str]:
        """Build reverse lookup dictionary for synonyms"""
        lookup = {}
        for standard, synonyms in self.ingredient_synonyms.items():
            # Add the standard name itself
            lookup[standard] = standard
            # Add all synonyms
            for synonym in synonyms:
                lookup[synonym.lower()] = standard
        return lookup
    
    def standardize_ingredient(self, ingredient_text: str) -> Dict[str, any]:
        """
        Standardize a single ingredient text
        
        Args:
            ingredient_text: Raw ingredient text (e.g., "2 cups fresh tomatoes, diced")
            
        Returns:
            Dictionary with standardized ingredient information
        """
        try:
            # Clean and normalize text
            cleaned_text = self._clean_text(ingredient_text)
            
            # Extract quantity and unit
            quantity, unit, ingredient_name = self._extract_quantity_and_unit(cleaned_text)
            
            # Standardize ingredient name
            standardized_name = self._standardize_name(ingredient_name)
            
            # Categorize ingredient
            category = self._categorize_ingredient(standardized_name)
            
            # Generate alternative names
            alternatives = self._get_alternatives(standardized_name)
            
            result = {
                'original': ingredient_text,
                'standardized_name': standardized_name,
                'quantity': quantity,
                'unit': unit,
                'category': category,
                'alternatives': alternatives,
                'confidence': self._calculate_confidence(ingredient_text, standardized_name)
            }
            
            logger.debug(f"Standardized '{ingredient_text}' -> '{standardized_name}'")
            return result
            
        except Exception as e:
            logger.error(f"Failed to standardize ingredient '{ingredient_text}': {e}")
            return {
                'original': ingredient_text,
                'standardized_name': ingredient_text.lower().strip(),
                'quantity': None,
                'unit': None,
                'category': 'other',
                'alternatives': [],
                'confidence': 0.5
            }
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize ingredient text"""
        # Convert to lowercase
        text = text.lower().strip()
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove parenthetical information
        text = re.sub(r'\([^)]*\)', '', text)
        
        # Remove common punctuation
        text = re.sub(r'[,;]', '', text)
        
        return text.strip()
    
    def _extract_quantity_and_unit(self, text: str) -> Tuple[Optional[float], Optional[str], str]:
        """Extract quantity, unit, and ingredient name from text"""
        # Pattern to match quantity and unit at the beginning
        pattern = r'^(\d+(?:\.\d+)?(?:/\d+)?)\s*([a-zA-Z]+)?\s+(.+)$'
        match = re.match(pattern, text)
        
        if match:
            quantity_str, unit, ingredient = match.groups()
            
            # Parse quantity (handle fractions)
            try:
                if '/' in quantity_str:
                    parts = quantity_str.split('/')
                    quantity = float(parts[0]) / float(parts[1])
                else:
                    quantity = float(quantity_str)
            except ValueError:
                quantity = None
            
            # Standardize unit
            if unit:
                unit = self._standardize_unit(unit)
            
            return quantity, unit, ingredient.strip()
        
        # If no quantity/unit pattern found, return the whole text as ingredient
        return None, None, text
    
    def _standardize_unit(self, unit: str) -> str:
        """Standardize measurement units"""
        unit = unit.lower()
        
        # Unit mappings
        unit_mappings = {
            # Weight
            'g': 'gram', 'grams': 'gram', 'kg': 'kilogram', 'kilograms': 'kilogram',
            'lb': 'pound', 'lbs': 'pound', 'pounds': 'pound', 'oz': 'ounce', 'ounces': 'ounce',
            
            # Volume
            'ml': 'milliliter', 'milliliters': 'milliliter', 'l': 'liter', 'liters': 'liter',
            'cups': 'cup', 'tbsp': 'tablespoon', 'tablespoons': 'tablespoon',
            'tsp': 'teaspoon', 'teaspoons': 'teaspoon', 'pints': 'pint', 'quarts': 'quart',
            
            # Count
            'pieces': 'piece', 'items': 'item', 'cloves': 'clove', 'slices': 'slice'
        }
        
        return unit_mappings.get(unit, unit)
    
    def _standardize_name(self, ingredient_name: str) -> str:
        """Standardize ingredient name using various techniques"""
        # Remove descriptors
        words = ingredient_name.split()
        filtered_words = [word for word in words if word not in self.descriptors_to_remove]
        cleaned_name = ' '.join(filtered_words)
        
        # Check direct synonym lookup
        if cleaned_name in self.synonym_to_standard:
            return self.synonym_to_standard[cleaned_name]
        
        # Try fuzzy matching if available
        if self.fuzzy_matcher:
            best_match = self._fuzzy_match(cleaned_name)
            if best_match:
                return best_match
        
        # Use spaCy for lemmatization if available
        if self.nlp:
            doc = self.nlp(cleaned_name)
            lemmatized = ' '.join([token.lemma_ for token in doc if not token.is_stop])
            if lemmatized and lemmatized != cleaned_name:
                # Check if lemmatized version has a match
                if lemmatized in self.synonym_to_standard:
                    return self.synonym_to_standard[lemmatized]
        
        # Return cleaned name if no standardization found
        return cleaned_name
    
    def _fuzzy_match(self, ingredient_name: str) -> Optional[str]:
        """Find best fuzzy match for ingredient name"""
        try:
            # Get all known ingredient names
            known_ingredients = list(self.synonym_to_standard.keys())
            
            # Find best match
            best_match, score = self.fuzzy_matcher.extractOne(
                ingredient_name, 
                known_ingredients,
                scorer=self.fuzz.ratio
            )
            
            # Return match if score is above threshold
            if score >= self.config.FUZZY_MATCH_THRESHOLD:
                return self.synonym_to_standard[best_match]
            
        except Exception as e:
            logger.debug(f"Fuzzy matching failed: {e}")
        
        return None
    
    def _categorize_ingredient(self, ingredient_name: str) -> str:
        """Categorize ingredient into food groups"""
        ingredient_lower = ingredient_name.lower()
        
        for category, ingredients in self.ingredient_categories.items():
            if any(ing in ingredient_lower for ing in ingredients):
                return category
        
        return 'other'
    
    def _get_alternatives(self, standardized_name: str) -> List[str]:
        """Get alternative names for an ingredient"""
        alternatives = []
        
        # Add synonyms
        if standardized_name in self.ingredient_synonyms:
            alternatives.extend(self.ingredient_synonyms[standardized_name])
        
        # Add variations (plural/singular)
        if standardized_name.endswith('s'):
            alternatives.append(standardized_name[:-1])
        else:
            alternatives.append(standardized_name + 's')
        
        # Remove duplicates and the original name
        alternatives = list(set(alternatives))
        if standardized_name in alternatives:
            alternatives.remove(standardized_name)
        
        return alternatives
    
    def _calculate_confidence(self, original: str, standardized: str) -> float:
        """Calculate confidence score for standardization"""
        if original.lower().strip() == standardized:
            return 1.0
        
        # Use fuzzy matching to calculate similarity
        if self.fuzzy_matcher:
            try:
                score = self.fuzz.ratio(original.lower(), standardized) / 100.0
                return score
            except:
                pass
        
        # Simple similarity based on word overlap
        original_words = set(original.lower().split())
        standardized_words = set(standardized.split())
        
        if not original_words:
            return 0.5
        
        overlap = len(original_words.intersection(standardized_words))
        return overlap / len(original_words)
    
    def batch_standardize(self, ingredient_list: List[str]) -> List[Dict[str, any]]:
        """Standardize a list of ingredients"""
        results = []
        for ingredient in ingredient_list:
            result = self.standardize_ingredient(ingredient)
            results.append(result)
        
        logger.info(f"Standardized {len(ingredient_list)} ingredients")
        return results
    
    def find_similar_ingredients(self, query: str, limit: int = 5) -> List[Tuple[str, float]]:
        """Find ingredients similar to query"""
        if not self.fuzzy_matcher:
            return []
        
        try:
            known_ingredients = list(self.synonym_to_standard.keys())
            matches = self.fuzzy_matcher.extract(
                query.lower(),
                known_ingredients,
                scorer=self.fuzz.ratio,
                limit=limit
            )
            
            # Convert to standardized names and return with scores
            results = []
            for match, score in matches:
                standardized = self.synonym_to_standard[match]
                results.append((standardized, score / 100.0))
            
            return results
            
        except Exception as e:
            logger.error(f"Similar ingredient search failed: {e}")
            return []

# Factory function
def create_ingredient_standardizer() -> IngredientStandardizer:
    """Create IngredientStandardizer instance"""
    return IngredientStandardizer()

# Utility functions
def standardize_ingredient_list(ingredients: List[str]) -> List[Dict[str, any]]:
    """Convenience function to standardize a list of ingredients"""
    standardizer = create_ingredient_standardizer()
    return standardizer.batch_standardize(ingredients)

def find_ingredient_matches(query: str, limit: int = 5) -> List[Tuple[str, float]]:
    """Find similar ingredients for a query"""
    standardizer = create_ingredient_standardizer()
    return standardizer.find_similar_ingredients(query, limit)
