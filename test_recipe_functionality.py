#!/usr/bin/env python3
"""
Test script to verify the recipe functionality works correctly
"""

import requests
import json

BASE_URL = "http://localhost:5002"

def test_find_recipes():
    """Test the find recipes functionality"""
    print("🔍 Testing Find Recipes functionality...")
    
    data = {
        'ingredients_text': 'chicken, tomato, onion, garlic',
        'recipe_action': 'find',
        'cuisine_preference': 'Italian',
        'diet_preference': '',
        'cooking_time': 'quick'
    }
    
    try:
        response = requests.post(f"{BASE_URL}/find-recipes", data=data)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Find Recipes: SUCCESS - Page loaded successfully")
            if "recipe" in response.text.lower():
                print("✅ Find Recipes: SUCCESS - Recipe content found in response")
            else:
                print("⚠️ Find Recipes: WARNING - No recipe content found")
        else:
            print(f"❌ Find Recipes: FAILED - Status code {response.status_code}")
            
    except Exception as e:
        print(f"❌ Find Recipes: ERROR - {e}")

def test_generate_recipes():
    """Test the generate recipes functionality"""
    print("\n🤖 Testing Generate Recipes functionality...")
    
    data = {
        'ingredients_text': 'chicken, tomato, onion, garlic, basil',
        'recipe_action': 'generate',
        'cuisine_preference': '',
        'diet_preference': '',
        'cooking_time': ''
    }
    
    try:
        response = requests.post(f"{BASE_URL}/find-recipes", data=data)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Generate Recipes: SUCCESS - Page loaded successfully")
            if "ai generated" in response.text.lower() or "generated" in response.text.lower():
                print("✅ Generate Recipes: SUCCESS - AI generated content found")
            else:
                print("⚠️ Generate Recipes: WARNING - No AI generated content found")
        else:
            print(f"❌ Generate Recipes: FAILED - Status code {response.status_code}")
            
    except Exception as e:
        print(f"❌ Generate Recipes: ERROR - {e}")

def test_smart_recipes_page():
    """Test that the smart recipes page loads"""
    print("\n📄 Testing Smart Recipes page...")
    
    try:
        response = requests.get(f"{BASE_URL}/smart-recipes")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Smart Recipes Page: SUCCESS - Page loaded successfully")
            if "find my recipes" in response.text.lower() and "generate my recipes" in response.text.lower():
                print("✅ Smart Recipes Page: SUCCESS - Both action buttons found")
            else:
                print("⚠️ Smart Recipes Page: WARNING - Action buttons not found")
        else:
            print(f"❌ Smart Recipes Page: FAILED - Status code {response.status_code}")
            
    except Exception as e:
        print(f"❌ Smart Recipes Page: ERROR - {e}")

if __name__ == "__main__":
    print("🧪 Testing Recipe Functionality")
    print("=" * 50)
    
    # Test the smart recipes page first
    test_smart_recipes_page()
    
    # Test find recipes functionality
    test_find_recipes()
    
    # Test generate recipes functionality
    test_generate_recipes()
    
    print("\n" + "=" * 50)
    print("🏁 Testing Complete!")
    print("\nTo manually test:")
    print(f"1. Visit {BASE_URL}/smart-recipes")
    print("2. Enter ingredients like: chicken, tomato, onion, garlic")
    print("3. Select either 'Find My Recipes' or 'Generate My Recipes'")
    print("4. Click the submit button")
    print("5. Check that you get detailed recipes with cooking instructions")
