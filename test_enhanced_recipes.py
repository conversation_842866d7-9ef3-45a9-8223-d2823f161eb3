#!/usr/bin/env python3
"""
Test the enhanced recipe generation with realistic names and precise instructions
"""

from ai_recipe_generator import AIRecipeGenerator
import json

def test_realistic_recipe_names():
    """Test that recipe names are realistic"""
    generator = AIRecipeGenerator()
    
    test_cases = [
        {
            'ingredients': ['chicken breast', 'tomato', 'basil', 'garlic', 'olive oil'],
            'expected_patterns': ['Chicken Margherita', 'Italian Herb Chicken', 'Chicken Cacciatore']
        },
        {
            'ingredients': ['chicken', 'broccoli', 'soy sauce', 'garlic', 'rice'],
            'expected_patterns': ['Chicken and Broccoli', '<PERSON>\'s Chicken', 'Asian Chicken Stir Fry']
        },
        {
            'ingredients': ['beef', 'onion', 'pepper', 'tomato'],
            'expected_patterns': ['Pepper Steak', 'Beef Fajitas', 'Beef and Vegetable Skillet']
        },
        {
            'ingredients': ['eggs', 'mushroom', 'onion', 'cheese'],
            'expected_patterns': ['Vegetable Omelet', 'Mushroom Omelet']
        }
    ]
    
    print("🧪 Testing Realistic Recipe Names")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {', '.join(test_case['ingredients'])}")
        
        try:
            recipe = generator.generate_ai_recipe(test_case['ingredients'], servings=2)
            recipe_name = recipe['name']
            
            print(f"Generated Name: {recipe_name}")
            
            # Check if name sounds realistic (not made-up)
            made_up_words = ['rustico', 'medley', 'delight', 'masterpiece', 'creation', 'fusion']
            is_realistic = not any(word.lower() in recipe_name.lower() for word in made_up_words)
            
            if is_realistic:
                print("✅ PASS: Recipe name sounds realistic")
            else:
                print("⚠️  WARNING: Recipe name might be too creative")
                
        except Exception as e:
            print(f"❌ ERROR: {e}")

def test_precise_ingredients():
    """Test that ingredients have precise quantities"""
    generator = AIRecipeGenerator()
    
    print("\n🥘 Testing Precise Ingredient Quantities")
    print("=" * 50)
    
    ingredients = ['chicken breast', 'onion', 'garlic', 'olive oil', 'salt', 'pepper']
    recipe = generator.generate_ai_recipe(ingredients, servings=4)
    
    print(f"Recipe: {recipe['name']}")
    print("\nIngredients with quantities:")
    
    for ingredient in recipe['ingredients']:
        print(f"  • {ingredient}")
        
        # Check if ingredient has proper quantity
        has_quantity = any(unit in ingredient.lower() for unit in ['oz', 'lbs', 'cups', 'tbsp', 'tsp', 'cloves', 'pieces'])
        
        if has_quantity:
            print("    ✅ Has precise quantity")
        else:
            print("    ⚠️  Missing quantity")

def test_detailed_instructions():
    """Test that instructions are detailed and precise"""
    generator = AIRecipeGenerator()
    
    print("\n👨‍🍳 Testing Detailed Cooking Instructions")
    print("=" * 50)
    
    ingredients = ['chicken breast', 'tomato', 'onion', 'garlic', 'basil', 'olive oil']
    recipe = generator.generate_ai_recipe(ingredients, servings=2)
    
    print(f"Recipe: {recipe['name']}")
    print("\nInstructions:")
    
    for i, instruction in enumerate(recipe['instructions'], 1):
        print(f"{i}. {instruction}")
    
    # Check for detailed elements
    instruction_text = ' '.join(recipe['instructions']).lower()
    
    checks = [
        ('temperatures', ['°f', 'degrees', 'heat']),
        ('timing', ['minutes', 'seconds', 'until']),
        ('techniques', ['dice', 'mince', 'sauté', 'cook', 'stir']),
        ('specificity', ['medium', 'high', 'low', 'golden', 'tender'])
    ]
    
    print("\nInstruction Quality Checks:")
    for check_name, keywords in checks:
        has_keywords = any(keyword in instruction_text for keyword in keywords)
        status = "✅ PASS" if has_keywords else "❌ FAIL"
        print(f"  {status}: {check_name.title()}")

def test_nlp_integration():
    """Test NLP integration for ingredient standardization"""
    print("\n🧠 Testing NLP Integration")
    print("=" * 50)
    
    try:
        from nlp_processor import create_ingredient_standardizer
        standardizer = create_ingredient_standardizer()
        
        test_ingredients = ['tomatos', 'onions', 'chicken breast', 'garlic cloves']
        
        print("Testing ingredient standardization:")
        for ingredient in test_ingredients:
            try:
                standardized = standardizer.standardize_ingredient(ingredient)
                print(f"  '{ingredient}' → '{standardized['standardized_name']}'")
            except Exception as e:
                print(f"  '{ingredient}' → ERROR: {e}")
                
    except ImportError as e:
        print(f"NLP modules not available: {e}")

if __name__ == "__main__":
    print("🍳 Enhanced Recipe Generation Test Suite")
    print("=" * 60)
    
    test_realistic_recipe_names()
    test_precise_ingredients()
    test_detailed_instructions()
    test_nlp_integration()
    
    print("\n" + "=" * 60)
    print("🏁 Test Suite Complete!")
    print("\nKey Improvements Made:")
    print("✅ Realistic recipe names (no made-up words)")
    print("✅ Precise ingredient quantities with units")
    print("✅ Detailed step-by-step cooking instructions")
    print("✅ NLP integration for ingredient standardization")
    print("✅ Enhanced image ingredient detection")
