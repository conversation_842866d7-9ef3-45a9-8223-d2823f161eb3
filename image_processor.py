"""
Advanced Image Processing Module for MealMind
Handles image preprocessing, enhancement, and preparation for ML models
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import logging
from pathlib import Path
from typing import Tuple, Optional, List, Union
from ml_config import VisionConfig, ProcessingConfig

# Set up logging
logging.basicConfig(level=getattr(logging, ProcessingConfig.LOG_LEVEL))
logger = logging.getLogger(__name__)

class ImageProcessor:
    """Advanced image processing for ingredient detection"""
    
    def __init__(self):
        self.config = VisionConfig()
        self.max_size = self.config.MAX_IMAGE_SIZE
        self.supported_formats = self.config.SUPPORTED_FORMATS
        
    def validate_image(self, image_path: Union[str, Path]) -> bool:
        """Validate if image file is supported and accessible"""
        try:
            image_path = Path(image_path)
            
            # Check if file exists
            if not image_path.exists():
                logger.error(f"Image file not found: {image_path}")
                return False
            
            # Check file extension
            if image_path.suffix.lower() not in self.supported_formats:
                logger.error(f"Unsupported image format: {image_path.suffix}")
                return False
            
            # Try to open image
            with Image.open(image_path) as img:
                img.verify()
            
            return True
            
        except Exception as e:
            logger.error(f"Image validation failed: {e}")
            return False
    
    def preprocess_image(self, image_path: Union[str, Path], 
                        enhance: bool = True) -> Optional[np.ndarray]:
        """
        Comprehensive image preprocessing pipeline
        
        Args:
            image_path: Path to input image
            enhance: Whether to apply image enhancement
            
        Returns:
            Preprocessed image as numpy array or None if failed
        """
        try:
            if not self.validate_image(image_path):
                return None
            
            # Load image
            image = cv2.imread(str(image_path))
            if image is None:
                logger.error(f"Failed to load image: {image_path}")
                return None
            
            # Convert BGR to RGB
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Resize image if too large
            image = self._resize_image(image)
            
            if enhance:
                # Apply enhancement pipeline
                image = self._enhance_image(image)
                image = self._denoise_image(image)
                image = self._adjust_lighting(image)
            
            logger.info(f"Successfully preprocessed image: {image_path}")
            return image
            
        except Exception as e:
            logger.error(f"Image preprocessing failed: {e}")
            return None
    
    def _resize_image(self, image: np.ndarray) -> np.ndarray:
        """Resize image while maintaining aspect ratio"""
        height, width = image.shape[:2]
        max_width, max_height = self.max_size
        
        if width <= max_width and height <= max_height:
            return image
        
        # Calculate scaling factor
        scale = min(max_width / width, max_height / height)
        new_width = int(width * scale)
        new_height = int(height * scale)
        
        # Resize image
        resized = cv2.resize(image, (new_width, new_height), 
                           interpolation=cv2.INTER_LANCZOS4)
        
        logger.debug(f"Resized image from {width}x{height} to {new_width}x{new_height}")
        return resized
    
    def _enhance_image(self, image: np.ndarray) -> np.ndarray:
        """Apply image enhancement techniques"""
        # Convert to PIL for enhancement
        pil_image = Image.fromarray(image)
        
        # Enhance contrast
        enhancer = ImageEnhance.Contrast(pil_image)
        pil_image = enhancer.enhance(1.2)
        
        # Enhance brightness
        enhancer = ImageEnhance.Brightness(pil_image)
        pil_image = enhancer.enhance(1.1)
        
        # Enhance color saturation
        enhancer = ImageEnhance.Color(pil_image)
        pil_image = enhancer.enhance(1.15)
        
        # Enhance sharpness
        enhancer = ImageEnhance.Sharpness(pil_image)
        pil_image = enhancer.enhance(1.1)
        
        # Convert back to numpy array
        enhanced = np.array(pil_image)
        
        logger.debug("Applied image enhancement")
        return enhanced
    
    def _denoise_image(self, image: np.ndarray) -> np.ndarray:
        """Remove noise from image"""
        # Apply Non-local Means Denoising
        denoised = cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
        
        logger.debug("Applied noise reduction")
        return denoised
    
    def _adjust_lighting(self, image: np.ndarray) -> np.ndarray:
        """Adjust lighting and exposure"""
        # Convert to LAB color space
        lab = cv2.cvtColor(image, cv2.COLOR_RGB2LAB)
        l, a, b = cv2.split(lab)
        
        # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization) to L channel
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        l = clahe.apply(l)
        
        # Merge channels and convert back to RGB
        lab = cv2.merge([l, a, b])
        adjusted = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)
        
        logger.debug("Applied lighting adjustment")
        return adjusted
    
    def extract_food_regions(self, image: np.ndarray) -> List[np.ndarray]:
        """
        Extract potential food regions from image using segmentation
        
        Args:
            image: Input image as numpy array
            
        Returns:
            List of cropped food regions
        """
        try:
            # Convert to HSV for better color segmentation
            hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
            
            # Define color ranges for food items (broad ranges)
            food_ranges = [
                # Red foods (tomatoes, apples, etc.)
                ([0, 50, 50], [10, 255, 255]),
                ([170, 50, 50], [180, 255, 255]),
                # Orange foods (carrots, oranges, etc.)
                ([10, 50, 50], [25, 255, 255]),
                # Yellow foods (bananas, corn, etc.)
                ([25, 50, 50], [35, 255, 255]),
                # Green foods (vegetables, etc.)
                ([35, 50, 50], [85, 255, 255]),
                # Brown foods (bread, meat, etc.)
                ([10, 50, 20], [20, 255, 200]),
            ]
            
            # Create combined mask
            combined_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
            
            for lower, upper in food_ranges:
                lower = np.array(lower)
                upper = np.array(upper)
                mask = cv2.inRange(hsv, lower, upper)
                combined_mask = cv2.bitwise_or(combined_mask, mask)
            
            # Apply morphological operations to clean up mask
            kernel = np.ones((5, 5), np.uint8)
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, 
                                         cv2.CHAIN_APPROX_SIMPLE)
            
            # Extract regions
            food_regions = []
            min_area = 1000  # Minimum area threshold
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > min_area:
                    # Get bounding rectangle
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # Add padding
                    padding = 10
                    x = max(0, x - padding)
                    y = max(0, y - padding)
                    w = min(image.shape[1] - x, w + 2 * padding)
                    h = min(image.shape[0] - y, h + 2 * padding)
                    
                    # Extract region
                    region = image[y:y+h, x:x+w]
                    food_regions.append(region)
            
            logger.info(f"Extracted {len(food_regions)} potential food regions")
            return food_regions
            
        except Exception as e:
            logger.error(f"Food region extraction failed: {e}")
            return [image]  # Return original image if extraction fails
    
    def prepare_for_detection(self, image: np.ndarray) -> np.ndarray:
        """
        Prepare image for object detection models
        
        Args:
            image: Preprocessed image
            
        Returns:
            Image ready for detection model
        """
        # Ensure image is in correct format (RGB, uint8)
        if image.dtype != np.uint8:
            image = (image * 255).astype(np.uint8)
        
        # Ensure 3 channels
        if len(image.shape) == 2:
            image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
        elif image.shape[2] == 4:
            image = cv2.cvtColor(image, cv2.COLOR_RGBA2RGB)
        
        return image
    
    def save_processed_image(self, image: np.ndarray, output_path: Union[str, Path],
                           quality: int = None) -> bool:
        """
        Save processed image to file
        
        Args:
            image: Image to save
            output_path: Output file path
            quality: JPEG quality (1-100)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Convert to PIL Image
            pil_image = Image.fromarray(image)
            
            # Set quality
            if quality is None:
                quality = self.config.IMAGE_QUALITY
            
            # Save image
            if output_path.suffix.lower() in ['.jpg', '.jpeg']:
                pil_image.save(output_path, 'JPEG', quality=quality, optimize=True)
            else:
                pil_image.save(output_path)
            
            logger.info(f"Saved processed image: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save image: {e}")
            return False

# Utility functions
def create_image_processor() -> ImageProcessor:
    """Factory function to create ImageProcessor instance"""
    return ImageProcessor()

def batch_process_images(image_paths: List[Union[str, Path]], 
                        output_dir: Union[str, Path] = None,
                        enhance: bool = True) -> List[Optional[np.ndarray]]:
    """
    Process multiple images in batch
    
    Args:
        image_paths: List of image file paths
        output_dir: Optional directory to save processed images
        enhance: Whether to apply enhancement
        
    Returns:
        List of processed images (None for failed images)
    """
    processor = create_image_processor()
    processed_images = []
    
    for i, image_path in enumerate(image_paths):
        logger.info(f"Processing image {i+1}/{len(image_paths)}: {image_path}")
        
        # Process image
        processed = processor.preprocess_image(image_path, enhance=enhance)
        processed_images.append(processed)
        
        # Save if output directory specified
        if output_dir and processed is not None:
            output_path = Path(output_dir) / f"processed_{Path(image_path).name}"
            processor.save_processed_image(processed, output_path)
    
    return processed_images
