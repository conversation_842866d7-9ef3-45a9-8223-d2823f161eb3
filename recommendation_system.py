"""
Advanced Recommendation System for MealMind
Implements collaborative filtering, content-based filtering, and hybrid approaches
"""

import numpy as np
import pandas as pd
import logging
from typing import List, Dict, Tuple, Optional, Any
from collections import defaultdict
import json
from pathlib import Path
import pickle

from ml_config import RecommendationConfig, ModelPaths
from nlp_processor import IngredientStandardizer

# Set up logging
logger = logging.getLogger(__name__)

class RecipeRecommendationSystem:
    """Advanced recipe recommendation system"""
    
    def __init__(self):
        self.config = RecommendationConfig()
        self.ingredient_standardizer = IngredientStandardizer()
        
        # Initialize models
        self.collaborative_model = None
        self.content_model = None
        self.recipe_features = None
        self.user_profiles = {}
        
        # Recipe and user data
        self.recipes_df = None
        self.user_ratings = defaultdict(dict)
        self.recipe_embeddings = {}
        
        # Initialize components
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize recommendation models"""
        try:
            # Try to load pre-trained models
            self._load_models()
        except:
            logger.info("No pre-trained models found. Will train on first use.")
            
        # Initialize scikit-learn components
        self._initialize_sklearn_components()
    
    def _initialize_sklearn_components(self):
        """Initialize scikit-learn components"""
        try:
            from sklearn.feature_extraction.text import TfidfVectorizer
            from sklearn.metrics.pairwise import cosine_similarity
            from sklearn.decomposition import TruncatedSVD
            from sklearn.preprocessing import StandardScaler
            
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=1000,
                stop_words='english',
                ngram_range=(1, 2)
            )
            self.scaler = StandardScaler()
            self.svd_model = TruncatedSVD(n_components=self.config.N_FACTORS)
            
            logger.info("Scikit-learn components initialized")
            
        except ImportError:
            logger.warning("Scikit-learn not available. Using basic recommendations.")
    
    def _load_models(self):
        """Load pre-trained models from disk"""
        model_paths = ModelPaths()
        
        if model_paths.RECIPE_EMBEDDINGS.exists():
            with open(model_paths.RECIPE_EMBEDDINGS, 'rb') as f:
                self.recipe_embeddings = pickle.load(f)
            logger.info("Loaded recipe embeddings")
        
        if model_paths.USER_PREFERENCES.exists():
            with open(model_paths.USER_PREFERENCES, 'rb') as f:
                self.user_profiles = pickle.load(f)
            logger.info("Loaded user preferences")
    
    def _save_models(self):
        """Save trained models to disk"""
        model_paths = ModelPaths()
        model_paths.RECIPE_EMBEDDINGS.parent.mkdir(parents=True, exist_ok=True)
        
        # Save recipe embeddings
        with open(model_paths.RECIPE_EMBEDDINGS, 'wb') as f:
            pickle.dump(self.recipe_embeddings, f)
        
        # Save user preferences
        with open(model_paths.USER_PREFERENCES, 'wb') as f:
            pickle.dump(self.user_profiles, f)
        
        logger.info("Models saved to disk")
    
    def prepare_recipe_data(self, recipes: List[Dict]) -> pd.DataFrame:
        """Prepare recipe data for recommendation system"""
        try:
            # Convert recipes to DataFrame
            df = pd.DataFrame(recipes)
            
            # Feature engineering
            df = self._engineer_recipe_features(df)
            
            # Create recipe embeddings
            self._create_recipe_embeddings(df)
            
            self.recipes_df = df
            logger.info(f"Prepared {len(df)} recipes for recommendation")
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to prepare recipe data: {e}")
            return pd.DataFrame()
    
    def _engineer_recipe_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Engineer features for recipes"""
        # Normalize numerical features
        if 'prep_time' in df.columns:
            df['prep_time_normalized'] = (df['prep_time'] - df['prep_time'].mean()) / df['prep_time'].std()
        
        if 'calories' in df.columns:
            df['calories_normalized'] = (df['calories'] - df['calories'].mean()) / df['calories'].std()
        
        # Encode categorical features
        if 'difficulty' in df.columns:
            difficulty_map = {'Easy': 1, 'Medium': 2, 'Hard': 3}
            df['difficulty_encoded'] = df['difficulty'].map(difficulty_map).fillna(2)
        
        if 'cuisine' in df.columns:
            df['cuisine_encoded'] = pd.Categorical(df['cuisine']).codes
        
        if 'diet_type' in df.columns:
            df['diet_type_encoded'] = pd.Categorical(df['diet_type']).codes
        
        # Ingredient-based features
        df['ingredient_count'] = df['ingredients'].apply(self._count_ingredients)
        df['protein_ratio'] = df['ingredients'].apply(self._calculate_protein_ratio)
        df['vegetable_ratio'] = df['ingredients'].apply(self._calculate_vegetable_ratio)
        df['grain_ratio'] = df['ingredients'].apply(self._calculate_grain_ratio)
        
        return df
    
    def _count_ingredients(self, ingredients) -> int:
        """Count total ingredients in a recipe"""
        if isinstance(ingredients, dict):
            return sum(len(category_ingredients) for category_ingredients in ingredients.values())
        elif isinstance(ingredients, list):
            return len(ingredients)
        return 0
    
    def _calculate_protein_ratio(self, ingredients) -> float:
        """Calculate ratio of protein ingredients"""
        total_count = self._count_ingredients(ingredients)
        if total_count == 0:
            return 0.0
        
        protein_count = 0
        protein_keywords = ['chicken', 'beef', 'pork', 'fish', 'egg', 'tofu', 'bean', 'lentil']
        
        if isinstance(ingredients, dict):
            all_ingredients = []
            for category_ingredients in ingredients.values():
                all_ingredients.extend(category_ingredients)
        else:
            all_ingredients = ingredients
        
        for ingredient in all_ingredients:
            if any(keyword in ingredient.lower() for keyword in protein_keywords):
                protein_count += 1
        
        return protein_count / total_count
    
    def _calculate_vegetable_ratio(self, ingredients) -> float:
        """Calculate ratio of vegetable ingredients"""
        total_count = self._count_ingredients(ingredients)
        if total_count == 0:
            return 0.0
        
        vegetable_count = 0
        vegetable_keywords = ['tomato', 'onion', 'carrot', 'broccoli', 'spinach', 'pepper', 'mushroom', 'lettuce']
        
        if isinstance(ingredients, dict):
            all_ingredients = []
            for category_ingredients in ingredients.values():
                all_ingredients.extend(category_ingredients)
        else:
            all_ingredients = ingredients
        
        for ingredient in all_ingredients:
            if any(keyword in ingredient.lower() for keyword in vegetable_keywords):
                vegetable_count += 1
        
        return vegetable_count / total_count
    
    def _calculate_grain_ratio(self, ingredients) -> float:
        """Calculate ratio of grain ingredients"""
        total_count = self._count_ingredients(ingredients)
        if total_count == 0:
            return 0.0
        
        grain_count = 0
        grain_keywords = ['rice', 'pasta', 'bread', 'flour', 'quinoa', 'oat']
        
        if isinstance(ingredients, dict):
            all_ingredients = []
            for category_ingredients in ingredients.values():
                all_ingredients.extend(category_ingredients)
        else:
            all_ingredients = ingredients
        
        for ingredient in all_ingredients:
            if any(keyword in ingredient.lower() for keyword in grain_keywords):
                grain_count += 1
        
        return grain_count / total_count
    
    def _create_recipe_embeddings(self, df: pd.DataFrame):
        """Create embeddings for recipes using TF-IDF"""
        try:
            # Create text representation of recipes
            recipe_texts = []
            for _, recipe in df.iterrows():
                text_parts = []
                
                # Add recipe name
                text_parts.append(recipe.get('name', ''))
                
                # Add cuisine
                text_parts.append(recipe.get('cuisine', ''))
                
                # Add ingredients
                ingredients = recipe.get('ingredients', [])
                if isinstance(ingredients, dict):
                    for category_ingredients in ingredients.values():
                        text_parts.extend(category_ingredients)
                else:
                    text_parts.extend(ingredients)
                
                recipe_text = ' '.join(text_parts)
                recipe_texts.append(recipe_text)
            
            # Create TF-IDF embeddings
            tfidf_matrix = self.tfidf_vectorizer.fit_transform(recipe_texts)
            
            # Store embeddings
            for i, recipe_id in enumerate(df['id']):
                self.recipe_embeddings[recipe_id] = tfidf_matrix[i].toarray().flatten()
            
            logger.info(f"Created embeddings for {len(self.recipe_embeddings)} recipes")
            
        except Exception as e:
            logger.error(f"Failed to create recipe embeddings: {e}")
    
    def get_content_based_recommendations(self, user_ingredients: List[str], 
                                        user_preferences: Dict = None,
                                        limit: int = None) -> List[Dict]:
        """Get recommendations based on content similarity"""
        if limit is None:
            limit = self.config.MAX_RECOMMENDATIONS
        
        try:
            if self.recipes_df is None or self.recipes_df.empty:
                logger.warning("No recipe data available for recommendations")
                return []
            
            # Standardize user ingredients
            standardized_ingredients = []
            for ingredient in user_ingredients:
                result = self.ingredient_standardizer.standardize_ingredient(ingredient)
                standardized_ingredients.append(result['standardized_name'])
            
            # Calculate ingredient match scores
            recipe_scores = []
            for _, recipe in self.recipes_df.iterrows():
                score = self._calculate_recipe_score(recipe, standardized_ingredients, user_preferences)
                recipe_scores.append({
                    'recipe_id': recipe['id'],
                    'score': score,
                    'recipe': recipe.to_dict()
                })
            
            # Sort by score and return top recommendations
            recipe_scores.sort(key=lambda x: x['score'], reverse=True)
            
            recommendations = []
            for item in recipe_scores[:limit]:
                recommendation = item['recipe'].copy()
                recommendation['recommendation_score'] = item['score']
                recommendation['match_details'] = self._get_match_details(
                    item['recipe'], standardized_ingredients
                )
                recommendations.append(recommendation)
            
            logger.info(f"Generated {len(recommendations)} content-based recommendations")
            return recommendations
            
        except Exception as e:
            logger.error(f"Content-based recommendation failed: {e}")
            return []
    
    def _calculate_recipe_score(self, recipe: pd.Series, user_ingredients: List[str],
                              user_preferences: Dict = None) -> float:
        """Calculate overall score for a recipe"""
        weights = self.config.PREFERENCE_WEIGHTS
        
        # Ingredient match score
        ingredient_score = self._calculate_ingredient_match_score(recipe, user_ingredients)
        
        # Preference scores
        cuisine_score = self._calculate_cuisine_score(recipe, user_preferences)
        difficulty_score = self._calculate_difficulty_score(recipe, user_preferences)
        dietary_score = self._calculate_dietary_score(recipe, user_preferences)
        
        # Combine scores
        total_score = (
            weights['ingredient_match'] * ingredient_score +
            weights['cuisine_preference'] * cuisine_score +
            weights['difficulty_preference'] * difficulty_score +
            weights['dietary_restrictions'] * dietary_score
        )
        
        return total_score
    
    def _calculate_ingredient_match_score(self, recipe: pd.Series, user_ingredients: List[str]) -> float:
        """Calculate how well recipe ingredients match user ingredients"""
        recipe_ingredients = recipe.get('ingredients', [])
        
        if isinstance(recipe_ingredients, dict):
            all_recipe_ingredients = []
            for category_ingredients in recipe_ingredients.values():
                all_recipe_ingredients.extend(category_ingredients)
        else:
            all_recipe_ingredients = recipe_ingredients
        
        if not all_recipe_ingredients:
            return 0.0
        
        # Standardize recipe ingredients
        standardized_recipe_ingredients = []
        for ingredient in all_recipe_ingredients:
            result = self.ingredient_standardizer.standardize_ingredient(ingredient)
            standardized_recipe_ingredients.append(result['standardized_name'])
        
        # Calculate match
        matches = 0
        for user_ing in user_ingredients:
            for recipe_ing in standardized_recipe_ingredients:
                if self._ingredients_match(user_ing, recipe_ing):
                    matches += 1
                    break
        
        return matches / len(standardized_recipe_ingredients)
    
    def _ingredients_match(self, ingredient1: str, ingredient2: str) -> bool:
        """Check if two ingredients match (with fuzzy matching)"""
        if ingredient1.lower() == ingredient2.lower():
            return True
        
        # Check if one ingredient contains the other
        if ingredient1.lower() in ingredient2.lower() or ingredient2.lower() in ingredient1.lower():
            return True
        
        # Use fuzzy matching if available
        if hasattr(self.ingredient_standardizer, 'fuzz') and self.ingredient_standardizer.fuzz:
            score = self.ingredient_standardizer.fuzz.ratio(ingredient1.lower(), ingredient2.lower())
            return score >= 80
        
        return False
    
    def _calculate_cuisine_score(self, recipe: pd.Series, user_preferences: Dict = None) -> float:
        """Calculate cuisine preference score"""
        if not user_preferences or 'favorite_cuisines' not in user_preferences:
            return 0.5  # Neutral score
        
        recipe_cuisine = recipe.get('cuisine', '').lower()
        favorite_cuisines = [c.lower() for c in user_preferences['favorite_cuisines']]
        
        if recipe_cuisine in favorite_cuisines:
            return 1.0
        
        return 0.2  # Lower score for non-preferred cuisines
    
    def _calculate_difficulty_score(self, recipe: pd.Series, user_preferences: Dict = None) -> float:
        """Calculate difficulty preference score"""
        if not user_preferences or 'cooking_skill' not in user_preferences:
            return 0.5  # Neutral score
        
        recipe_difficulty = recipe.get('difficulty', 'Medium')
        user_skill = user_preferences['cooking_skill']
        
        # Score based on skill level
        skill_difficulty_map = {
            'Beginner': {'Easy': 1.0, 'Medium': 0.5, 'Hard': 0.1},
            'Intermediate': {'Easy': 0.8, 'Medium': 1.0, 'Hard': 0.6},
            'Advanced': {'Easy': 0.6, 'Medium': 0.8, 'Hard': 1.0}
        }
        
        return skill_difficulty_map.get(user_skill, {}).get(recipe_difficulty, 0.5)
    
    def _calculate_dietary_score(self, recipe: pd.Series, user_preferences: Dict = None) -> float:
        """Calculate dietary restriction score"""
        if not user_preferences or 'dietary_preferences' not in user_preferences:
            return 1.0  # No restrictions
        
        recipe_diet = recipe.get('diet_type', '').lower()
        user_diets = [d.lower() for d in user_preferences['dietary_preferences']]
        
        # Check if recipe matches dietary preferences
        if not user_diets:
            return 1.0
        
        if recipe_diet in user_diets:
            return 1.0
        
        # Special cases
        if 'vegetarian' in user_diets and recipe_diet == 'vegan':
            return 1.0  # Vegan recipes are suitable for vegetarians
        
        return 0.1  # Low score for incompatible diets
    
    def _get_match_details(self, recipe: Dict, user_ingredients: List[str]) -> Dict:
        """Get detailed match information for a recipe"""
        recipe_ingredients = recipe.get('ingredients', [])
        
        if isinstance(recipe_ingredients, dict):
            all_recipe_ingredients = []
            for category_ingredients in recipe_ingredients.values():
                all_recipe_ingredients.extend(category_ingredients)
        else:
            all_recipe_ingredients = recipe_ingredients
        
        matched = []
        missing = []
        
        for recipe_ing in all_recipe_ingredients:
            found_match = False
            for user_ing in user_ingredients:
                if self._ingredients_match(user_ing, recipe_ing):
                    matched.append(recipe_ing)
                    found_match = True
                    break
            
            if not found_match:
                missing.append(recipe_ing)
        
        match_percentage = (len(matched) / len(all_recipe_ingredients)) * 100 if all_recipe_ingredients else 0
        
        return {
            'matched_ingredients': matched,
            'missing_ingredients': missing,
            'match_percentage': round(match_percentage, 1),
            'total_ingredients': len(all_recipe_ingredients)
        }
    
    def update_user_preferences(self, user_id: str, preferences: Dict):
        """Update user preferences for personalization"""
        self.user_profiles[user_id] = preferences
        logger.info(f"Updated preferences for user {user_id}")
    
    def add_user_rating(self, user_id: str, recipe_id: int, rating: float):
        """Add user rating for collaborative filtering"""
        self.user_ratings[user_id][recipe_id] = rating
        logger.info(f"Added rating {rating} for recipe {recipe_id} by user {user_id}")

# Factory function
def create_recommendation_system() -> RecipeRecommendationSystem:
    """Create RecipeRecommendationSystem instance"""
    return RecipeRecommendationSystem()

# Utility functions
def get_recipe_recommendations(recipes: List[Dict], user_ingredients: List[str],
                             user_preferences: Dict = None, limit: int = 10) -> List[Dict]:
    """Convenience function to get recipe recommendations"""
    recommender = create_recommendation_system()
    recommender.prepare_recipe_data(recipes)
    return recommender.get_content_based_recommendations(user_ingredients, user_preferences, limit)
