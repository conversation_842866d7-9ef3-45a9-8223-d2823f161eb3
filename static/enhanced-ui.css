/* Enhanced Organic Theme with Modern UI Effects for MealMind - Updated 2025-07-01 */

/* ===== GLOBAL OVERFLOW CONTROL ===== */
html, body {
    overflow-x: hidden;
    width: 100vw;
    position: relative;
}

/* Prevent horizontal overflow on all containers */
*, *::before, *::after {
    box-sizing: border-box;
}

/* Ensure containers use full width but don't overflow */
.container, .max-w-7xl, .max-w-6xl, .max-w-4xl, .max-w-2xl {
    width: 100%;
    max-width: 100%;
}

/* Specific fix for main content areas */
main {
    width: 100vw;
    overflow-x: hidden;
}

/* Home page specific fixes - Force no horizontal scroll */
html {
    overflow-x: hidden !important;
    max-width: 100vw !important;
}

body {
    overflow-x: hidden !important;
    max-width: 100vw !important;
    position: relative !important;
}

/* Sliding container optimizations */
#sliding-container {
    will-change: transform;
    overflow: hidden !important;
    max-width: 100vw !important;
    position: relative !important;
}

/* Prevent any child elements from causing overflow */
#sliding-container > * {
    max-width: 100vw !important;
    overflow-x: hidden !important;
}

/* Specific fix for sliding sections */
.slide-section {
    max-width: 100vw !important;
    overflow-x: hidden !important;
    position: relative !important;
}

/* Ensure transform animations don't cause scroll */
.slide-section[style*="transform"] {
    overflow-x: hidden !important;
}

/* Force all main containers to respect viewport */
main {
    overflow-x: hidden !important;
    max-width: 100vw !important;
    position: relative !important;
}

/* Ensure sliding container wrapper constrains overflow */
main > div:first-child {
    overflow-x: hidden !important;
    width: 100vw !important;
    max-width: 100vw !important;
    position: relative !important;
}

/* ===== THEME VARIABLES ===== */
:root {
    /* Light Theme */
    --glass-bg: rgba(255, 255, 255, 0.15);
    --glass-border: rgba(255, 255, 255, 0.25);
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-heavy: rgba(0, 0, 0, 0.25);
    --neuro-light: rgba(255, 255, 255, 0.8);
    --neuro-dark: rgba(0, 0, 0, 0.1);
    
    /* Softer Glow Colors */
    --glow-primary: #2D7A3D;
    --glow-secondary: #4A9B5E;
    --glow-accent: #1A5E2A;
    --glow-soft: rgba(45, 122, 61, 0.3);
    
    /* Organic Colors */
    --organic-beige: #F9F5ED;
    --organic-cream: #FDF9F0;
    --rich-green: #1A5E2A;
    --soft-green: #2D7A3D;
    --accent-green: #4A9B5E;
    --light-sage: #E8F5E8;
}

.dark {
    /* Dark Theme */
    --glass-bg: rgba(0, 0, 0, 0.25);
    --glass-border: rgba(255, 255, 255, 0.15);
    --shadow-light: rgba(0, 0, 0, 0.3);
    --shadow-heavy: rgba(0, 0, 0, 0.6);
    --neuro-light: rgba(255, 255, 255, 0.1);
    --neuro-dark: rgba(0, 0, 0, 0.3);
    
    /* Dark Theme Colors */
    --organic-beige: #0F172A;
    --organic-cream: #1E293B;
    --rich-green: #00FF88;
    --soft-green: #00D4AA;
    --accent-green: #4ADE80;
    --light-sage: rgba(0, 255, 136, 0.1);
}

/* ===== GLASSMORPHISM EFFECTS ===== */
.glass {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    box-shadow: 0 8px 32px var(--shadow-light);
}

.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    box-shadow: 0 4px 16px var(--shadow-light);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.glass-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 16px 48px var(--shadow-heavy);
    border-color: var(--glow-soft);
}

/* ===== NEUROMORPHISM EFFECTS ===== */
.neuro {
    background: var(--organic-beige);
    box-shadow:
        8px 8px 16px var(--neuro-dark),
        -8px -8px 16px var(--neuro-light);
    border: none;
    transition: all 0.3s ease;
}

.neuro:hover {
    box-shadow:
        12px 12px 24px var(--neuro-dark),
        -12px -12px 24px var(--neuro-light);
    transform: translateY(-2px);
}

.neuro-inset {
    background: var(--organic-beige);
    box-shadow:
        inset 4px 4px 8px var(--neuro-dark),
        inset -4px -4px 8px var(--neuro-light);
    border: none;
    transition: all 0.3s ease;
}

.neuro-inset:focus {
    box-shadow:
        inset 6px 6px 12px var(--neuro-dark),
        inset -6px -6px 12px var(--neuro-light);
}

/* ===== COMPREHENSIVE NEUROMORPHISM ===== */
.neuro-card {
    background: var(--organic-beige);
    box-shadow:
        6px 6px 12px var(--neuro-dark),
        -6px -6px 12px var(--neuro-light);
    border: none;
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.neuro-card:hover {
    box-shadow:
        10px 10px 20px var(--neuro-dark),
        -10px -10px 20px var(--neuro-light);
    transform: translateY(-3px);
}

.neuro-btn {
    background: var(--organic-beige);
    box-shadow:
        4px 4px 8px var(--neuro-dark),
        -4px -4px 8px var(--neuro-light);
    border: none;
    border-radius: 12px;
    transition: all 0.2s ease;
}

.neuro-btn:hover {
    box-shadow:
        6px 6px 12px var(--neuro-dark),
        -6px -6px 12px var(--neuro-light);
    transform: translateY(-1px);
}

.neuro-btn:active {
    box-shadow:
        inset 2px 2px 4px var(--neuro-dark),
        inset -2px -2px 4px var(--neuro-light);
    transform: translateY(1px);
}

.neuro-input {
    background: var(--organic-beige);
    box-shadow:
        inset 3px 3px 6px var(--neuro-dark),
        inset -3px -3px 6px var(--neuro-light);
    border: none;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.neuro-input:focus {
    box-shadow:
        inset 4px 4px 8px var(--neuro-dark),
        inset -4px -4px 8px var(--neuro-light);
    outline: none;
}

.neuro-container {
    background: var(--organic-beige);
    box-shadow:
        15px 15px 30px var(--neuro-dark),
        -15px -15px 30px var(--neuro-light);
    border: none;
    border-radius: 25px;
    transition: all 0.4s ease;
}

.neuro-flat {
    background: var(--organic-beige);
    box-shadow:
        2px 2px 4px var(--neuro-dark),
        -2px -2px 4px var(--neuro-light);
    border: none;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.neuro-pressed {
    background: var(--organic-beige);
    box-shadow:
        inset 6px 6px 12px var(--neuro-dark),
        inset -6px -6px 12px var(--neuro-light);
    border: none;
    border-radius: 15px;
}

/* ===== NEUROMORPHISM NAVIGATION ===== */
.neuro-nav {
    background: var(--organic-beige);
    box-shadow:
        0 8px 16px var(--neuro-dark),
        0 -2px 4px var(--neuro-light);
    border: none;
    backdrop-filter: blur(10px);
}

.neuro-nav-item {
    background: transparent;
    box-shadow: none;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.neuro-nav-item:hover {
    background: var(--organic-beige);
    box-shadow:
        3px 3px 6px var(--neuro-dark),
        -3px -3px 6px var(--neuro-light);
}

.neuro-nav-item.active {
    background: var(--organic-beige);
    box-shadow:
        inset 2px 2px 4px var(--neuro-dark),
        inset -2px -2px 4px var(--neuro-light);
}

/* ===== ENHANCED GLOWING BUTTONS ===== */
.glow-btn {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--glow-primary), var(--glow-secondary));
    box-shadow: 0 4px 15px var(--glow-soft);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: none;
}

.glow-btn:hover {
    box-shadow: 
        0 8px 25px var(--glow-soft), 
        0 0 30px rgba(45, 122, 61, 0.2);
    transform: translateY(-3px) scale(1.02);
}

.glow-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.glow-btn:hover::before {
    left: 100%;
}

.glow-btn-secondary {
    background: linear-gradient(135deg, var(--glass-bg), rgba(74, 155, 94, 0.2));
    border: 2px solid var(--glow-primary);
    color: var(--glow-primary);
    box-shadow: 0 4px 15px rgba(45, 122, 61, 0.1);
}

.glow-btn-secondary:hover {
    background: var(--glow-primary);
    color: white;
    box-shadow: 0 8px 25px var(--glow-soft);
}

/* ===== 3D TILT EFFECTS ===== */
.tilt-card {
    transform-style: preserve-3d;
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.tilt-card:hover {
    transform: perspective(1000px) rotateX(8deg) rotateY(8deg) translateZ(20px);
}

.tilt-btn {
    transform-style: preserve-3d;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.tilt-btn:hover {
    transform: perspective(500px) rotateX(5deg) rotateY(5deg) translateZ(10px);
}

.tilt-btn:active {
    transform: perspective(500px) rotateX(2deg) rotateY(2deg) translateZ(5px);
    transition: all 0.1s ease;
}

/* ===== FLOATING ANIMATIONS ===== */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes glow-pulse {
    0%, 100% { box-shadow: 0 0 5px var(--glow-soft); }
    50% { box-shadow: 0 0 20px var(--glow-soft), 0 0 30px var(--glow-soft); }
}

@keyframes parallax {
    0% { transform: translateX(-50%); }
    100% { transform: translateX(50%); }
}

.float-element {
    animation: float 6s ease-in-out infinite;
}

.float-element:nth-child(2) { animation-delay: -2s; }
.float-element:nth-child(3) { animation-delay: -4s; }

/* ===== PARALLAX BACKGROUND ===== */
.parallax-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(45deg,
        rgba(26, 94, 42, 0.05) 0%,
        rgba(74, 155, 94, 0.05) 25%,
        rgba(45, 122, 61, 0.05) 50%,
        rgba(26, 94, 42, 0.05) 75%,
        rgba(74, 155, 94, 0.05) 100%);
    animation: parallax 20s infinite linear;
    z-index: -1;
    overflow: hidden;
}





/* ===== ENHANCED FORM INPUTS ===== */
input, textarea, select {
    transition: all 0.3s ease;
}

input:focus, textarea:focus, select:focus {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px var(--shadow-light);
}
