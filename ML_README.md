# MealMind ML Implementation

This document describes the machine learning implementation for MealMind, including computer vision for ingredient detection, NLP for ingredient standardization, and recommendation systems.

## 🚀 Quick Start

1. **Install Dependencies**:
   ```bash
   python setup_ml.py
   ```

2. **Run the Application**:
   ```bash
   python app.py
   ```

## 🧠 ML Components Overview

### 1. Computer Vision for Ingredient Detection

**File**: `computer_vision.py`

**Features**:
- YOLOv8 object detection for food items
- Google Vision API integration
- Food classification using Hugging Face models
- Fallback simulation mode for development

**Usage**:
```python
from computer_vision import detect_ingredients_from_image

# Detect ingredients from image
detections = detect_ingredients_from_image('path/to/image.jpg')
for detection in detections:
    print(f"Found: {detection['name']} (confidence: {detection['confidence']:.2f})")
```

### 2. Image Processing Libraries

**File**: `image_processor.py`

**Features**:
- Advanced image preprocessing pipeline
- Noise reduction and enhancement
- Food region extraction
- Image quality optimization

**Usage**:
```python
from image_processor import create_image_processor

processor = create_image_processor()
processed_image = processor.preprocess_image('image.jpg', enhance=True)
food_regions = processor.extract_food_regions(processed_image)
```

### 3. Natural Language Processing

**File**: `nlp_processor.py`

**Features**:
- Ingredient name standardization
- Fuzzy string matching
- Synonym resolution
- Quantity and unit extraction

**Usage**:
```python
from nlp_processor import create_ingredient_standardizer

standardizer = create_ingredient_standardizer()
result = standardizer.standardize_ingredient("2 cups fresh tomatoes, diced")
print(f"Standardized: {result['standardized_name']}")
print(f"Quantity: {result['quantity']} {result['unit']}")
```

### 4. Recommendation System

**File**: `recommendation_system.py`

**Features**:
- Content-based filtering
- Collaborative filtering (foundation)
- User preference learning
- Recipe similarity scoring

**Usage**:
```python
from recommendation_system import get_recipe_recommendations

recommendations = get_recipe_recommendations(
    recipes=recipe_database,
    user_ingredients=['chicken', 'tomatoes', 'onion'],
    user_preferences={'dietary_preferences': ['non-veg']},
    limit=10
)
```

## 📁 File Structure

```
MealMind/
├── ml_config.py              # ML configuration and settings
├── computer_vision.py        # Computer vision for ingredient detection
├── image_processor.py        # Image preprocessing and enhancement
├── nlp_processor.py          # NLP for ingredient standardization
├── recommendation_system.py  # Recipe recommendation engine
├── ingredient_ml.py          # Enhanced ML integration layer
├── setup_ml.py              # Setup and installation script
├── requirements.txt         # Updated dependencies
├── models/                  # Downloaded ML models
├── data/                    # Data storage
├── uploads/                 # User uploaded images
└── logs/                    # Application logs
```

## 🔧 Configuration

### API Keys (Optional)

Create a `.env` file with your API keys:

```bash
# Google Cloud Vision API
GOOGLE_VISION_API_KEY=your_key_here
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json

# Azure Computer Vision
AZURE_VISION_ENDPOINT=your_endpoint_here
AZURE_VISION_KEY=your_key_here

# Hugging Face
HUGGINGFACE_API_KEY=your_key_here
```

### ML Settings

Edit `ml_config.py` to customize:
- Model paths and URLs
- Processing parameters
- Feature engineering settings
- Recommendation weights

## 🛠️ Dependencies

### Core ML Libraries
- **torch**: PyTorch for deep learning models
- **ultralytics**: YOLOv8 for object detection
- **opencv-python**: Computer vision operations
- **scikit-learn**: Machine learning algorithms
- **pandas**: Data manipulation
- **numpy**: Numerical computing

### NLP Libraries
- **spacy**: Advanced NLP processing
- **transformers**: Hugging Face models
- **fuzzywuzzy**: Fuzzy string matching
- **sentence-transformers**: Text embeddings

### Image Processing
- **Pillow**: Image manipulation
- **albumentations**: Data augmentation
- **scikit-image**: Image processing algorithms

## 🔄 API Endpoints

### Enhanced Endpoints

1. **Image Analysis**:
   ```
   POST /api/upload-ingredient-image
   ```
   - Upload image for ingredient detection
   - Returns detailed analysis with confidence scores

2. **Smart Suggestions**:
   ```
   GET /api/ingredient-suggestions?q=partial_name
   ```
   - NLP-powered ingredient suggestions
   - Fuzzy matching and synonym resolution

3. **ML Recommendations**:
   ```
   POST /api/recipe-recommendations
   ```
   - Advanced recipe recommendations
   - Content-based and collaborative filtering

## 🧪 Testing

### Test ML Components
```bash
python -c "
from ingredient_ml import analyze_ingredient_image
result = analyze_ingredient_image('test_image.jpg')
print(result)
"
```

### Test Individual Modules
```bash
# Test computer vision
python -c "from computer_vision import create_ingredient_detector; detector = create_ingredient_detector(use_mock=True); print('CV OK')"

# Test NLP
python -c "from nlp_processor import create_ingredient_standardizer; nlp = create_ingredient_standardizer(); print('NLP OK')"

# Test recommendations
python -c "from recommendation_system import create_recommendation_system; rec = create_recommendation_system(); print('Rec OK')"
```

## 🎯 Performance Modes

### 1. Full ML Mode
- All advanced features enabled
- Requires ML dependencies
- Best accuracy and features

### 2. Fallback Mode
- Basic functionality only
- No external dependencies
- Simulation-based detection

### 3. Hybrid Mode
- Some ML features enabled
- Graceful degradation
- Configurable components

## 📊 Model Information

### YOLOv8 Nano
- **Size**: ~6MB
- **Speed**: Fast inference
- **Accuracy**: Good for general objects
- **Use**: Object detection in images

### spaCy en_core_web_sm
- **Size**: ~15MB
- **Features**: POS tagging, NER, lemmatization
- **Use**: Text processing and standardization

### Sentence Transformers
- **Model**: all-MiniLM-L6-v2
- **Size**: ~90MB
- **Use**: Text embeddings for similarity

## 🔍 Troubleshooting

### Common Issues

1. **Import Errors**:
   ```bash
   pip install -r requirements.txt
   python setup_ml.py
   ```

2. **spaCy Model Missing**:
   ```bash
   python -m spacy download en_core_web_sm
   ```

3. **YOLO Model Download**:
   ```bash
   python -c "from ultralytics import YOLO; YOLO('yolov8n.pt')"
   ```

4. **Memory Issues**:
   - Reduce batch sizes in `ml_config.py`
   - Use CPU-only mode for development

### Fallback Mode
If ML components fail, the system automatically falls back to simulation mode:
- Basic ingredient detection
- Simple string matching
- Rule-based recommendations

## 🚀 Future Enhancements

### Planned Features
1. **Custom Food Detection Model**
   - Train on food-specific dataset
   - Better ingredient recognition
   - Nutrition estimation

2. **Advanced Collaborative Filtering**
   - User behavior tracking
   - Matrix factorization
   - Deep learning recommendations

3. **Multi-language Support**
   - Ingredient names in multiple languages
   - Recipe translation
   - Cultural cuisine preferences

4. **Real-time Processing**
   - Streaming ingredient detection
   - Live recipe suggestions
   - Mobile app integration

## 📝 Contributing

To add new ML features:

1. **Add Configuration**: Update `ml_config.py`
2. **Implement Module**: Create new `.py` file
3. **Integrate**: Update `ingredient_ml.py`
4. **Test**: Add tests and documentation
5. **API**: Add new endpoints in `app.py`

## 📄 License

This ML implementation is part of the MealMind project and follows the same license terms.
