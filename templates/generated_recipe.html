<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ recipe.name }} - AI Generated Recipe | MealMind</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='organic-overrides.css') }}">
</head>
<body class="bg-organic-beige min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-rich-green/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="{{ url_for('index') }}" class="flex items-center">
                        <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind" class="h-8 w-auto">
                        <span class="ml-2 text-xl font-serif font-bold text-rich-green">MealMind</span>
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ url_for('smart_recipes') }}" class="text-soft-gray hover:text-rich-green transition-colors">
                        ← Back to Smart Recipes
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- AI Generated Badge -->
        <div class="text-center mb-6">
            <div class="inline-flex items-center bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-3 rounded-full shadow-lg">
                <span class="text-2xl mr-2">��</span>
                <span class="font-semibold">AI Generated Recipe</span>
                <span class="text-2xl ml-2">✨</span>
            </div>
        </div>

        <!-- Recipe Header -->
        <div class="bg-white rounded-3xl shadow-xl overflow-hidden mb-8">
            <!-- Recipe Info -->
            <div class="p-8">
                <h1 class="text-3xl md:text-4xl font-serif font-bold text-rich-green mb-4">{{ recipe.name }}</h1>
                
                <!-- Recipe Meta -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div class="text-center p-4 bg-light-sage rounded-xl">
                        <div class="text-2xl mb-1">⏱️</div>
                        <div class="text-sm text-soft-gray">Prep Time</div>
                        <div class="font-semibold text-rich-green">{{ recipe.prep_time }}</div>
                    </div>
                    <div class="text-center p-4 bg-light-sage rounded-xl">
                        <div class="text-2xl mb-1">🔥</div>
                        <div class="text-sm text-soft-gray">Cook Time</div>
                        <div class="font-semibold text-rich-green">{{ recipe.get('cook_time', '20 minutes') }}</div>
                    </div>
                    <div class="text-center p-4 bg-light-sage rounded-xl">
                        <div class="text-2xl mb-1">👥</div>
                        <div class="text-sm text-soft-gray">Servings</div>
                        <div class="font-semibold text-rich-green">{{ recipe.servings }}</div>
                    </div>
                    <div class="text-center p-4 bg-light-sage rounded-xl">
                        <div class="text-2xl mb-1">📊</div>
                        <div class="text-sm text-soft-gray">Difficulty</div>
                        <div class="font-semibold text-rich-green">{{ recipe.difficulty }}</div>
                    </div>
                </div>

                <!-- Cuisine and Diet Info -->
                <div class="flex flex-wrap gap-3 mb-6">
                    <span class="bg-rich-green text-white px-4 py-2 rounded-full text-sm font-medium">
                        🌍 {{ recipe.cuisine }} Cuisine
                    </span>
                    <span class="bg-soft-green text-white px-4 py-2 rounded-full text-sm font-medium">
                        🥗 {{ recipe.diet_type }}
                    </span>
                    {% if recipe.get('flavor_profile') %}
                    <span class="bg-accent-green text-white px-4 py-2 rounded-full text-sm font-medium">
                        👅 {{ recipe.flavor_profile }}
                    </span>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recipe Content -->
        <div class="grid lg:grid-cols-3 gap-8">
            <!-- Ingredients -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-2xl p-6 shadow-lg h-fit">
                    <h3 class="text-xl font-serif font-bold text-rich-green mb-4 flex items-center">
                        <span class="text-2xl mr-2">🥗</span>
                        Ingredients
                    </h3>
                    <div class="space-y-3">
                        {% for ingredient in recipe.ingredients %}
                        <div class="flex items-center space-x-3 p-3 bg-light-sage rounded-lg">
                            <div class="w-2 h-2 bg-rich-green rounded-full flex-shrink-0"></div>
                            <span class="text-rich-green font-medium">{{ ingredient }}</span>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <!-- Nutrition Info -->
                    <div class="mt-6 p-4 bg-gradient-to-r from-light-sage to-soft-green/20 rounded-xl">
                        <h4 class="font-semibold text-rich-green mb-2">Nutrition (per serving)</h4>
                        <div class="text-sm text-soft-gray">
                            <div class="flex justify-between">
                                <span>Calories:</span>
                                <span class="font-medium">{{ recipe.calories }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Instructions -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-2xl p-6 shadow-lg">
                    <h3 class="text-xl font-serif font-bold text-rich-green mb-6 flex items-center">
                        <span class="text-2xl mr-2">👨‍🍳</span>
                        AI-Generated Instructions
                    </h3>

                    <div class="space-y-4">
                        {% for instruction in recipe.instructions %}
                        <div class="flex items-start space-x-4">
                            <div class="bg-rich-green text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold flex-shrink-0">
                                {{ loop.index }}
                            </div>
                            <p class="text-soft-gray leading-relaxed">{{ instruction }}</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- AI Cooking Tips -->
                {% if recipe.get('cooking_tips') %}
                <div class="bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-6 shadow-lg mt-6">
                    <h3 class="text-xl font-serif font-bold text-rich-green mb-4 flex items-center">
                        <span class="text-2xl mr-2">💡</span>
                        AI Cooking Tips
                    </h3>
                    <div class="space-y-2">
                        {% for tip in recipe.cooking_tips %}
                        <div class="flex items-start space-x-3">
                            <span class="text-purple-500 mt-1">•</span>
                            <span class="text-soft-gray">{{ tip }}</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-8 text-center space-x-4">
            <a href="{{ url_for('smart_recipes') }}" class="bg-accent-green text-white px-6 py-3 rounded-full font-semibold hover:bg-soft-green transition-colors shadow-lg inline-block">
                🔄 Generate Another Recipe
            </a>
        </div>

        <!-- AI Attribution -->
        <div class="mt-8 text-center">
            <div class="bg-gradient-to-r from-purple-100 to-blue-100 rounded-xl p-4 inline-block">
                <p class="text-sm text-soft-gray">
                    <span class="text-lg">🤖</span>
                    This recipe was intelligently generated by MealMind AI using only your specified ingredients.
                    <br>
                    <span class="font-medium">Cooking time and techniques optimized for {{ recipe.cuisine }} cuisine.</span>
                </p>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-rich-green text-organic-beige py-8 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <p>&copy; 2024 MealMind. Powered by AI for personalized cooking experiences.</p>
        </div>
    </footer>
</body>
</html>
