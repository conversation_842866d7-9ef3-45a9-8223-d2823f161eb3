<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - MealMind</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'organic-beige': '#F9F5ED',
                        'organic-cream': '#FDF9F0',
                        'rich-green': '#1A5E2A',
                        'soft-green': '#2D7A3D',
                        'accent-green': '#4A9B5E',
                        'warm-brown': '#8B4513',
                        'soft-gray': '#6B7280',
                        'light-sage': '#E8F5E8'
                    },
                    fontFamily: {
                        'serif': ['Playfair Display', 'serif'],
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom organic overrides -->
    <link rel="stylesheet" href="{{ url_for('static', filename='organic-overrides.css') }}">

    <!-- Enhanced UI Effects -->
    <link rel="stylesheet" href="{{ url_for('static', filename='enhanced-ui.css') }}">

    <!-- Prevent page from becoming dull on back button -->
    <style>
        body {
            opacity: 1 !important;
            filter: none !important;
            transition: none !important;
        }

        html, body {
            background-color: #F9F5ED !important;
        }

        * {
            opacity: 1 !important;
            filter: none !important;
        }

        body.bfcache {
            opacity: 1 !important;
            filter: none !important;
        }
    </style>

    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍽️</text></svg>">
</head>
<body class="bg-organic-beige font-sans text-rich-green min-h-screen transition-all duration-500" id="body">
    <!-- Parallax Background -->
    <div class="parallax-bg"></div>

    <!-- Theme Toggle Button -->
    <button class="theme-toggle" onclick="toggleTheme()" title="Toggle Dark/Light Mode">
        <span id="theme-icon">🌙</span>
    </button>
    <!-- Header -->
    <header class="bg-organic-cream/80 backdrop-blur-sm border-b border-rich-green/10 sticky top-0 z-50">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Brand -->
                <div class="flex items-center space-x-3">
                    <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-12 w-12">
                    <div>
                        <h1 class="text-2xl font-serif font-bold text-rich-green tracking-tight">MealMind</h1>
                        <p class="text-xs text-soft-gray font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="{{ url_for('index') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Home</a>
                    <a href="{{ url_for('browse_recipes') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Discover</a>
                    <a href="{{ url_for('smart_recipes') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Smart Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                    <a href="{{ url_for('profile') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium border-b-2 border-rich-green">Profile</a>
                    <a href="{{ url_for('logout') }}" class="bg-rich-green text-organic-beige px-6 py-2 rounded-full hover:bg-soft-green transition-colors font-medium">Logout</a>
                </div>

                <!-- Mobile menu button -->
                <button class="md:hidden text-rich-green hover:text-soft-green" id="mobile-menu-toggle">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div class="md:hidden hidden" id="mobile-menu">
                <div class="px-2 pt-2 pb-3 space-y-1 bg-organic-cream border-t border-rich-green/10">
                    <a href="{{ url_for('index') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Home</a>
                    <a href="{{ url_for('browse_recipes') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Discover</a>
                    <a href="{{ url_for('smart_recipes') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Smart Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                    <a href="{{ url_for('profile') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium bg-light-sage rounded">Profile</a>
                    <a href="{{ url_for('logout') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Logout</a>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <!-- Profile Header -->
        <section class="py-12 lg:py-16 bg-organic-cream">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="neuro-container p-8 tilt-card">
                    <div class="flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8">
                        <!-- Avatar -->
                        <div class="relative">
                            <div class="w-24 h-24 bg-rich-green rounded-full flex items-center justify-center text-organic-beige text-3xl font-serif font-bold">
                                {{ user.full_name[0].upper() }}
                            </div>
                            <button class="absolute -bottom-2 -right-2 bg-soft-green text-organic-beige w-8 h-8 rounded-full hover:bg-rich-green transition-colors">
                                📷
                            </button>
                        </div>

                        <!-- Profile Info -->
                        <div class="flex-1 text-center md:text-left">
                            <h1 id="header-full-name" class="text-3xl font-serif font-bold text-rich-green mb-2">{{ user.full_name }}</h1>
                            <p id="header-username" class="text-lg text-soft-gray mb-1">@{{ user.username }}</p>
                            <p class="text-sm text-soft-gray">Member since {{ user.created_at[:7].replace('-', '/') if user.created_at else 'Recently' }}</p>
                        </div>

                        <!-- Stats -->
                        <div class="grid grid-cols-3 gap-6 text-center">
                            <div>
                                <div class="text-2xl font-serif font-bold text-rich-green">{{ favorite_recipes|length }}</div>
                                <div class="text-sm text-soft-gray">Favorites</div>
                            </div>
                            <div>
                                <div class="text-2xl font-serif font-bold text-rich-green">{{ profile.favorite_cuisines|length if profile.favorite_cuisines else 0 }}</div>
                                <div class="text-sm text-soft-gray">Cuisines</div>
                            </div>
                            <div>
                                <div class="text-2xl font-serif font-bold text-rich-green">{{ profile.cooking_skill if profile.cooking_skill else 'Beginner' }}</div>
                                <div class="text-sm text-soft-gray">Level</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Profile Content -->
        <section class="py-12 bg-organic-beige">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid lg:grid-cols-4 gap-8">
                    <!-- Sidebar -->
                    <div class="lg:col-span-1">
                        <div class="neuro-card p-6 sticky top-24">
                            <h3 class="text-lg font-serif font-semibold text-rich-green mb-4 text-center">Profile Menu</h3>
                            <nav class="space-y-2">
                                <button class="w-full text-left px-4 py-3 rounded-lg bg-light-sage text-rich-green font-medium" data-tab="account" onclick="showSection('account')">
                                    👤 Account Information
                                </button>
                                <button class="w-full text-left px-4 py-3 rounded-lg text-soft-gray hover:bg-light-sage hover:text-rich-green transition-colors" data-tab="preferences" onclick="showSection('preferences')">
                                    ⚙️ Preferences
                                </button>
                                <button class="w-full text-left px-4 py-3 rounded-lg text-soft-gray hover:bg-light-sage hover:text-rich-green transition-colors" data-tab="dietary" onclick="showSection('dietary')">
                                    🥗 Dietary Information
                                </button>
                                <button class="w-full text-left px-4 py-3 rounded-lg text-soft-gray hover:bg-light-sage hover:text-rich-green transition-colors" data-tab="favorites" onclick="showSection('favorites')">
                                    ❤️ Favorite Recipes
                                </button>
                            </nav>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <div class="lg:col-span-3">
                        <!-- Account Information Section -->
                        <div class="section-content" id="account-section">
                            <div class="neuro-container p-8 mb-8 tilt-card">
                                <div class="mb-8">
                                    <h2 class="text-2xl font-serif font-bold text-rich-green mb-2">👤 Account Information</h2>
                                    <p class="text-soft-gray">Manage your personal details</p>
                                </div>

                                <form class="space-y-6">
                                    <div class="grid md:grid-cols-2 gap-6">
                                        <div>
                                            <label class="block text-sm font-semibold text-rich-green mb-2">Full Name</label>
                                            <input id="account-full-name" type="text" value="{{ user.full_name }}" class="w-full p-3 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none" onchange="updateHeaderInfo()">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-semibold text-rich-green mb-2">Username</label>
                                            <input id="account-username" type="text" value="{{ user.username }}" class="w-full p-3 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none" onchange="updateHeaderInfo()">
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-semibold text-rich-green mb-2">Email Address</label>
                                        <input id="account-email" type="email" value="{{ user.email }}" class="w-full p-3 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-semibold text-rich-green mb-2">Member Since</label>
                                        <input type="text" value="{{ user.created_at[:7].replace('-', '/') if user.created_at else 'Recently' }}" readonly class="w-full p-3 border border-rich-green/20 rounded-xl bg-gray-50 text-gray-500">
                                    </div>
                                </form>

                                <!-- Account Action Buttons -->
                                <div class="flex flex-col sm:flex-row gap-4 mt-8 pt-6 border-t border-rich-green/10">
                                    <button onclick="updateAccount()" class="flex-1 bg-rich-green text-organic-beige py-3 px-6 rounded-xl font-semibold hover:bg-soft-green transition-colors">
                                        ✅ Update Account
                                    </button>
                                    <button onclick="deleteAccount()" class="flex-1 bg-red-500 text-white py-3 px-6 rounded-xl font-semibold hover:bg-red-600 transition-colors">
                                        🗑️ Delete Account
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Preferences Section -->
                        <div class="section-content hidden" id="preferences-section">
                            <div class="neuro-container p-8 mb-8 tilt-card">
                                <div class="mb-8">
                                    <h2 class="text-2xl font-serif font-bold text-rich-green mb-2">⚙️ Preferences</h2>
                                    <p class="text-soft-gray">Customize your cooking experience</p>
                                </div>

                                <form class="space-y-8">
                                    <div>
                                        <h3 class="text-lg font-serif font-semibold text-rich-green mb-4">🍽️ Favorite Cuisines</h3>
                                        <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                            {% for cuisine in ['Italian', 'Indian', 'Chinese', 'Japanese', 'Korean', 'American', 'Thai', 'Mediterranean', 'Fusion'] %}
                                            <label class="flex items-center space-x-2 p-3 border border-rich-green/20 rounded-xl hover:bg-light-sage cursor-pointer">
                                                <input type="checkbox" name="favorite_cuisines" value="{{ cuisine }}" {% if cuisine in profile.favorite_cuisines %}checked{% endif %} class="text-rich-green focus:ring-rich-green">
                                                <span class="text-sm font-medium text-rich-green">{{ cuisine }}</span>
                                            </label>
                                            {% endfor %}
                                        </div>
                                    </div>

                                    <div>
                                        <h3 class="text-lg font-serif font-semibold text-rich-green mb-4">👨‍🍳 Cooking Skill Level</h3>
                                        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                                            {% for skill in ['Beginner', 'Intermediate', 'Advanced', 'Expert'] %}
                                            <label class="skill-option flex items-center justify-center p-4 border border-rich-green/20 rounded-xl hover:bg-light-sage cursor-pointer transition-colors {% if skill == profile.cooking_skill %}bg-light-sage border-rich-green{% endif %}">
                                                <input type="radio" name="cooking_skill" value="{{ skill }}" {% if skill == profile.cooking_skill %}checked{% endif %} class="hidden">
                                                <span class="text-sm font-medium text-rich-green">{{ skill }}</span>
                                            </label>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </form>

                                <!-- Preferences Action Button -->
                                <div class="mt-8 pt-6 border-t border-rich-green/10">
                                    <button onclick="savePreferences()" class="w-full bg-rich-green text-organic-beige py-3 px-6 rounded-xl font-semibold hover:bg-soft-green transition-colors">
                                        💾 Save Preferences
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Dietary Information Section -->
                        <div class="section-content hidden" id="dietary-section">
                            <div class="neuro-container p-8 mb-8 tilt-card">
                                <div class="mb-8">
                                    <h2 class="text-2xl font-serif font-bold text-rich-green mb-2">🥗 Dietary Information</h2>
                                    <p class="text-soft-gray">Help us suggest better recipes for you</p>
                                </div>

                                <form class="space-y-8">
                                    <div>
                                        <h3 class="text-lg font-serif font-semibold text-rich-green mb-4">🌱 Dietary Preferences</h3>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                            {% for diet in ['Vegetarian', 'Vegan', 'Gluten-Free', 'Dairy-Free', 'Keto', 'Paleo', 'Low-Carb', 'High-Protein'] %}
                                            <label class="flex items-center space-x-2 p-3 border border-rich-green/20 rounded-xl hover:bg-light-sage cursor-pointer">
                                                <input type="checkbox" name="dietary_preferences" value="{{ diet }}" {% if diet in profile.dietary_preferences %}checked{% endif %} class="text-rich-green focus:ring-rich-green">
                                                <span class="text-sm font-medium text-rich-green">{{ diet }}</span>
                                            </label>
                                            {% endfor %}
                                        </div>
                                    </div>

                                    <div>
                                        <h3 class="text-lg font-serif font-semibold text-rich-green mb-4">🚫 Allergies & Restrictions</h3>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                            {% for allergy in ['Nuts', 'Shellfish', 'Eggs', 'Dairy', 'Soy', 'Wheat', 'Fish', 'Sesame'] %}
                                            <label class="flex items-center space-x-2 p-3 border border-rich-green/20 rounded-xl hover:bg-light-sage cursor-pointer">
                                                <input type="checkbox" name="allergies" value="{{ allergy }}" {% if allergy in profile.allergies %}checked{% endif %} class="text-rich-green focus:ring-rich-green">
                                                <span class="text-sm font-medium text-rich-green">{{ allergy }}</span>
                                            </label>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </form>

                                <!-- Dietary Info Action Button -->
                                <div class="mt-8 pt-6 border-t border-rich-green/10">
                                    <button onclick="saveDietaryInfo()" class="w-full bg-rich-green text-organic-beige py-3 px-6 rounded-xl font-semibold hover:bg-soft-green transition-colors">
                                        🥗 Save Dietary Information
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Favorite Recipes Section -->
                        <div class="section-content hidden" id="favorites-section">
                            <div class="neuro-container p-8 mb-8 tilt-card">
                                <div class="mb-8">
                                    <h2 class="text-2xl font-serif font-bold text-rich-green mb-2">❤️ Favorite Recipes</h2>
                                    <p class="text-soft-gray">Recipes you've saved for later</p>
                                </div>

                                {% if favorite_recipes %}
                                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    {% for recipe in favorite_recipes %}
                                    <div class="bg-organic-beige rounded-xl p-6 border border-rich-green/10 hover:border-rich-green/20 transition-colors">
                                        <div class="flex justify-between items-start mb-3">
                                            <h3 class="text-lg font-serif font-semibold text-rich-green">{{ recipe.name }}</h3>
                                            <button class="text-red-500 hover:text-red-700 transition-colors" onclick="toggleFavorite({{ recipe.id }})">💔</button>
                                        </div>
                                        <div class="flex flex-wrap gap-2 mb-4">
                                            <span class="bg-light-sage/30 text-rich-green px-3 py-1 rounded-full text-sm font-medium">{{ recipe.cuisine }}</span>
                                            <span class="bg-light-sage/30 text-rich-green px-3 py-1 rounded-full text-sm font-medium">{{ recipe.diet_type }}</span>
                                        </div>
                                        <div class="grid grid-cols-3 gap-2 text-sm text-soft-gray mb-4">
                                            <div>⏱️ {{ recipe.prep_time }}m</div>
                                            <div>🔥 {{ recipe.calories }}cal</div>
                                            <div>📊 {{ recipe.difficulty }}</div>
                                        </div>
                                        <a href="{{ url_for('recipe_detail', recipe_id=recipe.id) }}"
                                           class="w-full bg-rich-green text-organic-beige py-2 px-4 rounded-lg text-sm font-medium hover:bg-soft-green transition-colors text-center block">
                                            View Recipe
                                        </a>
                                    </div>
                                    {% endfor %}
                                </div>
                                {% else %}
                                <div class="text-center py-12">
                                    <div class="text-6xl mb-4">💔</div>
                                    <h3 class="text-xl font-serif font-semibold text-rich-green mb-2">No favorite recipes yet</h3>
                                    <p class="text-soft-gray mb-6">Start exploring recipes and save your favorites!</p>
                                </div>
                                {% endif %}

                                <!-- Favorites Action Button -->
                                <div class="mt-8 pt-6 border-t border-rich-green/10">
                                    <a href="{{ url_for('browse_recipes') }}" class="block w-full bg-light-sage text-rich-green py-3 px-6 rounded-xl font-semibold hover:bg-rich-green hover:text-organic-beige transition-colors text-center">
                                        🔍 Discover More Recipes
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>



    <!-- Footer -->
    <footer class="bg-rich-green text-organic-beige py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand Section -->
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-10 w-10">
                        <div>
                            <h3 class="text-2xl font-serif font-bold">MealMind</h3>
                            <p class="text-sm text-organic-beige/80 font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                        </div>
                    </div>
                    <p class="text-organic-beige/80 mb-4 max-w-md">
                        Discover delicious recipes crafted with quality ingredients. Transform your cooking experience with AI-powered recipe discovery.
                    </p>
                    <div class="flex space-x-4">
                        <span class="text-2xl">🍅</span>
                        <span class="text-2xl">🥕</span>
                        <span class="text-2xl">🌶️</span>
                        <span class="text-2xl">🥬</span>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li><a href="{{ url_for('index') }}" class="hover:text-organic-beige transition-colors">Home</a></li>
                        <li><a href="{{ url_for('browse_recipes') }}" class="hover:text-organic-beige transition-colors">Browse Recipes</a></li>
                        <li><a href="{{ url_for('meal_plan') }}" class="hover:text-organic-beige transition-colors">Meal Plan</a></li>
                        {% if user %}
                        <li><a href="{{ url_for('favorites') }}" class="hover:text-organic-beige transition-colors">My Favorites</a></li>
                        {% else %}
                        <li><a href="{{ url_for('register') }}" class="hover:text-organic-beige transition-colors">Join Free</a></li>
                        {% endif %}
                    </ul>
                </div>

                <!-- Features -->
                <div>
                    <h4 class="font-semibold mb-4">Features</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li>🤖 AI Recipe Discovery</li>
                        <li>📸 Photo Recognition</li>
                        <li>🥗 Diet Filtering</li>
                        <li>⏱️ Time-based Search</li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-organic-beige/20 mt-8 pt-8 text-center">
                <p class="text-organic-beige/60 text-sm">
                    © 2025 MealMind. Made with 💚 for food lovers everywhere.
                </p>
            </div>
        </div>
    </footer>


    <script src="{{ url_for('static', filename='script.js') }}"></script>
    <script>
        // Section navigation
        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.section-content').forEach(section => {
                section.classList.add('hidden');
            });

            // Remove active class from all menu buttons
            document.querySelectorAll('[data-tab]').forEach(button => {
                button.classList.remove('bg-light-sage', 'text-rich-green', 'font-medium');
                button.classList.add('text-soft-gray');
            });

            // Show selected section
            document.getElementById(sectionName + '-section').classList.remove('hidden');

            // Add active class to clicked button
            const activeButton = document.querySelector(`[data-tab="${sectionName}"]`);
            activeButton.classList.add('bg-light-sage', 'text-rich-green', 'font-medium');
            activeButton.classList.remove('text-soft-gray');
        }

        // Update header information in real-time
        function updateHeaderInfo() {
            const fullNameInput = document.getElementById('account-full-name');
            const usernameInput = document.getElementById('account-username');
            const headerFullName = document.getElementById('header-full-name');
            const headerUsername = document.getElementById('header-username');

            if (fullNameInput && headerFullName) {
                headerFullName.textContent = fullNameInput.value || 'Your Name';
            }

            if (usernameInput && headerUsername) {
                headerUsername.textContent = '@' + (usernameInput.value || 'username');
            }
        }

        // Action button functions
        function savePreferences() {
            const formData = new FormData();

            // Collect favorite cuisines
            const cuisines = [];
            document.querySelectorAll('input[name="favorite_cuisines"]:checked').forEach(input => {
                cuisines.push(input.value);
            });

            // Collect cooking skill
            const skill = document.querySelector('input[name="cooking_skill"]:checked')?.value || 'Beginner';

            const profileData = {
                favorite_cuisines: cuisines,
                cooking_skill: skill
            };

            fetch('/update-profile', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ profile_info: profileData })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Preferences saved successfully!', 'success');
                } else {
                    showNotification('Error saving preferences', 'error');
                }
            })
            .catch(error => {
                showNotification('Error saving preferences', 'error');
            });
        }

        function saveDietaryInfo() {
            // Collect dietary preferences
            const dietaryPrefs = [];
            document.querySelectorAll('input[name="dietary_preferences"]:checked').forEach(input => {
                dietaryPrefs.push(input.value);
            });

            // Collect allergies
            const allergies = [];
            document.querySelectorAll('input[name="allergies"]:checked').forEach(input => {
                allergies.push(input.value);
            });

            const profileData = {
                dietary_preferences: dietaryPrefs,
                allergies: allergies
            };

            fetch('/update-profile', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ profile_info: profileData })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Dietary information saved successfully!', 'success');
                } else {
                    showNotification('Error saving dietary information', 'error');
                }
            })
            .catch(error => {
                showNotification('Error saving dietary information', 'error');
            });
        }

        function updateAccount() {
            const fullName = document.getElementById('account-full-name').value;
            const username = document.getElementById('account-username').value;
            const email = document.getElementById('account-email').value;

            const userData = {
                full_name: fullName,
                username: username,
                email: email
            };

            fetch('/update-profile', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ user_info: userData })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update header immediately after successful save
                    updateHeaderInfo();
                    showNotification('Account updated successfully!', 'success');
                } else {
                    showNotification('Error updating account', 'error');
                }
            })
            .catch(error => {
                showNotification('Error updating account', 'error');
            });
        }

        function deleteAccount() {
            if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
                if (confirm('This will permanently delete all your data. Are you absolutely sure?')) {
                    // Implement account deletion
                    showNotification('Account deletion feature coming soon', 'info');
                }
            }
        }

        // Handle cooking skill selection and real-time updates
        document.addEventListener('DOMContentLoaded', function() {
            // Handle skill level radio buttons
            document.querySelectorAll('.skill-option').forEach(label => {
                label.addEventListener('click', function() {
                    // Remove active class from all skill options
                    document.querySelectorAll('.skill-option').forEach(option => {
                        option.classList.remove('bg-light-sage', 'border-rich-green');
                        option.classList.add('border-rich-green/20');
                    });

                    // Add active class to clicked option
                    this.classList.add('bg-light-sage', 'border-rich-green');
                    this.classList.remove('border-rich-green/20');

                    // Check the radio button
                    const radio = this.querySelector('input[type="radio"]');
                    radio.checked = true;
                });
            });

            // Add real-time input listeners for header updates
            const fullNameInput = document.getElementById('account-full-name');
            const usernameInput = document.getElementById('account-username');

            if (fullNameInput) {
                fullNameInput.addEventListener('input', updateHeaderInfo);
            }

            if (usernameInput) {
                usernameInput.addEventListener('input', updateHeaderInfo);
            }
        });

        // Mobile menu toggle
        document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Toggle favorite functionality for profile page
        function toggleFavorite(recipeId) {
            if (confirm('Remove this recipe from your favorites?')) {
                fetch('/api/toggle-favorite', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ recipe_id: recipeId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload the page to update the favorites list
                        location.reload();
                    } else {
                        alert('Error removing from favorites: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error removing from favorites');
                });
            }
        }
    </script>

    <!-- Flash messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <script>
                {% for category, message in messages %}
                    showNotification('{{ message }}', '{{ category }}');
                {% endfor %}
            </script>
        {% endif %}
    {% endwith %}

    <!-- Prevent page from becoming dull on back button -->
    <script>
        window.addEventListener('pageshow', function(event) {
            if (event.persisted) {
                document.body.style.opacity = '1';
                document.body.style.filter = 'none';
                document.body.style.transition = 'none';
            }
        });

        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                document.body.style.opacity = '1';
                document.body.style.filter = 'none';
            }
        });

        window.addEventListener('load', function() {
            document.body.style.opacity = '1';
            document.body.style.filter = 'none';
            document.body.style.transition = 'none';
        });

        document.body.style.opacity = '1';
        document.body.style.filter = 'none';
        document.body.style.transition = 'none';
    </script>
</body>
</html>
