<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ recipe.name }} - MealMind</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'organic-beige': '#F9F5ED',
                        'organic-cream': '#FDF9F0',
                        'rich-green': '#1A5E2A',
                        'soft-green': '#2D7A3D',
                        'accent-green': '#4A9B5E',
                        'warm-brown': '#8B4513',
                        'soft-gray': '#6B7280',
                        'light-sage': '#E8F5E8'
                    },
                    fontFamily: {
                        'serif': ['Playfair Display', 'serif'],
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom organic overrides -->
    <link rel="stylesheet" href="{{ url_for('static', filename='organic-overrides.css') }}">

    <!-- Enhanced UI Effects -->
    <link rel="stylesheet" href="{{ url_for('static', filename='enhanced-ui.css') }}">

    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍽️</text></svg>">
</head>
<body class="bg-organic-beige font-sans text-rich-green min-h-screen transition-all duration-500" id="body">
    <!-- Parallax Background -->
    <div class="parallax-bg"></div>

    <!-- Theme Toggle Button -->
    <button class="theme-toggle" onclick="toggleTheme()" title="Toggle Dark/Light Mode">
        <span id="theme-icon">🌙</span>
    </button>

    <!-- Header -->
    <header class="neuro-nav sticky top-0 z-50">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="{{ url_for('index') }}" class="text-2xl font-serif font-bold text-rich-green">
                        🍽️ MealMind
                    </a>
                    <span class="ml-2 text-sm text-soft-gray font-medium">Think Smart. Eat Better.</span>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="{{ url_for('index') }}" class="neuro-nav-item px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Home</a>
                    <a href="{{ url_for('browse_recipes') }}" class="neuro-nav-item active px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Discover</a>
                    <a href="{{ url_for('smart_recipes') }}" class="neuro-nav-item px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Smart Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="neuro-nav-item px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                    {% if session.user_id %}
                    <a href="{{ url_for('profile') }}" class="neuro-nav-item px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Profile</a>
                    <a href="{{ url_for('logout') }}" class="neuro-nav-item px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Logout</a>
                    {% else %}
                    <a href="{{ url_for('login') }}" class="neuro-nav-item px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Login</a>
                    {% endif %}
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-toggle" class="neuro-btn p-2 text-rich-green">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="hidden md:hidden pb-4">
                <div class="space-y-2">
                    <a href="{{ url_for('index') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Home</a>
                    <a href="{{ url_for('browse_recipes') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Discover</a>
                    <a href="{{ url_for('smart_recipes') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Smart Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                    {% if session.user_id %}
                    <a href="{{ url_for('profile') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Profile</a>
                    <a href="{{ url_for('logout') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Logout</a>
                    {% else %}
                    <a href="{{ url_for('login') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Login</a>
                    {% endif %}
                </div>
            </div>
        </nav>
    </header>

    <main>
        <!-- Recipe Header -->
        <section class="pt-8 pb-12 neuro-container mx-4 my-8">
            <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h1 class="text-3xl lg:text-4xl font-serif font-bold text-rich-green mb-6 float-element">{{ recipe.name }}</h1>

                <!-- Recipe Overview Cards -->
                <div class="grid md:grid-cols-4 gap-4 mb-8">
                    <div class="neuro-flat p-4 rounded-xl tilt-card">
                        <div class="text-2xl mb-2">⏱️</div>
                        <div class="text-xl font-serif font-bold text-rich-green">{{ recipe.prep_time }}</div>
                        <div class="text-sm text-soft-gray font-medium">Minutes</div>
                    </div>
                    <div class="neuro-flat p-4 rounded-xl tilt-card">
                        <div class="text-2xl mb-2">👥</div>
                        <div class="text-xl font-serif font-bold text-rich-green">{{ recipe.servings }}</div>
                        <div class="text-sm text-soft-gray font-medium">Servings</div>
                    </div>
                    <div class="neuro-flat p-4 rounded-xl tilt-card">
                        <div class="text-2xl mb-2">🔥</div>
                        <div class="text-xl font-serif font-bold text-rich-green">{{ recipe.calories }}</div>
                        <div class="text-sm text-soft-gray font-medium">Calories</div>
                    </div>
                    <div class="neuro-flat p-4 rounded-xl tilt-card">
                        <div class="text-2xl mb-2">⭐</div>
                        <div class="text-xl font-serif font-bold text-rich-green">{{ recipe.difficulty or 'Medium' }}</div>
                        <div class="text-sm text-soft-gray font-medium">Difficulty</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main Content Grid -->
        <section class="pb-16">
            <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid lg:grid-cols-3 gap-8">

                    <!-- Ingredients Box -->
                    <div class="lg:col-span-1">
                        <div class="neuro-card p-6 h-fit sticky top-24">
                            <h2 class="text-2xl font-serif font-bold text-rich-green mb-6 flex items-center gap-2">
                                🥘 <span>Ingredients</span>
                            </h2>

                            {% if recipe.ingredients is mapping %}
                                {% for category, items in recipe.ingredients.items() %}
                                <div class="mb-6">
                                    <h3 class="text-lg font-semibold text-rich-green mb-3 capitalize">{{ category }}</h3>
                                    <ul class="space-y-2">
                                        {% for ingredient in items %}
                                        <li class="flex items-start gap-2 text-soft-gray">
                                            <span class="text-accent-green mt-1">•</span>
                                            <span>{{ ingredient }}</span>
                                        </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endfor %}
                            {% else %}
                                <ul class="space-y-3">
                                    {% for ingredient in recipe.ingredients %}
                                    <li class="flex items-start gap-3 text-soft-gray">
                                        <span class="neuro-btn w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-rich-green flex-shrink-0 mt-1">{{ loop.index }}</span>
                                        <span>{{ ingredient }}</span>
                                    </li>
                                    {% endfor %}
                                </ul>
                            {% endif %}

                            <div class="neuro-flat p-4 rounded-lg mt-6">
                                <h3 class="font-semibold text-rich-green mb-2 flex items-center gap-2">
                                    💡 <span>Chef's Tip</span>
                                </h3>
                                <p class="text-sm text-soft-gray">Fresh ingredients make all the difference! Try to use organic produce when possible for the best flavor and nutrition.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Instructions Box -->
                    <div class="lg:col-span-2">
                        <div class="neuro-card p-6 mb-8">
                            <h2 class="text-2xl font-serif font-bold text-rich-green mb-6 flex items-center gap-2">
                                👨‍🍳 <span>Instructions</span>
                            </h2>

                            {% if recipe.instructions is iterable and recipe.instructions is not string %}
                                <div class="space-y-6">
                                    {% for step in recipe.instructions %}
                                    <div class="flex gap-4">
                                        <div class="neuro-btn w-10 h-10 rounded-full flex items-center justify-center text-lg font-bold text-rich-green flex-shrink-0">
                                            {{ loop.index }}
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-soft-gray leading-relaxed">{{ step }}</p>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <div class="text-soft-gray leading-relaxed">
                                    {{ recipe.instructions }}
                                </div>
                            {% endif %}

                            {% if recipe.tips %}
                            <div class="neuro-flat p-4 rounded-lg mt-6">
                                <h3 class="font-semibold text-rich-green mb-2 flex items-center gap-2">
                                    💡 <span>Chef's Special Tips</span>
                                </h3>
                                <p class="text-sm text-soft-gray">{{ recipe.tips }}</p>
                            </div>
                            {% endif %}

                            <div class="neuro-flat p-4 rounded-lg mt-6">
                                <h3 class="font-semibold text-rich-green mb-3 flex items-center gap-2">
                                    🔥 <span>Cooking Tips</span>
                                </h3>
                                <ul class="space-y-2 text-sm text-soft-gray">
                                    <li class="flex items-start gap-2">
                                        <span class="text-accent-green mt-1">•</span>
                                        <span>Prep all ingredients before you start cooking</span>
                                    </li>
                                    <li class="flex items-start gap-2">
                                        <span class="text-accent-green mt-1">•</span>
                                        <span>Taste and adjust seasoning as you go</span>
                                    </li>
                                    <li class="flex items-start gap-2">
                                        <span class="text-accent-green mt-1">•</span>
                                        <span>Don't overcrowd the pan when cooking</span>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <!-- Nutrition Box -->
                        <div class="neuro-card p-6">
                            <h2 class="text-2xl font-serif font-bold text-rich-green mb-6 flex items-center gap-2">
                                📊 <span>Nutrition Facts</span>
                            </h2>
                            <p class="text-sm text-soft-gray mb-6">Per serving • Approximate values</p>

                            <div id="nutrition-info" class="grid grid-cols-2 gap-4">
                                <div class="neuro-flat p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🔥</div>
                                    <div class="text-lg font-bold text-rich-green">{{ recipe.calories }}</div>
                                    <div class="text-sm text-soft-gray">Calories</div>
                                </div>
                                <div class="neuro-flat p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🥩</div>
                                    <div class="text-lg font-bold text-rich-green">--</div>
                                    <div class="text-sm text-soft-gray">Protein (g)</div>
                                </div>
                                <div class="neuro-flat p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🍞</div>
                                    <div class="text-lg font-bold text-rich-green">--</div>
                                    <div class="text-sm text-soft-gray">Carbs (g)</div>
                                </div>
                                <div class="neuro-flat p-4 rounded-lg text-center">
                                    <div class="text-2xl mb-2">🧈</div>
                                    <div class="text-lg font-bold text-rich-green">--</div>
                                    <div class="text-sm text-soft-gray">Fat (g)</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Action Buttons -->
        <section class="pb-16">
            <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="neuro-card p-6 text-center">
                    <div class="flex flex-wrap justify-center gap-4">
                        <button onclick="addToMealPlan({{ recipe.id }})" class="glow-btn text-white px-6 py-3 font-semibold transition-all duration-300 tilt-btn">
                            ➕ Add to Meal Plan
                        </button>
                        <button onclick="printRecipe()" class="neuro-btn text-rich-green px-6 py-3 font-semibold transition-all duration-300 tilt-btn">
                            🖨️ Print Recipe
                        </button>
                        <button onclick="shareRecipe()" class="neuro-btn text-rich-green px-6 py-3 font-semibold transition-all duration-300 tilt-btn">
                            📤 Share
                        </button>
                        <button onclick="toggleFavorite({{ recipe.id }})" id="favorite-btn" class="neuro-btn text-rich-green px-6 py-3 font-semibold transition-all duration-300 tilt-btn">
                            <span id="favorite-icon">❤️</span> <span id="favorite-text">Add to Favorites</span>
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Recipe Suggestions -->
        <section class="pb-16">
            <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <div class="neuro-card p-8">
                    <h2 class="text-2xl font-serif font-bold text-rich-green mb-4">🍽️ You Might Also Like</h2>
                    <p class="text-soft-gray mb-6">Discover more delicious recipes that pair well with this dish!</p>
                    <a href="{{ url_for('browse_recipes') }}" class="glow-btn text-white px-8 py-4 font-semibold transition-all duration-300 tilt-btn">
                        Browse More Recipes
                    </a>
                </div>
            </div>
        </section>
    </main>

    <!-- Add to Meal Plan Modal -->
    <div id="meal-plan-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="neuro-container p-8 max-w-md w-full mx-4 relative">
            <button class="absolute top-4 right-4 text-2xl text-soft-gray hover:text-rich-green transition-colors" onclick="closeMealPlanModal()">&times;</button>
            <h3 class="text-xl font-serif font-bold text-rich-green mb-6 flex items-center gap-2">
                🍽️ <span>Add to Meal Plan</span>
            </h3>
            <form action="{{ url_for('add_to_meal_plan') }}" method="POST" class="space-y-4">
                <input type="hidden" id="modal-recipe-id" name="recipe_id" value="{{ recipe.id }}">
                <div>
                    <label for="date" class="block text-sm font-medium text-rich-green mb-2">📅 Date:</label>
                    <input type="date" id="date" name="date" required
                           class="w-full neuro-flat px-4 py-3 rounded-lg border-0 focus:outline-none focus:ring-2 focus:ring-accent-green">
                </div>
                <div>
                    <label for="meal_type" class="block text-sm font-medium text-rich-green mb-2">🍽️ Meal Type:</label>
                    <select id="meal_type" name="meal_type" required
                            class="w-full neuro-flat px-4 py-3 rounded-lg border-0 focus:outline-none focus:ring-2 focus:ring-accent-green">
                        <option value="">Select meal type...</option>
                        <option value="breakfast">🌅 Breakfast</option>
                        <option value="lunch">☀️ Lunch</option>
                        <option value="dinner">🌙 Dinner</option>
                    </select>
                </div>
                <div class="flex gap-4 pt-4">
                    <button type="submit" class="flex-1 glow-btn text-white py-3 px-6 font-semibold transition-all duration-300 tilt-btn">
                        ✨ Add to Plan
                    </button>
                    <button type="button" onclick="closeMealPlanModal()" class="flex-1 neuro-btn text-rich-green py-3 px-6 font-semibold transition-all duration-300 tilt-btn">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Footer -->
    <footer class="neuro-nav mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid md:grid-cols-4 gap-8">
                <div class="md:col-span-2">
                    <div class="flex items-center mb-4">
                        <span class="text-2xl mr-2">🍽️</span>
                        <span class="text-xl font-serif font-bold text-rich-green">MealMind</span>
                    </div>
                    <p class="text-soft-gray mb-4">Think Smart. Eat Better.</p>
                    <p class="text-sm text-soft-gray">Transform your ingredients into culinary masterpieces with AI-powered recipe discovery.</p>
                </div>

                <div>
                    <h3 class="font-semibold text-rich-green mb-4">Quick Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="{{ url_for('index') }}" class="text-soft-gray hover:text-rich-green transition-colors">Home</a></li>
                        <li><a href="{{ url_for('browse_recipes') }}" class="text-soft-gray hover:text-rich-green transition-colors">Discover</a></li>
                        <li><a href="{{ url_for('smart_recipes') }}" class="text-soft-gray hover:text-rich-green transition-colors">Smart Recipes</a></li>
                        <li><a href="{{ url_for('meal_plan') }}" class="text-soft-gray hover:text-rich-green transition-colors">Meal Plan</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold text-rich-green mb-4">Features</h3>
                    <ul class="space-y-2 text-sm">
                        <li class="text-soft-gray">🤖 AI Recipe Matching</li>
                        <li class="text-soft-gray">📸 Ingredient Recognition</li>
                        <li class="text-soft-gray">📅 Meal Planning</li>
                        <li class="text-soft-gray">❤️ Save Favorites</li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-rich-green/10 mt-8 pt-8 text-center">
                <p class="text-sm text-soft-gray">&copy; 2024 MealMind. All rights reserved.</p>
            </div>
        </div>
    </footer>


    <script src="{{ url_for('static', filename='script.js') }}"></script>
    <script>
        // Load nutrition information
        fetch(`/nutrition-info/{{ recipe.id }}`)
            .then(response => response.json())
            .then(data => {
                const nutritionInfo = document.getElementById('nutrition-info');
                if (nutritionInfo) {
                    const nutritionItems = nutritionInfo.querySelectorAll('.text-lg');
                    if (nutritionItems.length >= 4) {
                        nutritionItems[0].textContent = Math.round(data.calories);
                        nutritionItems[1].textContent = Math.round(data.protein) || '--';
                        nutritionItems[2].textContent = Math.round(data.carbs) || '--';
                        nutritionItems[3].textContent = Math.round(data.fat) || '--';
                    }
                }
            })
            .catch(error => console.error('Error loading nutrition info:', error));

        // Add to meal plan functionality
        function addToMealPlan(recipeId) {
            const modal = document.getElementById('meal-plan-modal');
            modal.classList.remove('hidden');
            modal.classList.add('flex');
            document.getElementById('modal-recipe-id').value = recipeId;
        }

        function closeMealPlanModal() {
            const modal = document.getElementById('meal-plan-modal');
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }

        function printRecipe() {
            window.print();
        }

        function shareRecipe() {
            if (navigator.share) {
                navigator.share({
                    title: '{{ recipe.name }} - MealMind',
                    text: 'Check out this delicious recipe!',
                    url: window.location.href
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('Recipe link copied to clipboard!');
                });
            }
        }

        // Theme switching functionality
        function toggleTheme() {
            const body = document.getElementById('body');
            const themeIcon = document.getElementById('theme-icon');

            if (body.classList.contains('dark')) {
                body.classList.remove('dark');
                themeIcon.textContent = '🌙';
                localStorage.setItem('theme', 'light');
            } else {
                body.classList.add('dark');
                themeIcon.textContent = '☀️';
                localStorage.setItem('theme', 'dark');
            }
        }

        // Load saved theme
        function loadTheme() {
            const savedTheme = localStorage.getItem('theme');
            const body = document.getElementById('body');
            const themeIcon = document.getElementById('theme-icon');

            if (savedTheme === 'dark') {
                body.classList.add('dark');
                themeIcon.textContent = '☀️';
            } else {
                body.classList.remove('dark');
                themeIcon.textContent = '🌙';
            }
        }

        // Initialize theme on page load
        loadTheme();

        // Mobile menu toggle
        document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Close modal when clicking outside
        document.getElementById('meal-plan-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeMealPlanModal();
            }
        });

        // Favorite functionality
        function toggleFavorite(recipeId) {
            const favoriteBtn = document.getElementById('favorite-btn');
            const favoriteIcon = document.getElementById('favorite-icon');
            const favoriteText = document.getElementById('favorite-text');

            // Check if user is logged in
            {% if not session.user_id %}
                alert('Please log in to add recipes to favorites!');
                window.location.href = '{{ url_for("login") }}';
                return;
            {% endif %}

            // Disable button during request
            favoriteBtn.disabled = true;
            favoriteBtn.style.opacity = '0.6';

            fetch('/api/toggle-favorite', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ recipe_id: recipeId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.is_favorite) {
                        favoriteIcon.textContent = '💖';
                        favoriteText.textContent = 'Remove from Favorites';
                        favoriteBtn.classList.remove('neuro-btn');
                        favoriteBtn.classList.add('glow-btn', 'text-white');
                        showNotification('Added to favorites!', 'success');
                    } else {
                        favoriteIcon.textContent = '❤️';
                        favoriteText.textContent = 'Add to Favorites';
                        favoriteBtn.classList.remove('glow-btn', 'text-white');
                        favoriteBtn.classList.add('neuro-btn', 'text-rich-green');
                        showNotification('Removed from favorites!', 'info');
                    }
                } else {
                    showNotification(data.message || 'Error updating favorites', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error updating favorites', 'error');
            })
            .finally(() => {
                // Re-enable button
                favoriteBtn.disabled = false;
                favoriteBtn.style.opacity = '1';
            });
        }

        // Check if recipe is already favorited on page load
        function checkFavoriteStatus() {
            {% if session.user_id %}
                fetch(`/api/check-favorite/{{ recipe.id }}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.is_favorite) {
                            const favoriteIcon = document.getElementById('favorite-icon');
                            const favoriteText = document.getElementById('favorite-text');
                            const favoriteBtn = document.getElementById('favorite-btn');

                            favoriteIcon.textContent = '💖';
                            favoriteText.textContent = 'Remove from Favorites';
                            favoriteBtn.classList.remove('neuro-btn', 'text-rich-green');
                            favoriteBtn.classList.add('glow-btn', 'text-white');
                        }
                    })
                    .catch(error => console.error('Error checking favorite status:', error));
            {% endif %}
        }

        // Simple notification function
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                type === 'info' ? 'bg-blue-500 text-white' :
                'bg-gray-500 text-white'
            }`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
                notification.style.opacity = '1';
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                notification.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Initialize favorite status check
        checkFavoriteStatus();
    </script>

    <!-- Flash messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <script>
                {% for category, message in messages %}
                    showNotification('{{ message }}', '{{ category }}');
                {% endfor %}
            </script>
        {% endif %}
    {% endwith %}
</body>
</html>
