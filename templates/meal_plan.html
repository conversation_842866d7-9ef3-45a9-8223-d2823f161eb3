<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meal Plan - MealMind</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'organic-beige': '#F9F5ED',
                        'organic-cream': '#FDF9F0',
                        'rich-green': '#1A5E2A',
                        'soft-green': '#2D7A3D',
                        'accent-green': '#4A9B5E',
                        'warm-brown': '#8B4513',
                        'soft-gray': '#6B7280',
                        'light-sage': '#E8F5E8'
                    },
                    fontFamily: {
                        'serif': ['Playfair Display', 'serif'],
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom organic overrides -->
    <link rel="stylesheet" href="{{ url_for('static', filename='organic-overrides.css') }}">

    <!-- Enhanced UI Effects -->
    <link rel="stylesheet" href="{{ url_for('static', filename='enhanced-ui.css') }}">

    <!-- Prevent page from becoming dull on back button -->
    <style>
        body {
            opacity: 1 !important;
            filter: none !important;
            transition: none !important;
        }

        html, body {
            background-color: #F9F5ED !important;
        }

        * {
            opacity: 1 !important;
            filter: none !important;
        }

        body.bfcache {
            opacity: 1 !important;
            filter: none !important;
        }
    </style>

    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍽️</text></svg>">
</head>
<body class="bg-organic-beige font-sans text-rich-green min-h-screen transition-all duration-500" id="body">
    <!-- Parallax Background -->
    <div class="parallax-bg"></div>

    <!-- Theme Toggle Button -->
    <button class="theme-toggle" onclick="toggleTheme()" title="Toggle Dark/Light Mode">
        <span id="theme-icon">🌙</span>
    </button>
    <!-- Header -->
    <header class="neuro-nav sticky top-0 z-50">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Brand -->
                <div class="flex items-center space-x-3">
                    <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-12 w-12">
                    <div>
                        <h1 class="text-2xl font-serif font-bold text-rich-green tracking-tight">MealMind</h1>
                        <p class="text-xs text-soft-gray font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="{{ url_for('index') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Home</a>
                    <a href="{{ url_for('browse_recipes') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Discover</a>
                    <a href="{{ url_for('smart_recipes') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Smart Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium border-b-2 border-rich-green">Meal Plan</a>
                    {% if user %}
                    <a href="{{ url_for('profile') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Profile</a>
                    <a href="{{ url_for('logout') }}" class="bg-rich-green text-organic-beige px-6 py-2 rounded-full hover:bg-soft-green transition-colors font-medium">Logout</a>
                    {% else %}
                    <a href="{{ url_for('login') }}" class="bg-rich-green text-organic-beige px-6 py-2 rounded-full hover:bg-soft-green transition-colors font-medium">Login</a>
                    {% endif %}
                </div>

                <!-- Mobile menu button -->
                <button class="md:hidden text-rich-green hover:text-soft-green" id="mobile-menu-toggle">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div class="md:hidden hidden" id="mobile-menu">
                <div class="px-2 pt-2 pb-3 space-y-1 bg-organic-cream border-t border-rich-green/10">
                    <a href="{{ url_for('index') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Home</a>
                    <a href="{{ url_for('browse_recipes') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Discover</a>
                    <a href="{{ url_for('smart_recipes') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Smart Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium bg-light-sage rounded">Meal Plan</a>
                    {% if user %}
                    <a href="{{ url_for('profile') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Profile</a>
                    <a href="{{ url_for('logout') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Logout</a>
                    {% else %}
                    <a href="{{ url_for('login') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Login</a>
                    {% endif %}
                </div>
            </div>
        </nav>
    </header>

    <main>
        <!-- Header Section -->
        <section class="pt-6 pb-10 lg:pt-8 lg:pb-12 bg-organic-cream">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-center md:text-left mb-6 md:mb-0">
                        <h1 class="text-3xl lg:text-4xl font-serif font-bold text-rich-green mb-3">🗓️ Your Meal Plan</h1>
                        <p class="text-base text-soft-gray">Plan your weekly meals and stay organized</p>
                    </div>
                    <div class="flex space-x-4">
                        <button onclick="loadWeeklyPlan()" class="bg-rich-green text-organic-beige px-6 py-3 rounded-full font-semibold hover:bg-soft-green transition-colors">
                            📅 Weekly View
                        </button>
                        <a href="{{ url_for('browse_recipes') }}" class="neuro text-rich-green px-6 py-3 rounded-full font-semibold transition-all duration-300 inline-block tilt-btn">
                            ➕ Add Meal
                        </a>
                    </div>
                </div>
            </div>
        </section>

        {% if meal_plans %}
        <!-- Stats Section -->
        <section class="py-8 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h2 class="text-2xl font-serif font-bold text-rich-green mb-6 text-center">📋 Planned Meals Overview</h2>
                <div class="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                    <div class="bg-light-sage/30 rounded-2xl p-6 text-center">
                        <div class="text-3xl font-serif font-bold text-rich-green mb-2">{{ meal_plans|length }}</div>
                        <div class="text-soft-gray font-medium">Meals Planned</div>
                    </div>
                    <div class="bg-light-sage/30 rounded-2xl p-6 text-center">
                        <div class="text-3xl font-serif font-bold text-rich-green mb-2">{{ meal_plans|sum(attribute='recipe.calories') }}</div>
                        <div class="text-soft-gray font-medium">Total Calories</div>
                    </div>
                    <div class="bg-light-sage/30 rounded-2xl p-6 text-center">
                        <div class="text-3xl font-serif font-bold text-rich-green mb-2">{{ (meal_plans|sum(attribute='recipe.prep_time') / 60)|round(1) }}</div>
                        <div class="text-soft-gray font-medium">Hours Cooking</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Meals List -->
        <section class="py-12 bg-organic-beige">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for plan in meal_plans %}
                    <div class="neuro-card p-6 tilt-card">
                        <div class="mb-4">
                            <h3 class="text-lg font-serif font-semibold text-rich-green mb-2">{{ plan.recipe.name }}</h3>
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="bg-light-sage/30 text-rich-green px-3 py-1 rounded-full text-sm font-medium">📅 {{ plan.date }}</span>
                                <span class="bg-light-sage/30 text-rich-green px-3 py-1 rounded-full text-sm font-medium">🍽️ {{ plan.meal_type.title() }}</span>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-2 text-sm text-soft-gray mb-4">
                            <div class="flex items-center">
                                <span class="mr-1">🔥</span>
                                <span>{{ plan.recipe.calories }} cal</span>
                            </div>
                            <div class="flex items-center">
                                <span class="mr-1">⏱️</span>
                                <span>{{ plan.recipe.prep_time }} min</span>
                            </div>
                        </div>

                        <div class="flex space-x-2">
                            <a href="{{ url_for('recipe_detail', recipe_id=plan.recipe.id) }}"
                               class="flex-1 bg-rich-green text-organic-beige py-2 px-4 rounded-lg text-sm font-medium hover:bg-soft-green transition-colors text-center">
                                👁️ View Recipe
                            </a>
                            <button onclick="removeMealPlan({{ plan.id }})"
                                    class="flex-1 border border-red-500 text-red-500 py-2 px-4 rounded-lg text-sm font-medium hover:bg-red-500 hover:text-white transition-colors">
                                🗑️ Remove
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </section>
        {% else %}
        <section class="py-20 bg-white">
            <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <div class="text-8xl mb-6">🍽️</div>
                <h2 class="text-3xl font-serif font-bold text-rich-green mb-4">No meals planned yet</h2>
                <p class="text-lg text-soft-gray mb-8 max-w-md mx-auto">Start planning your delicious meals! Browse our recipe collection and add your favorites to your meal plan.</p>
                <a href="{{ url_for('browse_recipes') }}" class="bg-rich-green text-organic-beige px-8 py-3 rounded-full text-lg font-semibold hover:bg-soft-green transition-colors shadow-lg hover:shadow-xl">🔍 Browse Recipes</a>
            </div>
        </section>
        {% endif %}

        <div class="weekly-view" id="weekly-view" style="display: none;">
            <h2>📅 Weekly Meal Plan</h2>
            <div class="week-grid" id="week-grid">
                <!-- Weekly plan will be loaded here -->
            </div>
        </div>



        <section class="py-16 bg-organic-cream">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h2 class="text-3xl font-serif font-bold text-rich-green mb-12 text-center">💡 Meal Planning Tips</h2>
                <div class="grid md:grid-cols-3 gap-8">
                    <div class="neuro-card p-8 text-center tilt-card float-element">
                        <div class="text-4xl mb-4">🎯</div>
                        <h3 class="text-xl font-serif font-semibold text-rich-green mb-4">Plan Ahead</h3>
                        <p class="text-soft-gray">Plan your meals for the week on Sunday to save time and reduce stress during busy weekdays.</p>
                    </div>
                    <div class="neuro-card p-8 text-center tilt-card float-element">
                        <div class="text-4xl mb-4">🛒</div>
                        <h3 class="text-xl font-serif font-semibold text-rich-green mb-4">Smart Shopping</h3>
                        <p class="text-soft-gray">Create a shopping list based on your meal plan to avoid impulse purchases and food waste.</p>
                    </div>
                    <div class="neuro-card p-8 text-center tilt-card float-element">
                        <div class="text-4xl mb-4">🥗</div>
                        <h3 class="text-xl font-serif font-semibold text-rich-green mb-4">Balance Nutrition</h3>
                        <p class="text-soft-gray">Include a variety of proteins, vegetables, and whole grains for a well-balanced diet.</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Add to Meal Plan Modal -->
    <div id="meal-plan-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="neuro-container p-8 max-w-md w-full mx-4 relative">
            <button class="absolute top-4 right-4 text-2xl text-soft-gray hover:text-rich-green transition-colors" onclick="closeMealPlanModal()">&times;</button>
            <h3 class="text-2xl font-serif font-bold text-rich-green mb-6">🍽️ Add to Meal Plan</h3>
            <form action="{{ url_for('add_to_meal_plan') }}" method="POST" class="space-y-6">
                <input type="hidden" id="modal-recipe-id" name="recipe_id">
                <div>
                    <label for="date" class="block text-sm font-semibold text-rich-green mb-2">📅 Date:</label>
                    <input type="date" id="date" name="date" required min="" class="w-full p-3 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none">
                </div>
                <div>
                    <label for="meal_type" class="block text-sm font-semibold text-rich-green mb-2">🍽️ Meal Type:</label>
                    <select id="meal_type" name="meal_type" required class="w-full p-3 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none">
                        <option value="">Select meal type...</option>
                        <option value="breakfast">🌅 Breakfast</option>
                        <option value="lunch">☀️ Lunch</option>
                        <option value="dinner">🌙 Dinner</option>
                    </select>
                </div>
                <button type="submit" class="w-full bg-rich-green text-organic-beige py-3 px-6 rounded-xl text-lg font-semibold hover:bg-soft-green transition-colors">✨ Add to Plan</button>
            </form>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-rich-green text-organic-beige py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand Section -->
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-10 w-10">
                        <div>
                            <h3 class="text-2xl font-serif font-bold">MealMind</h3>
                            <p class="text-sm text-organic-beige/80 font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                        </div>
                    </div>
                    <p class="text-organic-beige/80 mb-4 max-w-md">
                        Discover delicious recipes crafted with quality ingredients. Transform your cooking experience with AI-powered recipe discovery.
                    </p>
                    <div class="flex space-x-4">
                        <span class="text-2xl">🍅</span>
                        <span class="text-2xl">🥕</span>
                        <span class="text-2xl">🌶️</span>
                        <span class="text-2xl">🥬</span>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li><a href="{{ url_for('index') }}" class="hover:text-organic-beige transition-colors">Home</a></li>
                        <li><a href="{{ url_for('browse_recipes') }}" class="hover:text-organic-beige transition-colors">Browse Recipes</a></li>
                        <li><a href="{{ url_for('meal_plan') }}" class="hover:text-organic-beige transition-colors">Meal Plan</a></li>
                        {% if user %}
                        <li><a href="{{ url_for('favorites') }}" class="hover:text-organic-beige transition-colors">My Favorites</a></li>
                        {% else %}
                        <li><a href="{{ url_for('register') }}" class="hover:text-organic-beige transition-colors">Join Free</a></li>
                        {% endif %}
                    </ul>
                </div>

                <!-- Features -->
                <div>
                    <h4 class="font-semibold mb-4">Features</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li>🤖 AI Recipe Discovery</li>
                        <li>📸 Photo Recognition</li>
                        <li>🥗 Diet Filtering</li>
                        <li>⏱️ Time-based Search</li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-organic-beige/20 mt-8 pt-8 text-center">
                <p class="text-organic-beige/60 text-sm">
                    © 2025 MealMind. Made with 💚 for food lovers everywhere.
                </p>
            </div>
        </div>
    </footer>


    <script src="{{ url_for('static', filename='script.js') }}"></script>

    <!-- Meal Plan JavaScript -->
    <script>
        function closeMealPlanModal() {
            document.getElementById('meal-plan-modal').classList.add('hidden');
            document.getElementById('meal-plan-modal').classList.remove('flex');
        }

        function openMealPlanModal(recipeId) {
            document.getElementById('modal-recipe-id').value = recipeId;
            document.getElementById('meal-plan-modal').classList.remove('hidden');
            document.getElementById('meal-plan-modal').classList.add('flex');

            // Set default date to today and minimum date to today
            const today = new Date().toISOString().split('T')[0];
            const dateInput = document.getElementById('date');
            dateInput.value = today;
            dateInput.min = today;
        }

        function removeMealPlan(planId) {
            if (confirm('Are you sure you want to remove this meal from your plan?')) {
                fetch(`/meal-plan/remove/${planId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error removing meal plan');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error removing meal plan');
                });
            }
        }

        // Close modal when clicking outside
        document.getElementById('meal-plan-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeMealPlanModal();
            }
        });

        // Set minimum date on page load
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            const dateInput = document.getElementById('date');
            if (dateInput) {
                dateInput.min = today;
            }
        });

        // Mobile menu toggle
        document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
    </script>

    <!-- Flash messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <script>
                {% for category, message in messages %}
                    showNotification('{{ message }}', '{{ category }}');
                {% endfor %}
            </script>
        {% endif %}
    {% endwith %}

    <!-- Prevent page from becoming dull on back button -->
    <script>
        window.addEventListener('pageshow', function(event) {
            if (event.persisted) {
                document.body.style.opacity = '1';
                document.body.style.filter = 'none';
                document.body.style.transition = 'none';
            }
        });

        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                document.body.style.opacity = '1';
                document.body.style.filter = 'none';
            }
        });

        window.addEventListener('load', function() {
            document.body.style.opacity = '1';
            document.body.style.filter = 'none';
            document.body.style.transition = 'none';
        });

        document.body.style.opacity = '1';
        document.body.style.filter = 'none';
        document.body.style.transition = 'none';

        // Theme switching functionality
        function toggleTheme() {
            const body = document.getElementById('body');
            const themeIcon = document.getElementById('theme-icon');

            if (body.classList.contains('dark')) {
                body.classList.remove('dark');
                themeIcon.textContent = '🌙';
                localStorage.setItem('theme', 'light');
            } else {
                body.classList.add('dark');
                themeIcon.textContent = '☀️';
                localStorage.setItem('theme', 'dark');
            }
        }

        // Load saved theme
        function loadTheme() {
            const savedTheme = localStorage.getItem('theme');
            const body = document.getElementById('body');
            const themeIcon = document.getElementById('theme-icon');

            if (savedTheme === 'dark') {
                body.classList.add('dark');
                themeIcon.textContent = '☀️';
            } else {
                body.classList.remove('dark');
                themeIcon.textContent = '🌙';
            }
        }

        // Initialize theme on page load
        loadTheme();
    </script>
</body>
</html>
