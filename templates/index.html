<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MealMind - Artisan Recipe Discovery</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'organic-beige': '#F9F5ED',
                        'organic-cream': '#FDF9F0',
                        'rich-green': '#1A5E2A',
                        'soft-green': '#2D7A3D',
                        'accent-green': '#4A9B5E',
                        'warm-brown': '#8B4513',
                        'soft-gray': '#6B7280',
                        'light-sage': '#E8F5E8',
                        'dark-bg': '#0F172A',
                        'dark-surface': '#1E293B',
                        'dark-card': '#334155',
                        'neon-green': '#00FF88',
                        'neon-blue': '#00D4FF',
                        'neon-purple': '#B794F6'
                    },
                    fontFamily: {
                        'serif': ['Playfair Display', 'serif'],
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'tilt': 'tilt 10s infinite linear',
                        'parallax': 'parallax 20s infinite linear',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' },
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 5px #00FF88, 0 0 10px #00FF88, 0 0 15px #00FF88' },
                            '100%': { boxShadow: '0 0 10px #00FF88, 0 0 20px #00FF88, 0 0 30px #00FF88' },
                        },
                        tilt: {
                            '0%, 50%, 100%': { transform: 'rotate(0deg)' },
                            '25%': { transform: 'rotate(1deg)' },
                            '75%': { transform: 'rotate(-1deg)' },
                        },
                        parallax: {
                            '0%': { transform: 'translateX(-100%)' },
                            '100%': { transform: 'translateX(100%)' },
                        }
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom organic overrides -->
    <link rel="stylesheet" href="{{ url_for('static', filename='organic-overrides.css') }}">

    <!-- Prevent page from becoming dull on back button -->
    <style>
        body {
            opacity: 1 !important;
            filter: none !important;
            transition: none !important;
        }

        /* Force page to stay bright */
        html, body {
            background-color: #F9F5ED !important;
        }

        /* Prevent any dimming effects */
        * {
            opacity: 1 !important;
            filter: none !important;
        }

        /* Override any browser back-forward cache effects */
        body.bfcache {
            opacity: 1 !important;
            filter: none !important;
        }

        /* Enhanced UI Styles */
        /* Theme Variables */
        :root {
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --shadow-light: rgba(0, 0, 0, 0.1);
            --shadow-heavy: rgba(0, 0, 0, 0.3);
            --glow-primary: #00FF88;
            --glow-secondary: #00D4FF;
        }

        .dark {
            --glass-bg: rgba(0, 0, 0, 0.2);
            --glass-border: rgba(255, 255, 255, 0.1);
            --shadow-light: rgba(0, 0, 0, 0.3);
            --shadow-heavy: rgba(0, 0, 0, 0.6);
        }

        /* Glassmorphism Effects */
        .glass {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            box-shadow: 0 8px 32px var(--shadow-light);
        }

        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid var(--glass-border);
            box-shadow: 0 4px 16px var(--shadow-light);
            transition: all 0.3s ease;
        }

        .glass-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 12px 40px var(--shadow-heavy);
        }

        /* Glowing Buttons */
        .glow-btn {
            position: relative;
            overflow: hidden;
            background: linear-gradient(45deg, var(--glow-primary), var(--glow-secondary));
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
            transition: all 0.3s ease;
        }

        .glow-btn:hover {
            box-shadow: 0 0 30px rgba(0, 255, 136, 0.6), 0 0 60px rgba(0, 212, 255, 0.4);
            transform: translateY(-2px);
        }

        .glow-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .glow-btn:hover::before {
            left: 100%;
        }

        /* 3D Tilt Effects */
        .tilt-card {
            transform-style: preserve-3d;
            transition: transform 0.3s ease;
        }

        .tilt-card:hover {
            transform: perspective(1000px) rotateX(5deg) rotateY(5deg) translateZ(20px);
        }

        /* Parallax Background */
        .parallax-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 200%;
            height: 100%;
            background: linear-gradient(45deg,
                rgba(26, 94, 42, 0.1) 0%,
                rgba(74, 155, 94, 0.1) 25%,
                rgba(0, 255, 136, 0.1) 50%,
                rgba(0, 212, 255, 0.1) 75%,
                rgba(26, 94, 42, 0.1) 100%);
            animation: parallax 20s infinite linear;
            z-index: -1;
        }

        /* Floating Elements */
        .float-element {
            animation: float 6s ease-in-out infinite;
        }

        .float-element:nth-child(2) { animation-delay: -2s; }
        .float-element:nth-child(3) { animation-delay: -4s; }





        /* Neon Text Effect */
        .neon-text {
            text-shadow: 0 0 10px var(--glow-primary), 0 0 20px var(--glow-primary), 0 0 30px var(--glow-primary);
        }
    </style>

    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍽️</text></svg>">
</head>
<body class="bg-organic-beige font-sans text-rich-green min-h-screen transition-all duration-500" id="body">
    <!-- Parallax Background -->
    <div class="parallax-bg"></div>


    <!-- Header -->
    <header class="glass border-b border-rich-green/10 sticky top-0 z-50">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Brand -->
                <div class="flex items-center space-x-3">
                    <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-12 w-12">
                    <div>
                        <h1 class="text-2xl font-serif font-bold text-rich-green tracking-tight">MealMind</h1>
                        <p class="text-xs text-soft-gray font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="{{ url_for('index') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium border-b-2 border-rich-green">Home</a>
                    <a href="{{ url_for('browse_recipes') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Discover</a>
                    <a href="{{ url_for('smart_recipes') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Smart Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                    {% if user %}
                    <a href="{{ url_for('profile') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Profile</a>
                    <a href="{{ url_for('logout') }}" class="bg-rich-green text-organic-beige px-6 py-2 rounded-full hover:bg-soft-green transition-colors font-medium">Logout</a>
                    {% else %}
                    <a href="{{ url_for('login') }}" class="bg-rich-green text-organic-beige px-6 py-2 rounded-full hover:bg-soft-green transition-colors font-medium">Login</a>
                    {% endif %}
                </div>

                <!-- Mobile menu button -->
                <button class="md:hidden text-rich-green hover:text-soft-green" id="mobile-menu-toggle">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div class="md:hidden hidden" id="mobile-menu">
                <div class="px-2 pt-2 pb-3 space-y-1 bg-organic-cream border-t border-rich-green/10">
                    <a href="{{ url_for('index') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium bg-light-sage rounded">Home</a>
                    <a href="{{ url_for('browse_recipes') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Discover</a>
                    <a href="{{ url_for('smart_recipes') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Smart Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                    {% if user %}
                    <a href="{{ url_for('profile') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Profile</a>
                    <a href="{{ url_for('logout') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Logout</a>
                    {% else %}
                    <a href="{{ url_for('login') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Login</a>
                    {% endif %}
                </div>
            </div>
        </nav>
    </header>

    <main class="relative" style="height: calc(100vh - 80px); width: 100vw; max-width: 100vw; overflow-x: hidden; position: relative;">
        <!-- Sliding Container Wrapper -->
        <div class="w-full h-full overflow-hidden" style="max-width: 100vw; overflow-x: hidden; position: relative;">
            <!-- Sliding Container -->
            <div id="sliding-container" class="flex transition-transform duration-1000 ease-in-out h-full" style="width: 300vw;">

            <!-- Section 1: Smart Cooking Starts Here -->
            <section class="flex-shrink-0 pt-4 pb-8 lg:pt-6 lg:pb-12 w-screen h-full overflow-y-auto flex items-center">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
                    <div class="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
                        <!-- Hero Text -->
                        <div class="text-center lg:text-left">
                            <!-- Large Logo for Hero -->
                            <div class="flex justify-center lg:justify-start mb-6">
                                <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-20 w-20 lg:h-24 lg:w-24">
                            </div>

                            <h1 class="text-3xl sm:text-4xl lg:text-5xl font-serif font-bold text-rich-green leading-tight mb-4 neon-text float-element">
                                Smart Cooking
                                <span class="text-soft-green italic">Starts</span>
                                Here
                            </h1>
                            <p class="text-base lg:text-lg text-soft-gray leading-relaxed mb-6 max-w-2xl float-element">
                                No idea what to make? Just tell us what you have. MealMind's intelligent kitchen assistant will whip up chef-inspired recipes tailored just for you.
                            </p>

                            <!-- CTA Buttons -->
                            <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8">
                                <a href="{{ url_for('smart_recipes') }}" class="neuro text-rich-green px-8 py-4 rounded-full font-semibold transition-all duration-300 text-center tilt-btn">
                                    🤖 Start Cooking
                                </a>
                                <a href="{{ url_for('browse_recipes') }}" class="glow-btn-secondary px-8 py-4 rounded-full font-semibold transition-all duration-300 text-center tilt-btn">
                                    🔍 Browse Recipes
                                </a>
                            </div>

                            <!-- Stats -->
                            <div class="flex justify-center lg:justify-start space-x-8 text-center">
                                <div>
                                    <div class="text-2xl font-serif font-bold text-rich-green">200+</div>
                                    <div class="text-sm text-soft-gray font-medium">Recipes</div>
                                </div>
                                <div>
                                    <div class="text-2xl font-serif font-bold text-rich-green">4</div>
                                    <div class="text-sm text-soft-gray font-medium">Cuisines</div>
                                </div>
                                <div>
                                    <div class="text-2xl font-serif font-bold text-rich-green">AI</div>
                                    <div class="text-sm text-soft-gray font-medium">Powered</div>
                                </div>
                            </div>
                        </div>

                        <!-- Hero Visual -->
                        <div class="relative">
                            <div class="bg-organic-cream rounded-3xl p-8 lg:p-12 relative overflow-hidden">
                                <!-- Decorative elements -->
                                <div class="absolute top-4 right-4 text-2xl opacity-20">🌿</div>
                                <div class="absolute bottom-4 left-4 text-2xl opacity-20">🍃</div>

                                <!-- Main visual content -->
                                <div class="text-center">
                                    <div class="text-6xl mb-4">🥗</div>
                                    <h3 class="text-xl font-serif font-semibold text-rich-green mb-2">Fresh Ingredients</h3>
                                    <p class="text-soft-gray">Transformed into delicious meals</p>

                                    <!-- Ingredient icons -->
                                    <div class="flex justify-center space-x-4 mt-6 text-2xl">
                                        <span class="animate-bounce" style="animation-delay: 0s;">🍅</span>
                                        <span class="animate-bounce" style="animation-delay: 0.2s;">🥕</span>
                                        <span class="animate-bounce" style="animation-delay: 0.4s;">🧄</span>
                                        <span class="animate-bounce" style="animation-delay: 0.6s;">🌶️</span>
                                    </div>

                                    <div class="text-2xl mt-4 text-soft-green">↓</div>
                                    <div class="text-4xl mt-2">🍽️</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section 2: Ready to Start Cooking -->
            <section class="flex-shrink-0 py-8 lg:py-12 bg-organic-cream w-screen h-full overflow-y-auto flex items-center">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center w-full">
                    <h2 class="text-3xl lg:text-4xl font-serif font-bold text-rich-green mb-6">Ready to Start Cooking?</h2>
                    <p class="text-lg text-soft-gray max-w-2xl mx-auto mb-12">
                        Explore our features and discover your next favorite recipe
                    </p>

                    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
                        <!-- Smart Recipes -->
                        <div class="bg-white rounded-2xl p-8 border border-rich-green/10 hover:border-rich-green/20 transition-all hover:shadow-lg flex flex-col h-full">
                            <div class="text-5xl mb-4 text-center">🧠</div>
                            <h3 class="text-xl font-serif font-semibold text-rich-green mb-3 text-center">Smart Recipes</h3>
                            <p class="text-soft-gray mb-6 text-center flex-grow">Upload photos or type ingredients to get AI-powered recipe suggestions</p>
                            <a href="{{ url_for('smart_recipes') }}" class="bg-rich-green text-organic-beige px-6 py-3 rounded-full font-semibold hover:bg-soft-green transition-colors inline-block text-center">
                                Try Smart Recipes
                            </a>
                        </div>

                        <!-- Discover -->
                        <div class="bg-white rounded-2xl p-8 border border-rich-green/10 hover:border-rich-green/20 transition-all hover:shadow-lg flex flex-col h-full">
                            <div class="text-5xl mb-4 text-center">🔍</div>
                            <h3 class="text-xl font-serif font-semibold text-rich-green mb-3 text-center">Discover Recipes</h3>
                            <p class="text-soft-gray mb-6 text-center flex-grow">Browse our curated collection of recipes from around the world</p>
                            <a href="{{ url_for('browse_recipes') }}" class="bg-rich-green text-organic-beige px-6 py-3 rounded-full font-semibold hover:bg-soft-green transition-colors inline-block text-center">
                                Browse Recipes
                            </a>
                        </div>

                        <!-- Meal Plan -->
                        <div class="bg-white rounded-2xl p-8 border border-rich-green/10 hover:border-rich-green/20 transition-all hover:shadow-lg flex flex-col h-full">
                            <div class="text-5xl mb-4 text-center">📅</div>
                            <h3 class="text-xl font-serif font-semibold text-rich-green mb-3 text-center">Meal Planning</h3>
                            <p class="text-soft-gray mb-6 text-center flex-grow">Organize your weekly meals and stay on track with your cooking goals</p>
                            <a href="{{ url_for('meal_plan') }}" class="bg-rich-green text-organic-beige px-6 py-3 rounded-full font-semibold hover:bg-soft-green transition-colors inline-block text-center">
                                Plan Meals
                            </a>
                        </div>

                        <!-- Your Favorites -->
                        {% if user %}
                        <div class="bg-white rounded-2xl p-8 border border-rich-green/10 hover:border-rich-green/20 transition-all hover:shadow-lg flex flex-col h-full">
                            <div class="text-5xl mb-4 text-center">❤️</div>
                            <h3 class="text-xl font-serif font-semibold text-rich-green mb-3 text-center">Your Favorites</h3>
                            <p class="text-soft-gray mb-6 text-center flex-grow">View your saved favorite recipes and cook them again</p>
                            <a href="{{ url_for('favorites') }}" class="bg-rich-green text-organic-beige px-6 py-3 rounded-full font-semibold hover:bg-soft-green transition-colors inline-block text-center">
                                My Favorites
                            </a>
                        </div>
                        {% else %}
                        <div class="bg-white rounded-2xl p-8 border border-rich-green/10 hover:border-rich-green/20 transition-all hover:shadow-lg flex flex-col h-full">
                            <div class="text-5xl mb-4 text-center">🔑</div>
                            <h3 class="text-xl font-serif font-semibold text-rich-green mb-3 text-center">Join MealMind</h3>
                            <p class="text-soft-gray mb-6 text-center flex-grow">Create an account to save your favorite recipes</p>
                            <a href="{{ url_for('register') }}" class="bg-rich-green text-organic-beige px-6 py-3 rounded-full font-semibold hover:bg-soft-green transition-colors inline-block text-center">
                                Sign Up Free
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </section>





            <!-- Section 3: Why Choose MealMind -->
            <section class="flex-shrink-0 py-8 lg:py-12 w-screen h-full overflow-y-auto flex items-center">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
                    <div class="text-center mb-8">
                        <h2 class="text-3xl lg:text-4xl font-serif font-bold text-rich-green mb-4">Why Choose MealMind?</h2>
                        <p class="text-lg text-soft-gray max-w-2xl mx-auto">Experience the perfect blend of technology and culinary artistry</p>
                    </div>

                    <div class="grid md:grid-cols-3 gap-6 lg:gap-8">
                        <div class="bg-white rounded-2xl p-6 lg:p-8 border border-rich-green/10 hover:border-rich-green/20 transition-all hover:shadow-lg text-center">
                            <div class="bg-organic-cream w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-2xl">🎯</div>
                            <h3 class="text-xl font-serif font-semibold text-rich-green mb-3">Smart Planning</h3>
                            <p class="text-soft-gray">Organize your meals for the entire week with our intelligent meal planning system</p>
                        </div>

                        <div class="bg-white rounded-2xl p-6 lg:p-8 border border-rich-green/10 hover:border-rich-green/20 transition-all hover:shadow-lg text-center">
                            <div class="bg-organic-cream w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-2xl">🥗</div>
                            <h3 class="text-xl font-serif font-semibold text-rich-green mb-3">Healthy Recipes</h3>
                            <p class="text-soft-gray">Discover nutritious and delicious recipes with detailed nutritional information</p>
                        </div>

                        <div class="bg-white rounded-2xl p-6 lg:p-8 border border-rich-green/10 hover:border-rich-green/20 transition-all hover:shadow-lg text-center">
                            <div class="bg-organic-cream w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-2xl">⚡</div>
                            <h3 class="text-xl font-serif font-semibold text-rich-green mb-3">Quick & Easy</h3>
                            <p class="text-soft-gray">Find recipes that fit your schedule with our time-based filtering system</p>
                        </div>
                    </div>

                    <!-- Tagline -->
                    <div class="mt-12 text-center">
                        <p class="text-xl lg:text-2xl font-serif italic text-rich-green max-w-4xl mx-auto leading-relaxed">
                            "Smarter cooking made simple—with AI, wellness, and convenience at your fingertips."
                        </p>
                    </div>
                </div>
            </section>
        </div>

        <!-- Navigation Arrows -->
        <button id="prev-arrow" class="absolute left-4 top-1/2 transform -translate-y-1/2 glass-card text-rich-green p-3 rounded-full hover:shadow-xl transition-all duration-300 z-10 tilt-card">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
        </button>

        <button id="next-arrow" class="absolute right-4 top-1/2 transform -translate-y-1/2 glass-card text-rich-green p-3 rounded-full hover:shadow-xl transition-all duration-300 z-10 tilt-card">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
        </button>

        <!-- Navigation Dots -->
        <div class="flex justify-center space-x-3 py-8">
            <button class="slide-dot w-4 h-4 rounded-full transition-all duration-300" data-slide="0"></button>
            <button class="slide-dot w-4 h-4 rounded-full transition-all duration-300" data-slide="1"></button>
            <button class="slide-dot w-4 h-4 rounded-full transition-all duration-300" data-slide="2"></button>
        </div>
        </div> <!-- End sliding container wrapper -->
    </main>

    <!-- Footer -->
    <footer class="bg-rich-green text-organic-beige py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand Section -->
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-10 w-10">
                        <div>
                            <h3 class="text-2xl font-serif font-bold">MealMind</h3>
                            <p class="text-sm text-organic-beige/80 font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                        </div>
                    </div>
                    <p class="text-organic-beige/80 mb-4 max-w-md">
                        Discover delicious recipes crafted with quality ingredients. Transform your cooking experience with AI-powered recipe discovery.
                    </p>
                    <div class="flex space-x-4">
                        <span class="text-2xl">🍅</span>
                        <span class="text-2xl">🥕</span>
                        <span class="text-2xl">🌶️</span>
                        <span class="text-2xl">🥬</span>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li><a href="{{ url_for('index') }}" class="hover:text-organic-beige transition-colors">Home</a></li>
                        <li><a href="{{ url_for('browse_recipes') }}" class="hover:text-organic-beige transition-colors">Browse Recipes</a></li>
                        <li><a href="{{ url_for('meal_plan') }}" class="hover:text-organic-beige transition-colors">Meal Plan</a></li>
                        {% if user %}
                        <li><a href="{{ url_for('favorites') }}" class="hover:text-organic-beige transition-colors">My Favorites</a></li>
                        {% else %}
                        <li><a href="{{ url_for('register') }}" class="hover:text-organic-beige transition-colors">Join Free</a></li>
                        {% endif %}
                    </ul>
                </div>

                <!-- Features -->
                <div>
                    <h4 class="font-semibold mb-4">Features</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li>🤖 AI Recipe Discovery</li>
                        <li>📸 Photo Recognition</li>
                        <li>🥗 Diet Filtering</li>
                        <li>⏱️ Time-based Search</li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-organic-beige/20 mt-12 pt-12 text-center">
                <p class="text-organic-beige/60 text-sm">
                    © 2025 MealMind. Made with 💚 for food lovers everywhere.
                </p>
            </div>
        </div>
    </footer>







    <script src="{{ url_for('static', filename='script.js') }}"></script>

    <!-- Sliding Animation JavaScript -->
    <script>
        let currentSlide = 0;
        const totalSlides = 3;
        let slideInterval;
        let isManualNavigation = false;
        let manualNavigationTimeout;

        function goToSlide(slideIndex) {
            currentSlide = slideIndex;
            const container = document.getElementById('sliding-container');
            const translateX = -(slideIndex * 100); // Each slide is 100vw wide
            container.style.transform = `translateX(${translateX}vw)`;

            // Update dots
            document.querySelectorAll('.slide-dot').forEach((dot, index) => {
                if (index === slideIndex) {
                    dot.classList.remove('bg-rich-green/30');
                    dot.classList.add('bg-rich-green');
                } else {
                    dot.classList.remove('bg-rich-green');
                    dot.classList.add('bg-rich-green/30');
                }
            });
        }

        function nextSlide() {
            currentSlide = (currentSlide + 1) % totalSlides;
            goToSlide(currentSlide);
        }

        function prevSlide() {
            currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
            goToSlide(currentSlide);
        }

        function startAutoSlide() {
            stopAutoSlide(); // Clear any existing interval
            slideInterval = setInterval(() => {
                if (!isManualNavigation) {
                    nextSlide();
                }
            }, 5000); // Equal 5 seconds for each slide
        }

        function stopAutoSlide() {
            if (slideInterval) {
                clearInterval(slideInterval);
                slideInterval = null;
            }
        }

        function handleManualNavigation() {
            // Give priority to manual navigation
            isManualNavigation = true;
            stopAutoSlide();

            // Clear any existing manual navigation timeout
            if (manualNavigationTimeout) {
                clearTimeout(manualNavigationTimeout);
            }

            // Resume auto-slide after 6 seconds of no manual interaction
            manualNavigationTimeout = setTimeout(() => {
                isManualNavigation = false;
                startAutoSlide();
            }, 6000);
        }

        // Initialize sliding
        document.addEventListener('DOMContentLoaded', function() {
            // Add click listeners to dots
            document.querySelectorAll('.slide-dot').forEach((dot, index) => {
                dot.addEventListener('click', () => {
                    goToSlide(index);
                    handleManualNavigation();
                });
            });

            // Add click listeners to arrow buttons - HIGHEST PRIORITY
            document.getElementById('prev-arrow').addEventListener('click', () => {
                prevSlide();
                handleManualNavigation();
            });

            document.getElementById('next-arrow').addEventListener('click', () => {
                nextSlide();
                handleManualNavigation();
            });

            // Start auto-sliding
            startAutoSlide();

            // Pause auto-slide on hover (but don't interfere with manual navigation)
            const slidingContainer = document.getElementById('sliding-container');
            slidingContainer.addEventListener('mouseenter', () => {
                if (!isManualNavigation) {
                    stopAutoSlide();
                }
            });
            slidingContainer.addEventListener('mouseleave', () => {
                if (!isManualNavigation) {
                    startAutoSlide();
                }
            });

            // Add keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowLeft') {
                    prevSlide();
                    handleManualNavigation();
                } else if (e.key === 'ArrowRight') {
                    nextSlide();
                    handleManualNavigation();
                }
            });
        });



        // Enhanced slide dots functionality
        function updateDots() {
            document.querySelectorAll('.slide-dot').forEach((dot, index) => {
                if (index === currentSlide) {
                    dot.classList.add('active');
                } else {
                    dot.classList.remove('active');
                }
            });
        }


    </script>

    <script>


        // Enhanced ingredient detection correction
        function editDetectedIngredients() {
            const detectedTags = document.getElementById('detected-tags');
            const ingredients = Array.from(detectedTags.children).map(tag => tag.textContent);

            const correctedIngredients = prompt(
                'Edit detected ingredients (separate with commas):',
                ingredients.join(', ')
            );

            if (correctedIngredients !== null) {
                const newIngredients = correctedIngredients.split(',').map(ing => ing.trim()).filter(ing => ing);

                // Update the display
                detectedTags.innerHTML = '';
                newIngredients.forEach(ingredient => {
                    const tag = document.createElement('span');
                    tag.className = 'ingredient-tag detected';
                    tag.textContent = ingredient;
                    detectedTags.appendChild(tag);
                });

                // Update the text input as well
                document.getElementById('ingredients_text').value = newIngredients.join(', ');

                // Send correction to backend
                fetch('/correct-ingredients', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ ingredients: newIngredients })
                })
                .then(response => response.json())
                .then(data => {
                    showNotification('Ingredients corrected! This helps improve our AI.', 'success');
                });
            }
        }

        // Mobile menu toggle
        document.querySelector('.mobile-menu-toggle').addEventListener('click', function() {
            document.querySelector('.nav-menu').classList.toggle('active');
        });

        // Show login prompt for non-logged-in users
        function showLoginPrompt() {
            if (confirm('Please log in to save favorites. Would you like to go to the login page?')) {
                window.location.href = '/login';
            }
        }

        // Enhanced favorite toggle with visual feedback
        function toggleFavorite(recipeId) {
            fetch('/toggle-favorite', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ recipe_id: recipeId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showNotification(data.error, 'error');
                    return;
                }

                const btn = document.getElementById(`fav-btn-${recipeId}`);
                if (btn) {
                    if (data.is_favorite) {
                        btn.innerHTML = '❤️';
                        btn.classList.add('favorited');
                    } else {
                        btn.innerHTML = '🤍';
                        btn.classList.remove('favorited');
                    }
                }

                showNotification(data.message, 'success');
            })
            .catch(error => {
                console.error('Error toggling favorite:', error);
                showNotification('Error updating favorite', 'error');
            });
        }
    </script>

    <!-- ML Ingredient Identification JavaScript -->
    <script>
        let identificationTimeout;

        function identifyIngredientsFromText() {
            // Clear previous timeout
            clearTimeout(identificationTimeout);

            // Set a new timeout to avoid too many API calls
            identificationTimeout = setTimeout(() => {
                const text = document.getElementById('ingredients_text').value.trim();

                if (text.length < 3) {
                    document.getElementById('identified-ingredients-container').classList.add('hidden');
                    return;
                }

                // Call ML API to identify ingredients
                fetch('/api/identify-ingredients', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ text: text })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.ingredients.length > 0) {
                        displayIdentifiedIngredients(data.ingredients);
                    } else {
                        document.getElementById('identified-ingredients-container').classList.add('hidden');
                    }
                })
                .catch(error => {
                    console.error('Error identifying ingredients:', error);
                });
            }, 500); // Wait 500ms after user stops typing
        }

        function displayIdentifiedIngredients(ingredients) {
            const container = document.getElementById('identified-ingredients-container');
            const tagsContainer = document.getElementById('identified-ingredients-tags');

            // Clear previous tags
            tagsContainer.innerHTML = '';

            // Add new tags
            ingredients.forEach(ingredient => {
                const tag = document.createElement('span');
                tag.className = 'ingredient-tag';
                tag.textContent = `${ingredient.name} (${ingredient.category})`;

                // Add confidence indicator
                if (ingredient.confidence > 0.8) {
                    tag.style.backgroundColor = '#E8F5E8';
                    tag.style.borderColor = '#1A5E2A';
                } else {
                    tag.style.backgroundColor = '#FEF3C7';
                    tag.style.borderColor = '#D97706';
                }

                tagsContainer.appendChild(tag);
            });

            // Show container
            container.classList.remove('hidden');
        }
    </script>

    <!-- Mobile Menu JavaScript -->
    <script>
        document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
    </script>

    <!-- Flash messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <script>
                {% for category, message in messages %}
                    showNotification('{{ message }}', '{{ category }}');
                {% endfor %}
            </script>
        {% endif %}
    {% endwith %}

    <!-- Prevent page from becoming dull on back button -->
    <script>
        // Handle back-forward cache
        window.addEventListener('pageshow', function(event) {
            if (event.persisted) {
                // Page was loaded from back-forward cache
                document.body.style.opacity = '1';
                document.body.style.filter = 'none';
                document.body.style.transition = 'none';

                // Force refresh of styles
                document.body.classList.remove('bfcache');
                document.body.offsetHeight; // Trigger reflow
                document.body.classList.add('bfcache');
            }
        });

        // Handle when page becomes visible again
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                document.body.style.opacity = '1';
                document.body.style.filter = 'none';
            }
        });

        // Force styles on load
        window.addEventListener('load', function() {
            document.body.style.opacity = '1';
            document.body.style.filter = 'none';
            document.body.style.transition = 'none';
        });

        // Force styles immediately
        document.body.style.opacity = '1';
        document.body.style.filter = 'none';
        document.body.style.transition = 'none';
    </script>
</body>
</html>
