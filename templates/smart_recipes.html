<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Recipes - MealMind</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'organic-beige': '#F9F5ED',
                        'organic-cream': '#FDF9F0',
                        'rich-green': '#1A5E2A',
                        'soft-green': '#2D7A3D',
                        'accent-green': '#4A9B5E',
                        'warm-brown': '#8B4513',
                        'soft-gray': '#6B7280',
                        'light-sage': '#E8F5E8'
                    },
                    fontFamily: {
                        'serif': ['Playfair Display', 'serif'],
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom organic overrides -->
    <link rel="stylesheet" href="{{ url_for('static', filename='organic-overrides.css') }}">

    <!-- Enhanced UI Effects -->
    <link rel="stylesheet" href="{{ url_for('static', filename='enhanced-ui.css') }}">

    <!-- Prevent page from becoming dull on back button -->
    <style>
        body {
            opacity: 1 !important;
            filter: none !important;
            transition: none !important;
        }

        html, body {
            background-color: #F9F5ED !important;
        }

        * {
            opacity: 1 !important;
            filter: none !important;
        }

        body.bfcache {
            opacity: 1 !important;
            filter: none !important;
        }
    </style>

    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍽️</text></svg>">
</head>
<body class="bg-organic-beige font-sans text-rich-green min-h-screen transition-all duration-500" id="body">
    <!-- Parallax Background -->
    <div class="parallax-bg"></div>

    <!-- Theme Toggle Button -->
    <button class="theme-toggle" onclick="toggleTheme()" title="Toggle Dark/Light Mode">
        <span id="theme-icon">🌙</span>
    </button>
    <!-- Header -->
    <header class="neuro-nav sticky top-0 z-50">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Brand -->
                <div class="flex items-center space-x-3">
                    <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-12 w-12">
                    <div>
                        <h1 class="text-2xl font-serif font-bold text-rich-green tracking-tight">MealMind</h1>
                        <p class="text-xs text-soft-gray font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="{{ url_for('index') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Home</a>
                    <a href="{{ url_for('browse_recipes') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Discover</a>
                    <a href="{{ url_for('smart_recipes') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium border-b-2 border-rich-green">Smart Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                    {% if user %}
                    <a href="{{ url_for('profile') }}" class="text-rich-green hover:text-soft-green transition-colors font-medium">Profile</a>
                    <a href="{{ url_for('logout') }}" class="bg-rich-green text-organic-beige px-6 py-2 rounded-full hover:bg-soft-green transition-colors font-medium">Logout</a>
                    {% else %}
                    <a href="{{ url_for('login') }}" class="bg-rich-green text-organic-beige px-6 py-2 rounded-full hover:bg-soft-green transition-colors font-medium">Login</a>
                    {% endif %}
                </div>

                <!-- Mobile menu button -->
                <button class="md:hidden text-rich-green hover:text-soft-green" id="mobile-menu-toggle">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div class="md:hidden hidden" id="mobile-menu">
                <div class="px-2 pt-2 pb-3 space-y-1 bg-organic-cream border-t border-rich-green/10">
                    <a href="{{ url_for('index') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Home</a>
                    <a href="{{ url_for('browse_recipes') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Discover</a>
                    <a href="{{ url_for('smart_recipes') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium bg-light-sage rounded">Smart Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                    {% if user %}
                    <a href="{{ url_for('profile') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Profile</a>
                    <a href="{{ url_for('logout') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Logout</a>
                    {% else %}
                    <a href="{{ url_for('login') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Login</a>
                    {% endif %}
                </div>
            </div>
        </nav>
    </header>

    <main>
        <!-- Hero Section -->
        <section class="pt-8 pb-16 lg:pt-12 lg:pb-20 neuro-container mx-4 my-8">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h1 class="text-3xl lg:text-4xl font-serif font-bold text-rich-green mb-6">Smart Recipes</h1>
                <p class="text-lg lg:text-xl text-soft-gray mb-8 max-w-3xl mx-auto">
                    Let AI transform your ingredients into amazing recipes. Upload a photo or type what you have!
                </p>

                <!-- Step-by-Step Process -->
                <div class="max-w-7xl mx-auto">
                    <div class="flex items-stretch justify-center gap-6">
                        <!-- Step 1: Upload Ingredients -->
                        <div class="neuro rounded-2xl p-8 transition-all hover:shadow-lg flex-1 h-[32rem] tilt-card">
                            <div class="text-center mb-6">
                                <h3 class="text-xl font-serif font-bold text-rich-green">Step 1: Upload Ingredients</h3>
                            </div>

                            <div class="flex flex-col justify-center h-full space-y-6">
                                <div class="neuro-flat p-5 tilt-card">
                                    <div class="flex items-center mb-3">
                                        <span class="text-2xl mr-3">📷</span>
                                        <h4 class="font-semibold text-rich-green text-base">Upload Photo</h4>
                                    </div>
                                    <p class="text-soft-gray text-sm leading-relaxed">Select a photo of your ingredients. Our AI will analyze and identify the food items in it.</p>
                                </div>

                                <div class="text-center text-soft-gray font-medium text-sm">or</div>

                                <div class="neuro-flat p-5 tilt-card">
                                    <div class="flex items-center mb-3">
                                        <span class="text-2xl mr-3">⌨️</span>
                                        <h4 class="font-semibold text-rich-green text-base">Type Ingredients</h4>
                                    </div>
                                    <p class="text-soft-gray text-sm leading-relaxed">Prefer typing? Just list what you have at home (e.g., tomato, cheese, spinach).</p>
                                </div>
                            </div>
                        </div>

                        <!-- Arrow 1 -->
                        <div class="flex items-center justify-center px-4">
                            <div class="neuro rounded-full p-4 tilt-btn">
                                <svg class="w-6 h-6 text-rich-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- Step 2: Smart Analysis -->
                        <div class="neuro rounded-2xl p-8 transition-all hover:shadow-lg flex-1 h-[32rem] tilt-card">
                            <div class="text-center mb-6">
                                <h3 class="text-xl font-serif font-bold text-rich-green">Step 2: Smart Analysis</h3>
                            </div>

                            <div class="neuro-pressed p-6 flex flex-col justify-center h-full tilt-card float-element">
                                <div class="flex items-center mb-6">
                                    <span class="text-2xl mr-3">🤖</span>
                                    <h4 class="font-semibold text-rich-green text-lg">AI Matches Recipes</h4>
                                </div>
                                <p class="text-soft-gray text-sm mb-4 leading-relaxed">Once your ingredients are uploaded or listed, MealMind's intelligent algorithm goes to work. It evaluates your ingredients, understands your dietary preferences and filters through hundreds of recipes to find those that fit your needs, lifestyle, and taste.</p>
                                <p class="text-rich-green text-sm font-medium italic">We don't just find a recipe—we find the right one for you.</p>
                            </div>
                        </div>

                        <!-- Arrow 2 -->
                        <div class="flex items-center justify-center px-4">
                            <div class="neuro rounded-full p-4 tilt-btn">
                                <svg class="w-6 h-6 text-rich-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- Step 3: Get Personalized Recipes -->
                        <div class="neuro rounded-2xl p-8 transition-all hover:shadow-lg flex-1 h-[32rem] tilt-card">
                            <div class="text-center mb-6">
                                <h3 class="text-xl font-serif font-bold text-rich-green">Step 3: Get Personalized Recipes</h3>
                            </div>

                            <div class="neuro-flat p-6 flex flex-col justify-center h-full tilt-card float-element">
                                <div class="flex items-center mb-6">
                                    <span class="text-2xl mr-3">🍽️</span>
                                    <h4 class="font-semibold text-rich-green text-lg">Perfect Recipes</h4>
                                </div>
                                <p class="text-soft-gray text-sm mb-4 leading-relaxed">You'll receive a beautifully curated list of recipe suggestions tailored to what you have at home and what you're craving.</p>
                                <p class="text-soft-gray text-sm leading-relaxed">Each recipe comes with step-by-step instructions, prep and cook time, serving size, and detailed nutritional information—so you know exactly what you're eating.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Ingredient Finder Section -->
        <section id="input-methods" class="py-16 lg:py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl lg:text-4xl font-serif font-bold text-rich-green mb-4">Find Your Perfect Recipe</h2>
                    <p class="text-lg text-soft-gray max-w-2xl mx-auto">Choose your preferred method to discover amazing recipes tailored to your ingredients</p>
                </div>

                <!-- Warning Message -->
                <div id="warning-message" class="hidden max-w-4xl mx-auto mb-8">
                    <div class="bg-red-50 border border-red-200 rounded-xl p-4 flex items-center">
                        <div class="text-2xl mr-3">⚠️</div>
                        <div>
                            <h4 class="font-semibold text-red-800 mb-1">Please provide ingredients or apply filters</h4>
                            <p class="text-red-600 text-sm">Upload a photo, type ingredients, or select filters to get recipe suggestions.</p>
                        </div>
                    </div>
                </div>

                <form id="recipe-form" action="{{ url_for('find_recipes') }}" method="POST" enctype="multipart/form-data" class="max-w-6xl mx-auto">
                    <div class="grid lg:grid-cols-2 gap-8">
                        <!-- Photo Upload Method -->
                        <div class="bg-organic-beige rounded-2xl p-8 border border-rich-green/10 hover:border-rich-green/20 transition-colors">
                            <div class="text-center mb-6">
                                <div class="text-4xl mb-3">📸</div>
                                <h3 class="text-xl font-serif font-semibold text-rich-green mb-2">Smart Photo Recognition</h3>
                                <p class="text-soft-gray">Upload a photo and let AI identify your ingredients</p>
                            </div>

                            <div class="image-upload-zone" id="image-upload-area">
                                <input type="file" id="ingredients_image" name="ingredients_image" accept="image/*" style="display: none;">
                                <div class="upload-placeholder border-2 border-dashed border-rich-green/30 rounded-xl p-8 text-center hover:border-rich-green/50 transition-colors cursor-pointer bg-white" id="upload-placeholder">
                                    <div class="text-4xl mb-4 text-rich-green/60">📷</div>
                                    <div class="mb-4">
                                        <div class="text-lg font-medium text-rich-green mb-1">Drop your photo here</div>
                                        <div class="text-soft-gray">or click to browse</div>
                                    </div>
                                    <div class="text-sm text-soft-gray">
                                        Supports: JPG, PNG, GIF, WebP (max 16MB)
                                    </div>
                                </div>
                                <div id="image-preview" class="hidden mt-4">
                                    <img id="preview-img" class="w-full h-48 object-cover rounded-xl border border-rich-green/20">
                                    <button type="button" id="remove-image" class="mt-2 text-sm text-red-500 hover:text-red-700">Remove Image</button>
                                </div>
                            </div>
                        </div>

                        <!-- Text Input Method -->
                        <div class="bg-organic-beige rounded-2xl p-8 border border-rich-green/10 hover:border-rich-green/20 transition-colors">
                            <div class="text-center mb-6">
                                <div class="text-4xl mb-3">✍️</div>
                                <h3 class="text-xl font-serif font-semibold text-rich-green mb-2">Type Your Ingredients</h3>
                                <p class="text-soft-gray">Type your ingredients for precise control</p>
                            </div>

                            <div class="text-input-zone">
                                <textarea
                                    id="ingredients_text"
                                    name="ingredients_text"
                                    placeholder="Enter your ingredients here...

Examples:
• chicken breast, tomatoes, onion, garlic
• pasta, cheese, herbs, olive oil
• rice, vegetables, soy sauce"
                                    rows="8"
                                    class="w-full p-4 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none resize-none text-rich-green placeholder-soft-gray bg-white"
                                    oninput="identifyIngredientsFromText()"
                                ></textarea>
                                <div class="mt-3">
                                    <div class="text-sm text-soft-gray mb-2">
                                        💡 Tip: Our AI will automatically identify ingredients as you type
                                    </div>
                                    <div id="identified-ingredients-container" class="hidden">
                                        <div class="text-sm font-medium text-rich-green mb-2">🤖 AI Identified Ingredients:</div>
                                        <div id="identified-ingredients-tags" class="flex flex-wrap gap-2"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filter Options -->
                    <div class="mt-12 bg-white rounded-2xl p-8 border border-rich-green/10">
                        <div class="text-center mb-8">
                            <h3 class="text-2xl font-serif font-bold text-rich-green mb-2">🎯 Customize Your Results</h3>
                            <p class="text-soft-gray">Add filters to get more personalized recipe suggestions</p>
                        </div>

                        <div class="grid md:grid-cols-3 gap-6">
                            <!-- Cuisine Filter -->
                            <div>
                                <label for="cuisine_preference" class="block text-sm font-semibold text-rich-green mb-2">
                                    🌍 Cuisine Preference
                                </label>
                                <select id="cuisine_preference" name="cuisine_preference" class="w-full p-3 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none text-rich-green bg-white">
                                    <option value="">Any Cuisine</option>
                                    {% for cuisine in cuisines %}
                                    <option value="{{ cuisine }}">{{ cuisine }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- Diet Type Filter -->
                            <div>
                                <label for="diet_preference" class="block text-sm font-semibold text-rich-green mb-2">
                                    🥗 Diet Type
                                </label>
                                <select id="diet_preference" name="diet_preference" class="w-full p-3 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none text-rich-green bg-white">
                                    <option value="">Any Diet</option>
                                    {% for diet in diet_types %}
                                    <option value="{{ diet }}">{{ diet }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- Cooking Time Filter -->
                            <div>
                                <label for="cooking_time" class="block text-sm font-semibold text-rich-green mb-2">
                                    ⏱️ Cooking Time
                                </label>
                                <select id="cooking_time" name="cooking_time" class="w-full p-3 border border-rich-green/20 rounded-xl focus:border-rich-green focus:ring-2 focus:ring-rich-green/20 focus:outline-none text-rich-green bg-white">
                                    <option value="">Any Time</option>
                                    <option value="quick">Quick (Under 30 min)</option>
                                    <option value="medium">Medium (30-60 min)</option>
                                    <option value="long">Long (Over 60 min)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Action Selection -->
                    <div class="max-w-4xl mx-auto mb-8">
                        <h3 class="text-xl font-serif font-bold text-rich-green mb-6 text-center">Choose Your Action</h3>
                        <div class="grid md:grid-cols-2 gap-6">
                            <!-- Find Recipes Card -->
                            <div class="action-card border-2 border-rich-green/20 rounded-2xl p-6 cursor-pointer hover:border-rich-green/40 transition-colors" onclick="selectAction('find')">
                                <div class="text-center">
                                    <div class="text-4xl mb-4">🔍</div>
                                    <h4 class="text-xl font-serif font-bold text-rich-green mb-3">Find My Recipes</h4>
                                    <p class="text-soft-gray mb-4">Search through thousands of existing recipes and find the best matches for your ingredients with ML-powered matching percentages.</p>
                                    <div class="bg-light-sage rounded-lg p-3">
                                        <p class="text-sm text-rich-green font-medium">✨ Shows match percentages</p>
                                        <p class="text-sm text-rich-green font-medium">🎯 ML-powered matching</p>
                                        <p class="text-sm text-rich-green font-medium">📚 From recipe database</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Generate Recipes Card -->
                            <div class="action-card border-2 border-rich-green/20 rounded-2xl p-6 cursor-pointer hover:border-rich-green/40 transition-colors" onclick="selectAction('generate')">
                                <div class="text-center">
                                    <div class="text-4xl mb-4">🤖</div>
                                    <h4 class="text-xl font-serif font-bold text-rich-green mb-3">Generate My Recipes</h4>
                                    <p class="text-soft-gray mb-4">AI creates completely new, custom recipes using only your specified ingredients. Perfect for creative cooking!</p>
                                    <div class="bg-light-sage rounded-lg p-3">
                                        <p class="text-sm text-rich-green font-medium">🎨 AI-generated recipes</p>
                                        <p class="text-sm text-rich-green font-medium">🥗 Uses only your ingredients</p>
                                        <p class="text-sm text-rich-green font-medium">📸 Includes recipe images</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" id="recipe_action" name="recipe_action" value="">
                    </div>
                    <!-- Submit Button -->
                    <div class="text-center mt-8">
                        <button type="button" onclick="validateAndSubmit()" class="bg-rich-green text-organic-beige px-12 py-4 rounded-full text-lg font-semibold hover:bg-soft-green transition-colors shadow-lg hover:shadow-xl">
                            🔍 Find My Perfect Recipes
                        </button>
                    </div>
                </form>
            </div>
        </section>
    </main>
    <!-- Footer -->
    <footer class="bg-rich-green text-organic-beige py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand Section -->
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <img src="{{ url_for('static', filename='logo.svg') }}" alt="MealMind Logo" class="h-10 w-10">
                        <div>
                            <h3 class="text-2xl font-serif font-bold">MealMind</h3>
                            <p class="text-sm text-organic-beige/80 font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                        </div>
                    </div>
                    <p class="text-organic-beige/80 mb-4 max-w-md">
                        Discover delicious recipes crafted with quality ingredients. Transform your cooking experience with AI-powered recipe discovery.
                    </p>
                    <div class="flex space-x-4">
                        <span class="text-2xl">🍅</span>
                        <span class="text-2xl">🥕</span>
                        <span class="text-2xl">🌶️</span>
                        <span class="text-2xl">🥬</span>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li><a href="{{ url_for('index') }}" class="hover:text-organic-beige transition-colors">Home</a></li>
                        <li><a href="{{ url_for('browse_recipes') }}" class="hover:text-organic-beige transition-colors">Browse Recipes</a></li>
                        <li><a href="{{ url_for('meal_plan') }}" class="hover:text-organic-beige transition-colors">Meal Plan</a></li>
                        {% if user %}
                        <li><a href="{{ url_for('favorites') }}" class="hover:text-organic-beige transition-colors">My Favorites</a></li>
                        {% else %}
                        <li><a href="{{ url_for('register') }}" class="hover:text-organic-beige transition-colors">Join Free</a></li>
                        {% endif %}
                    </ul>
                </div>

                <!-- Features -->
                <div>
                    <h4 class="font-semibold mb-4">Features</h4>
                    <ul class="space-y-2 text-organic-beige/80">
                        <li>🤖 AI Recipe Discovery</li>
                        <li>📸 Photo Recognition</li>
                        <li>🥗 Diet Filtering</li>
                        <li>⏱️ Time-based Search</li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-organic-beige/20 mt-8 pt-8 text-center">
                <p class="text-organic-beige/60 text-sm">
                    © 2025 MealMind. Made with 💚 for food lovers everywhere.
                </p>
            </div>
        </div>
    </footer>



    <!-- JavaScript for functionality -->
    <script src="{{ url_for('static', filename='script.js') }}"></script>

    <!-- ML Ingredient Identification JavaScript -->
    <script>
        let identificationTimeout;

        function identifyIngredientsFromText() {
            // Clear previous timeout
            clearTimeout(identificationTimeout);

            // Set a new timeout to avoid too many API calls
            identificationTimeout = setTimeout(() => {
                const text = document.getElementById('ingredients_text').value.trim();

                if (text.length < 3) {
                    document.getElementById('identified-ingredients-container').classList.add('hidden');
                    return;
                }

                // Call ML API to identify ingredients
                fetch('/api/identify-ingredients', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ text: text })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.ingredients.length > 0) {
                        displayIdentifiedIngredients(data.ingredients);
                    } else {
                        document.getElementById('identified-ingredients-container').classList.add('hidden');
                    }
                })
                .catch(error => {
                    console.error('Error identifying ingredients:', error);
                });
            }, 500); // Wait 500ms after user stops typing
        }

        function displayIdentifiedIngredients(ingredients) {
            const container = document.getElementById('identified-ingredients-container');
            const tagsContainer = document.getElementById('identified-ingredients-tags');

            // Clear previous tags
            tagsContainer.innerHTML = '';

            // Add new tags
            ingredients.forEach(ingredient => {
                const tag = document.createElement('span');
                tag.className = 'ingredient-tag';
                tag.textContent = `${ingredient.name} (${ingredient.category})`;

                // Add confidence indicator
                if (ingredient.confidence > 0.8) {
                    tag.style.backgroundColor = '#E8F5E8';
                    tag.style.borderColor = '#1A5E2A';
                } else {
                    tag.style.backgroundColor = '#FEF3C7';
                    tag.style.borderColor = '#D97706';
                }

                tagsContainer.appendChild(tag);
            });

            // Show container
            container.classList.remove('hidden');
        }
    </script>

    <!-- Form Validation JavaScript -->
    <script>
        // Action selection functionality
        function selectAction(action) {
            console.log("Action selected:", action);
            
            // Remove previous selections
            document.querySelectorAll(".action-card").forEach(card => {
                card.classList.remove("border-rich-green", "bg-light-sage/20");
                card.classList.add("border-rich-green/20");
            });
            
            // Highlight selected card
            event.currentTarget.classList.remove("border-rich-green/20");
            event.currentTarget.classList.add("border-rich-green", "bg-light-sage/20");
            
            // Set the action value
            document.getElementById("recipe_action").value = action;
            console.log("Recipe action set to:", document.getElementById("recipe_action").value);
            
            // Update submit button text
            const submitButton = document.querySelector("button[onclick='validateAndSubmit()']");
            if (action === "find") {
                submitButton.innerHTML = "🔍 Find My Perfect Recipes";
            } else if (action === "generate") {
                submitButton.innerHTML = "🤖 Generate Custom Recipes";
            }
        }
        function validateAndSubmit() {
            const action = document.getElementById("recipe_action").value;
            if (!action) {
                alert("Please select an action: Find My Recipes or Generate My Recipes");
                return;
            }

            // Get form values
            const imageInput = document.getElementById('ingredients_image');
            const textInput = document.getElementById('ingredients_text');
            const cuisineFilter = document.getElementById('cuisine_preference');
            const dietFilter = document.getElementById('diet_preference');
            const timeFilter = document.getElementById('cooking_time');

            // Check if any input is provided
            const hasImage = imageInput && imageInput.files && imageInput.files.length > 0;
            const hasText = textInput && textInput.value.trim() !== '';
            const hasCuisineFilter = cuisineFilter && cuisineFilter.value !== '';
            const hasDietFilter = dietFilter && dietFilter.value !== '';
            const hasTimeFilter = timeFilter && timeFilter.value !== '';

            // For generate action, require ingredients
            if (action === 'generate' && !hasText && !hasImage) {
                const warningMessage = document.getElementById('warning-message');
                warningMessage.innerHTML = `
                    <div class="bg-red-50 border border-red-200 rounded-xl p-4 flex items-center">
                        <div class="text-2xl mr-3">⚠️</div>
                        <div>
                            <h4 class="font-semibold text-red-800 mb-1">Ingredients Required for Recipe Generation</h4>
                            <p class="text-red-600 text-sm">Please provide ingredients (text or image) to generate custom recipes.</p>
                        </div>
                    </div>
                `;
                warningMessage.classList.remove('hidden');
                warningMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
                setTimeout(() => warningMessage.classList.add('hidden'), 5000);
                return false;
            }

            // Check if at least one input method or filter is provided
            if (!hasImage && !hasText && !hasCuisineFilter && !hasDietFilter && !hasTimeFilter) {
                // Show warning message
                const warningMessage = document.getElementById('warning-message');
                warningMessage.classList.remove('hidden');

                // Scroll to warning message
                warningMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });

                // Hide warning after 5 seconds
                setTimeout(() => {
                    warningMessage.classList.add('hidden');
                }, 5000);

                return false;
            }

            // Hide warning message if visible
            document.getElementById('warning-message').classList.add('hidden');

            // Show loading state
            const submitButton = document.querySelector('button[onclick="validateAndSubmit()"]');
            const originalText = submitButton.innerHTML;
            if (action === 'generate') {
                submitButton.innerHTML = '🤖 Generating Custom Recipes...';
            } else {
                submitButton.innerHTML = '🔍 Finding Recipes...';
            }
            submitButton.disabled = true;

            // Submit the form
            const form = document.getElementById('recipe-form');
            form.submit();
        }


    </script>

    <!-- Mobile Menu JavaScript -->
    <script>
        document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
    </script>

    <!-- Prevent page from becoming dull on back button -->
    <script>
        window.addEventListener('pageshow', function(event) {
            if (event.persisted) {
                document.body.style.opacity = '1';
                document.body.style.filter = 'none';
                document.body.style.transition = 'none';
            }
        });

        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                document.body.style.opacity = '1';
                document.body.style.filter = 'none';
            }
        });

        window.addEventListener('load', function() {
            document.body.style.opacity = '1';
            document.body.style.filter = 'none';
            document.body.style.transition = 'none';
        });

        document.body.style.opacity = '1';
        document.body.style.filter = 'none';
        document.body.style.transition = 'none';

        // Image Upload Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const imageInput = document.getElementById('ingredients_image');
            const uploadPlaceholder = document.getElementById('upload-placeholder');
            const imagePreview = document.getElementById('image-preview');
            const previewImg = document.getElementById('preview-img');
            const removeImageBtn = document.getElementById('remove-image');

            // Click to upload
            uploadPlaceholder.addEventListener('click', function() {
                imageInput.click();
            });

            // Drag and drop functionality
            uploadPlaceholder.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadPlaceholder.classList.add('border-rich-green');
                uploadPlaceholder.classList.remove('border-rich-green/30');
            });

            uploadPlaceholder.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadPlaceholder.classList.remove('border-rich-green');
                uploadPlaceholder.classList.add('border-rich-green/30');
            });

            uploadPlaceholder.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadPlaceholder.classList.remove('border-rich-green');
                uploadPlaceholder.classList.add('border-rich-green/30');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleImageUpload(files[0]);
                }
            });

            // File input change
            imageInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    handleImageUpload(e.target.files[0]);
                }
            });

            // Remove image
            removeImageBtn.addEventListener('click', function() {
                imageInput.value = '';
                imagePreview.classList.add('hidden');
                uploadPlaceholder.classList.remove('hidden');
            });

            function handleImageUpload(file) {
                // Validate file type
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                if (!allowedTypes.includes(file.type)) {
                    alert('Please upload a valid image file (JPG, PNG, GIF, WebP)');
                    return;
                }

                // Validate file size (16MB max)
                if (file.size > 16 * 1024 * 1024) {
                    alert('File size must be less than 16MB');
                    return;
                }

                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    uploadPlaceholder.classList.add('hidden');
                    imagePreview.classList.remove('hidden');

                    // Simulate ingredient detection from image
                    simulateIngredientDetection(file.name);
                };
                reader.readAsDataURL(file);
            }

            function simulateIngredientDetection(filename) {
                // Show a loading indicator
                const loadingDiv = document.createElement('div');
                loadingDiv.id = 'detection-loading';
                loadingDiv.className = 'mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg';
                loadingDiv.innerHTML = `
                    <div class="flex items-center">
                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-3"></div>
                        <span class="text-blue-700 text-sm">🤖 AI is analyzing your image...</span>
                    </div>
                `;
                imagePreview.appendChild(loadingDiv);

                // Simulate detection delay
                setTimeout(() => {
                    // Remove loading indicator
                    const loading = document.getElementById('detection-loading');
                    if (loading) loading.remove();

                    // Simulate detected ingredients based on filename or random selection
                    const detectedIngredients = simulateDetectedIngredients(filename);

                    // Update the text input with detected ingredients
                    const textInput = document.getElementById('ingredients_text');
                    if (textInput.value.trim()) {
                        textInput.value += ', ' + detectedIngredients.join(', ');
                    } else {
                        textInput.value = detectedIngredients.join(', ');
                    }

                    // Show detection results
                    const resultsDiv = document.createElement('div');
                    resultsDiv.className = 'mt-4 p-3 bg-green-50 border border-green-200 rounded-lg';
                    resultsDiv.innerHTML = `
                        <div class="text-green-700 text-sm">
                            <strong>🎯 Detected Ingredients:</strong><br>
                            ${detectedIngredients.map(ing => `<span class="inline-block bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs mr-1 mt-1">${ing}</span>`).join('')}
                        </div>
                    `;
                    imagePreview.appendChild(resultsDiv);

                    // Trigger ingredient identification
                    identifyIngredientsFromText();
                }, 2000);
            }

            function simulateDetectedIngredients(filename) {
                const commonIngredients = {
                    'vegetables': ['tomato', 'onion', 'garlic', 'carrot', 'bell pepper', 'broccoli', 'spinach'],
                    'proteins': ['chicken breast', 'ground beef', 'eggs', 'salmon', 'tofu'],
                    'pantry': ['rice', 'pasta', 'olive oil', 'salt', 'pepper', 'cheese']
                };

                const filename_lower = filename.toLowerCase();
                let detected = [];

                // Try to detect from filename
                for (const [category, ingredients] of Object.entries(commonIngredients)) {
                    for (const ingredient of ingredients) {
                        if (filename_lower.includes(ingredient.replace(' ', '')) ||
                            ingredient.split(' ').some(word => filename_lower.includes(word))) {
                            detected.push(ingredient);
                        }
                    }
                }

                // If no matches, simulate a realistic kitchen scenario
                if (detected.length === 0) {
                    const scenarios = [
                        ['tomato', 'onion', 'garlic', 'basil', 'olive oil'],
                        ['chicken breast', 'bell pepper', 'onion', 'rice'],
                        ['eggs', 'cheese', 'spinach', 'tomato'],
                        ['ground beef', 'onion', 'garlic', 'tomato'],
                        ['salmon', 'broccoli', 'carrot', 'rice']
                    ];
                    detected = scenarios[Math.floor(Math.random() * scenarios.length)];
                }

                return detected.slice(0, 6); // Return max 6 ingredients
            }
        });

        // Theme switching functionality
        function toggleTheme() {
            const body = document.getElementById('body');
            const themeIcon = document.getElementById('theme-icon');

            if (body.classList.contains('dark')) {
                body.classList.remove('dark');
                themeIcon.textContent = '🌙';
                localStorage.setItem('theme', 'light');
            } else {
                body.classList.add('dark');
                themeIcon.textContent = '☀️';
                localStorage.setItem('theme', 'dark');
            }
        }

        // Load saved theme
        function loadTheme() {
            const savedTheme = localStorage.getItem('theme');
            const body = document.getElementById('body');
            const themeIcon = document.getElementById('theme-icon');

            if (savedTheme === 'dark') {
                body.classList.add('dark');
                themeIcon.textContent = '☀️';
            } else {
                body.classList.remove('dark');
                themeIcon.textContent = '🌙';
            }
        }

        // Initialize theme on page load
        loadTheme();
    </script>
</body>
</html>
