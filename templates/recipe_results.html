<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recipe Results - MealMind</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'organic-beige': '#F9F5ED',
                        'organic-cream': '#FDF9F0',
                        'rich-green': '#1A5E2A',
                        'soft-green': '#2D7A3D',
                        'accent-green': '#4A9B5E',
                        'warm-brown': '#8B4513',
                        'soft-gray': '#6B7280',
                        'light-sage': '#E8F5E8'
                    },
                    fontFamily: {
                        'serif': ['Playfair Display', 'serif'],
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom organic overrides -->
    <link rel="stylesheet" href="{{ url_for('static', filename='organic-overrides.css') }}">
    
    <!-- Enhanced UI Effects -->
    <link rel="stylesheet" href="{{ url_for('static', filename='enhanced-ui.css') }}">

    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍽️</text></svg>">
</head>
<body class="bg-organic-beige font-sans text-rich-green min-h-screen transition-all duration-500" id="body">
    <!-- Parallax Background -->
    <div class="parallax-bg"></div>
    
    <!-- Theme Toggle Button -->
    <button class="theme-toggle" onclick="toggleTheme()" title="Toggle Dark/Light Mode">
        <span id="theme-icon">🌙</span>
    </button>

    <!-- Header -->
    <header class="neuro-nav sticky top-0 z-50">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="{{ url_for('index') }}" class="text-2xl font-serif font-bold text-rich-green">
                        🍽️ MealMind
                    </a>
                    <span class="ml-2 text-sm text-soft-gray font-medium">Think Smart. Eat Better.</span>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="{{ url_for('index') }}" class="neuro-nav-item px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Home</a>
                    <a href="{{ url_for('browse_recipes') }}" class="neuro-nav-item px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Discover</a>
                    <a href="{{ url_for('smart_recipes') }}" class="neuro-nav-item active px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Smart Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="neuro-nav-item px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                    {% if session.user_id %}
                    <a href="{{ url_for('profile') }}" class="neuro-nav-item px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Profile</a>
                    <a href="{{ url_for('logout') }}" class="neuro-nav-item px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Logout</a>
                    {% else %}
                    <a href="{{ url_for('login') }}" class="neuro-nav-item px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Login</a>
                    {% endif %}
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-toggle" class="neuro-btn p-2 text-rich-green">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="hidden md:hidden pb-4">
                <div class="space-y-2">
                    <a href="{{ url_for('index') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Home</a>
                    <a href="{{ url_for('browse_recipes') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Discover</a>
                    <a href="{{ url_for('smart_recipes') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Smart Recipes</a>
                    <a href="{{ url_for('meal_plan') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                    {% if session.user_id %}
                    <a href="{{ url_for('profile') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Profile</a>
                    <a href="{{ url_for('logout') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Logout</a>
                    {% else %}
                    <a href="{{ url_for('login') }}" class="block px-3 py-2 text-rich-green hover:text-soft-green transition-colors font-medium">Login</a>
                    {% endif %}
                </div>
            </div>
        </nav>
    </header>

    <main>
        <!-- Header Section -->
        <section class="pt-8 pb-12 neuro-container mx-4 my-8">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                {% if is_generated %}
                <h1 class="text-3xl lg:text-4xl font-serif font-bold text-rich-green mb-4 float-element">🤖 AI Generated Recipes</h1>
                <p class="text-lg text-soft-gray mb-6 max-w-2xl mx-auto">
                    Here are custom recipes created just for your ingredients!
                </p>
                {% else %}
                <h1 class="text-3xl lg:text-4xl font-serif font-bold text-rich-green mb-4 float-element">🔍 Recipe Search Results</h1>
                <p class="text-lg text-soft-gray mb-6 max-w-2xl mx-auto">
                    Here are the perfect recipe matches for your ingredients!
                </p>
                {% endif %}

                <!-- Search Summary -->
                <div id="search-summary" class="neuro-flat p-4 rounded-xl mb-8 max-w-2xl mx-auto">
                    <p class="text-sm text-soft-gray">
                        <span class="font-semibold text-rich-green">Your ingredients:</span>
                        <span class="text-rich-green">{{ user_ingredients | join(', ') }}</span>
                    </p>
                    <p class="text-xs text-soft-gray mt-2">
                        Found {{ total_found }} {{ 'generated' if is_generated else 'matching' }} recipe{{ 's' if total_found != 1 else '' }}
                    </p>
                </div>
            </div>
        </section>

        <!-- Recipe Results -->
        <section class="pb-16">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {% if recipes %}
                <!-- Results Grid -->
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for recipe in recipes %}
                    <div class="neuro-card p-6 hover:shadow-lg transition-all duration-300 tilt-card">
                        <!-- Recipe Header -->
                        <div class="mb-4">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-lg font-serif font-bold text-rich-green">{{ recipe.name }}</h3>
                                {% if recipe.is_ai_generated %}
                                <span class="text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white px-2 py-1 rounded-full">AI Generated</span>
                                {% endif %}
                            </div>
                            <div class="flex items-center text-sm text-soft-gray space-x-4">
                                <span>🍽️ {{ recipe.cuisine }}</span>
                                <span>⏱️ {{ recipe.prep_time if recipe.prep_time else '30 min' }}</span>
                                <span>👥 {{ recipe.servings }} servings</span>
                            </div>
                        </div>

                        <!-- Match Score (for found recipes) -->
                        {% if not is_generated and recipe.match_percentage %}
                        <div class="mb-4">
                            <div class="flex items-center justify-between text-sm mb-1">
                                <span class="text-soft-gray">Match Score</span>
                                <span class="font-semibold text-rich-green">{{ recipe.match_percentage }}%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-gradient-to-r from-rich-green to-accent-green h-2 rounded-full" style="width: {{ recipe.match_percentage }}%"></div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Ingredients Preview -->
                        <div class="mb-4">
                            <h4 class="text-sm font-semibold text-rich-green mb-2">Key Ingredients:</h4>
                            <div class="flex flex-wrap gap-1">
                                {% if recipe.is_ai_generated %}
                                    {% for ingredient in recipe.ingredients[:4] %}
                                    <span class="text-xs bg-light-sage text-rich-green px-2 py-1 rounded-full">{{ ingredient.split()[1:] | join(' ') if ingredient.split() | length > 1 else ingredient }}</span>
                                    {% endfor %}
                                {% else %}
                                    {% for category, ingredients in recipe.ingredients.items() %}
                                        {% for ingredient in ingredients[:2] %}
                                        <span class="text-xs bg-light-sage text-rich-green px-2 py-1 rounded-full">{{ ingredient.split()[1:] | join(' ') if ingredient.split() | length > 1 else ingredient }}</span>
                                        {% endfor %}
                                    {% endfor %}
                                {% endif %}
                            </div>
                        </div>

                        <!-- Recipe Stats -->
                        <div class="grid grid-cols-3 gap-2 mb-4 text-center">
                            <div class="neuro-flat p-2 rounded">
                                <div class="text-xs text-soft-gray">Difficulty</div>
                                <div class="text-sm font-semibold text-rich-green">{{ recipe.difficulty }}</div>
                            </div>
                            <div class="neuro-flat p-2 rounded">
                                <div class="text-xs text-soft-gray">Diet</div>
                                <div class="text-sm font-semibold text-rich-green">{{ recipe.diet_type }}</div>
                            </div>
                            <div class="neuro-flat p-2 rounded">
                                <div class="text-xs text-soft-gray">Calories</div>
                                <div class="text-sm font-semibold text-rich-green">{{ recipe.calories }}</div>
                            </div>
                        </div>

                        <!-- Action Button -->
                        <button onclick="showRecipeDetails({{ recipe.id if not recipe.is_ai_generated else loop.index }}, {{ 'true' if recipe.is_ai_generated else 'false' }})"
                                class="w-full neuro-btn text-rich-green py-2 font-semibold transition-all duration-300 tilt-btn">
                            {% if recipe.is_ai_generated %}🤖 View AI Recipe{% else %}📖 View Recipe{% endif %}
                        </button>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <!-- No Results State -->
                <div class="text-center py-12">
                    <div class="neuro-card p-8 max-w-md mx-auto">
                        <div class="text-4xl mb-4">😔</div>
                        <h3 class="text-xl font-serif font-semibold text-rich-green mb-2">No Recipes Found</h3>
                        <p class="text-soft-gray mb-6">We couldn't find any recipes matching your ingredients. Try different ingredients or check your spelling.</p>
                        <a href="{{ url_for('smart_recipes') }}" class="neuro-btn text-rich-green px-6 py-3 font-semibold transition-all duration-300 tilt-btn">
                            Try Again
                        </a>
                    </div>
                </div>
                {% endif %}

                <!-- Back to Search -->
                <div class="text-center mt-12">
                    <a href="{{ url_for('smart_recipes') }}" class="neuro-container text-rich-green px-8 py-4 font-semibold transition-all duration-300 inline-flex items-center gap-2 tilt-btn">
                        <span>← Search Again</span>
                    </a>
                </div>
            </div>
        </section>
    </main>

    <!-- Recipe Detail Modal -->
    <div id="recipe-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="neuro-container p-8 max-w-4xl w-full mx-4 relative max-h-[90vh] overflow-y-auto">
            <button class="absolute top-4 right-4 text-2xl text-soft-gray hover:text-rich-green transition-colors" onclick="closeRecipeModal()">&times;</button>
            <div id="modal-content">
                <!-- Recipe details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="neuro-nav mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid md:grid-cols-4 gap-8">
                <div class="md:col-span-2">
                    <div class="flex items-center mb-4">
                        <span class="text-2xl mr-2">🍽️</span>
                        <span class="text-xl font-serif font-bold text-rich-green">MealMind</span>
                    </div>
                    <p class="text-soft-gray mb-4">Think Smart. Eat Better.</p>
                    <p class="text-sm text-soft-gray">Transform your ingredients into culinary masterpieces with AI-powered recipe discovery.</p>
                </div>
                
                <div>
                    <h3 class="font-semibold text-rich-green mb-4">Quick Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="{{ url_for('index') }}" class="text-soft-gray hover:text-rich-green transition-colors">Home</a></li>
                        <li><a href="{{ url_for('browse_recipes') }}" class="text-soft-gray hover:text-rich-green transition-colors">Discover</a></li>
                        <li><a href="{{ url_for('smart_recipes') }}" class="text-soft-gray hover:text-rich-green transition-colors">Smart Recipes</a></li>
                        <li><a href="{{ url_for('meal_plan') }}" class="text-soft-gray hover:text-rich-green transition-colors">Meal Plan</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="font-semibold text-rich-green mb-4">Features</h3>
                    <ul class="space-y-2 text-sm">
                        <li class="text-soft-gray">🤖 AI Recipe Matching</li>
                        <li class="text-soft-gray">📸 Ingredient Recognition</li>
                        <li class="text-soft-gray">📅 Meal Planning</li>
                        <li class="text-soft-gray">❤️ Save Favorites</li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-rich-green/10 mt-8 pt-8 text-center">
                <p class="text-sm text-soft-gray">&copy; 2024 MealMind. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Store recipes data for modal display
        const recipesData = {{ recipes | tojson | safe }};
        const isGenerated = {{ 'true' if is_generated else 'false' }};

        function showRecipeDetails(recipeId, isAiGenerated = false) {
            // Show modal
            document.getElementById('recipe-modal').classList.remove('hidden');
            document.getElementById('recipe-modal').classList.add('flex');

            let recipe;
            if (isAiGenerated) {
                // For AI generated recipes, use index-based lookup
                recipe = recipesData[recipeId - 1];
            } else {
                // For regular recipes, find by ID
                recipe = recipesData.find(r => r.id == recipeId);
            }

            if (recipe) {
                displayRecipeDetails(recipe);
            } else {
                document.getElementById('modal-content').innerHTML = '<div class="text-center py-8"><div class="text-4xl mb-4">😞</div><p>Recipe not found</p></div>';
            }
        }

        function displayRecipeDetails(recipe) {
            const content = document.getElementById('modal-content');

            // Handle ingredients display for both AI generated and regular recipes
            let ingredientsHtml = '';
            if (recipe.is_ai_generated && Array.isArray(recipe.ingredients)) {
                // AI generated recipes have ingredients as array
                ingredientsHtml = `
                    <div class="neuro-flat p-4 rounded-lg">
                        <h3 class="font-semibold text-rich-green mb-4">🥘 Ingredients</h3>
                        <ul class="space-y-1">
                            ${recipe.ingredients.map(ingredient => `
                                <li class="text-sm text-soft-gray flex items-center">
                                    <span class="w-2 h-2 bg-rich-green rounded-full mr-3"></span>
                                    ${ingredient}
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            } else if (recipe.ingredients && typeof recipe.ingredients === 'object') {
                // Regular recipes have ingredients as object with categories
                ingredientsHtml = `
                    <div class="neuro-flat p-4 rounded-lg">
                        <h3 class="font-semibold text-rich-green mb-4">🥘 Ingredients</h3>
                        ${Object.entries(recipe.ingredients).map(([category, items]) => `
                            <div class="mb-3">
                                <h4 class="text-sm font-semibold text-rich-green mb-2">${category.charAt(0).toUpperCase() + category.slice(1)}:</h4>
                                <ul class="space-y-1 ml-4">
                                    ${items.map(ingredient => `
                                        <li class="text-sm text-soft-gray flex items-center">
                                            <span class="w-2 h-2 bg-rich-green rounded-full mr-3"></span>
                                            ${ingredient}
                                        </li>
                                    `).join('')}
                                </ul>
                            </div>
                        `).join('')}
                    </div>
                `;
            }

            // Handle cooking tips for AI generated recipes
            let tipsHtml = '';
            if (recipe.is_ai_generated && recipe.cooking_tips && recipe.cooking_tips.length > 0) {
                tipsHtml = `
                    <div class="neuro-flat p-4 rounded-lg">
                        <h3 class="font-semibold text-rich-green mb-4">💡 Cooking Tips</h3>
                        <ul class="space-y-2">
                            ${recipe.cooking_tips.map(tip => `
                                <li class="text-sm text-soft-gray flex items-start">
                                    <span class="text-rich-green mr-2">•</span>
                                    ${tip}
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            }

            content.innerHTML = `
                <div class="space-y-6">
                    <div class="text-center">
                        <h2 class="text-2xl font-serif font-bold text-rich-green mb-2">${recipe.name}</h2>
                        ${recipe.is_ai_generated ? '<span class="inline-block bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm mb-4">🤖 AI Generated</span>' : ''}
                        <div class="flex justify-center items-center space-x-4 text-sm text-soft-gray">
                            <span>🍽️ ${recipe.cuisine || 'International'}</span>
                            <span>⭐ ${recipe.difficulty || 'Medium'}</span>
                            <span>🥗 ${recipe.diet_type || 'Mixed'}</span>
                        </div>
                    </div>

                    <div class="grid md:grid-cols-3 gap-4 text-center">
                        <div class="neuro-flat p-4 rounded-lg">
                            <div class="text-2xl mb-2">⏱️</div>
                            <div class="font-semibold text-rich-green">${recipe.prep_time || '30 min'}</div>
                            <div class="text-sm text-soft-gray">Prep Time</div>
                        </div>
                        <div class="neuro-flat p-4 rounded-lg">
                            <div class="text-2xl mb-2">🍽️</div>
                            <div class="font-semibold text-rich-green">${recipe.servings || 2} servings</div>
                            <div class="text-sm text-soft-gray">Serves</div>
                        </div>
                        <div class="neuro-flat p-4 rounded-lg">
                            <div class="text-2xl mb-2">🔥</div>
                            <div class="font-semibold text-rich-green">${recipe.calories || 300} cal</div>
                            <div class="text-sm text-soft-gray">Per Serving</div>
                        </div>
                    </div>

                    ${ingredientsHtml}

                    ${recipe.instructions && recipe.instructions.length > 0 ? `
                    <div class="neuro-flat p-4 rounded-lg">
                        <h3 class="font-semibold text-rich-green mb-4">👨‍🍳 Instructions</h3>
                        <ol class="space-y-3">
                            ${recipe.instructions.map((step, index) => `
                                <li class="flex">
                                    <span class="neuro-btn w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold text-rich-green mr-4 mt-1 flex-shrink-0">${index + 1}</span>
                                    <span class="text-sm text-soft-gray leading-relaxed">${step}</span>
                                </li>
                            `).join('')}
                        </ol>
                    </div>
                    ` : ''}

                    ${tipsHtml}

                    ${recipe.is_ai_generated && recipe.flavor_profile ? `
                    <div class="neuro-flat p-4 rounded-lg">
                        <h3 class="font-semibold text-rich-green mb-2">🌟 Flavor Profile</h3>
                        <p class="text-sm text-soft-gray">${recipe.flavor_profile}</p>
                    </div>
                    ` : ''}

                    <div class="flex gap-4">
                        <button onclick="closeRecipeModal()" class="flex-1 neuro-btn text-rich-green py-3 px-6 font-semibold transition-all duration-300 tilt-btn">
                            Close
                        </button>
                        ${recipe.source_url ? `
                        <a href="${recipe.source_url}" target="_blank" class="flex-1 glow-btn text-white py-3 px-6 font-semibold transition-all duration-300 text-center tilt-btn">
                            View Original
                        </a>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        function closeRecipeModal() {
            document.getElementById('recipe-modal').classList.add('hidden');
            document.getElementById('recipe-modal').classList.remove('flex');
        }

        function showNoResults() {
            document.getElementById('loading-state').classList.add('hidden');
            document.getElementById('no-results').classList.remove('hidden');
        }

        // Theme switching functionality
        function toggleTheme() {
            const body = document.getElementById('body');
            const themeIcon = document.getElementById('theme-icon');

            if (body.classList.contains('dark')) {
                body.classList.remove('dark');
                themeIcon.textContent = '🌙';
                localStorage.setItem('theme', 'light');
            } else {
                body.classList.add('dark');
                themeIcon.textContent = '☀️';
                localStorage.setItem('theme', 'dark');
            }
        }

        // Load saved theme
        function loadTheme() {
            const savedTheme = localStorage.getItem('theme');
            const body = document.getElementById('body');
            const themeIcon = document.getElementById('theme-icon');

            if (savedTheme === 'dark') {
                body.classList.add('dark');
                themeIcon.textContent = '☀️';
            } else {
                body.classList.remove('dark');
                themeIcon.textContent = '🌙';
            }
        }

        // Initialize theme on page load
        loadTheme();

        // Mobile menu toggle
        document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Close modal when clicking outside
        document.getElementById('recipe-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeRecipeModal();
            }
        });
    </script>
</body>
</html>
