"""
AI Recipe Generator for MealMind
Uses AI to generate custom recipes based on user ingredients
"""

import json
import random
import re
from typing import List, Dict, Any

class AIRecipeGenerator:
    def __init__(self):
        """Initialize the AI Recipe Generator"""
        self.cuisine_styles = {
            'Italian': {
                'cooking_methods': ['sauté', 'simmer', 'roast', 'grill'],
                'flavor_profiles': ['garlic', 'herbs', 'olive oil', 'tomato-based'],
                'techniques': ['al dente', 'soffritto', 'risotto method']
            },
            'Chinese': {
                'cooking_methods': ['stir-fry', 'steam', 'braise', 'deep-fry'],
                'flavor_profiles': ['ginger', 'garlic', 'soy-based', 'sweet and sour'],
                'techniques': ['wok hei', 'velvet coating', 'blanching']
            },
            'Indian': {
                'cooking_methods': ['curry', 'tandoor', 'steam', 'sauté'],
                'flavor_profiles': ['spicy', 'aromatic', 'curry-based', 'yogurt-based'],
                'techniques': ['tempering', 'dum cooking', 'marination']
            },
            'American': {
                'cooking_methods': ['grill', 'roast', 'fry', 'bake'],
                'flavor_profiles': ['hearty', 'comfort food', 'barbecue', 'cheese-based'],
                'techniques': ['caramelization', 'smoking', 'grilling']
            },
            'Mexican': {
                'cooking_methods': ['sauté', 'grill', 'simmer', 'roast'],
                'flavor_profiles': ['spicy', 'lime-based', 'chili-based', 'fresh herbs'],
                'techniques': ['charring', 'slow cooking', 'fresh preparation']
            }
        }
        
    def generate_ai_recipe(self, ingredients: List[str], servings: int = 2) -> Dict[str, Any]:
        """Generate a custom recipe using AI-like logic"""
        
        # Analyze ingredients to determine cuisine and cooking style
        cuisine = self._determine_optimal_cuisine(ingredients)
        cooking_style = self.cuisine_styles.get(cuisine, self.cuisine_styles['American'])
        
        # Generate recipe components
        recipe_name = self._generate_creative_recipe_name(ingredients, cuisine)
        cooking_method = self._select_optimal_cooking_method(ingredients, cooking_style)
        
        # Create detailed recipe structure
        recipe = {
            'id': f'ai_generated_{random.randint(10000, 99999)}',
            'name': recipe_name,
            'cuisine': cuisine,
            'servings': servings,
            'ingredients': self._format_ingredients_with_quantities(ingredients, servings),
            'instructions': self._generate_ai_instructions(ingredients, cooking_method, cuisine, servings),
            'prep_time': self._calculate_prep_time(ingredients, cooking_method),
            'cook_time': self._calculate_cook_time(ingredients, cooking_method),
            'difficulty': self._assess_difficulty(ingredients, cooking_method),
            'calories': self._estimate_calories(ingredients, servings),
            'diet_type': self._determine_diet_type(ingredients),
            'cooking_tips': self._generate_ai_cooking_tips(ingredients, cooking_method, cuisine),
            'flavor_profile': self._analyze_flavor_profile(ingredients, cuisine),
            'is_ai_generated': True
        }
        
        return recipe
    
    def _determine_optimal_cuisine(self, ingredients: List[str]) -> str:
        """AI logic to determine the best cuisine for given ingredients"""
        ingredient_text = ' '.join(ingredients).lower()
        
        # Cuisine scoring based on ingredient analysis
        cuisine_scores = {}
        
        for cuisine, style in self.cuisine_styles.items():
            score = 0
            
            # Score based on typical ingredients for each cuisine
            if cuisine == 'Italian':
                if any(ing in ingredient_text for ing in ['tomato', 'basil', 'cheese', 'pasta', 'olive']):
                    score += 3
                if any(ing in ingredient_text for ing in ['garlic', 'onion']):
                    score += 2
                    
            elif cuisine == 'Chinese':
                if any(ing in ingredient_text for ing in ['ginger', 'soy', 'rice', 'sesame']):
                    score += 3
                if any(ing in ingredient_text for ing in ['garlic', 'onion', 'vegetable']):
                    score += 2
                    
            elif cuisine == 'Indian':
                if any(ing in ingredient_text for ing in ['curry', 'turmeric', 'cumin', 'garam']):
                    score += 3
                if any(ing in ingredient_text for ing in ['onion', 'garlic', 'ginger', 'tomato']):
                    score += 2
                    
            elif cuisine == 'Mexican':
                if any(ing in ingredient_text for ing in ['lime', 'cilantro', 'chili', 'cumin']):
                    score += 3
                if any(ing in ingredient_text for ing in ['onion', 'tomato', 'pepper']):
                    score += 2
                    
            elif cuisine == 'American':
                if any(ing in ingredient_text for ing in ['cheese', 'beef', 'potato', 'bacon']):
                    score += 3
                if any(ing in ingredient_text for ing in ['onion', 'garlic']):
                    score += 1
            
            cuisine_scores[cuisine] = score
        
        # Return cuisine with highest score, default to fusion if tie
        best_cuisine = max(cuisine_scores, key=cuisine_scores.get)
        return best_cuisine if cuisine_scores[best_cuisine] > 0 else 'Fusion'
    
    def _generate_creative_recipe_name(self, ingredients: List[str], cuisine: str) -> str:
        """Generate realistic recipe names based on actual dishes"""
        ingredient_text = ' '.join(ingredients).lower()

        # Detect main protein
        main_protein = None
        if 'chicken' in ingredient_text:
            main_protein = 'chicken'
        elif 'beef' in ingredient_text:
            main_protein = 'beef'
        elif 'pork' in ingredient_text:
            main_protein = 'pork'
        elif 'fish' in ingredient_text or 'salmon' in ingredient_text:
            main_protein = 'fish'
        elif 'shrimp' in ingredient_text:
            main_protein = 'shrimp'
        elif 'turkey' in ingredient_text:
            main_protein = 'turkey'
        elif 'egg' in ingredient_text:
            main_protein = 'egg'

        # Detect key vegetables and ingredients
        has_tomato = 'tomato' in ingredient_text
        has_onion = 'onion' in ingredient_text
        has_pepper = 'pepper' in ingredient_text
        has_mushroom = 'mushroom' in ingredient_text
        has_broccoli = 'broccoli' in ingredient_text
        has_spinach = 'spinach' in ingredient_text
        has_garlic = 'garlic' in ingredient_text
        has_basil = 'basil' in ingredient_text
        has_rice = 'rice' in ingredient_text
        has_pasta = 'pasta' in ingredient_text

        # Generate realistic names based on cuisine and ingredients
        if cuisine == 'Italian':
            if main_protein == 'chicken':
                if has_tomato and has_basil:
                    return "Chicken Margherita"
                elif has_mushroom:
                    return "Chicken Marsala"
                elif has_pepper:
                    return "Chicken Cacciatore"
                elif has_spinach:
                    return "Chicken Florentine"
                else:
                    return "Italian Herb Chicken"
            elif main_protein == 'beef':
                if has_tomato:
                    return "Beef Marinara"
                else:
                    return "Italian Beef Stew"
            elif has_pasta:
                if has_tomato and has_basil:
                    return "Pasta Marinara"
                elif has_mushroom:
                    return "Mushroom Pasta"
                else:
                    return "Pasta Primavera"
            else:
                return "Italian Vegetable Medley"

        elif cuisine == 'Chinese' or cuisine == 'Asian':
            if main_protein == 'chicken':
                if has_broccoli:
                    return "Chicken and Broccoli"
                elif has_pepper:
                    return "Chicken Bell Pepper Stir Fry"
                elif has_mushroom:
                    return "Chicken Mushroom Stir Fry"
                else:
                    return "General Tso's Chicken"
            elif main_protein == 'beef':
                if has_broccoli:
                    return "Beef and Broccoli"
                elif has_pepper:
                    return "Pepper Steak"
                else:
                    return "Mongolian Beef"
            elif main_protein == 'shrimp':
                if has_rice:
                    return "Shrimp Fried Rice"
                else:
                    return "Honey Garlic Shrimp"
            elif has_rice:
                return "Vegetable Fried Rice"
            else:
                return "Mixed Vegetable Stir Fry"

        elif cuisine == 'Mexican':
            if main_protein == 'chicken':
                if has_pepper and has_onion:
                    return "Chicken Fajitas"
                else:
                    return "Mexican Chicken Bowl"
            elif main_protein == 'beef':
                if has_pepper and has_onion:
                    return "Beef Fajitas"
                else:
                    return "Mexican Beef Skillet"
            else:
                return "Vegetarian Burrito Bowl"

        elif cuisine == 'Indian':
            if main_protein == 'chicken':
                if has_tomato:
                    return "Chicken Tikka Masala"
                else:
                    return "Chicken Curry"
            elif main_protein == 'beef':
                return "Beef Curry"
            else:
                return "Vegetable Curry"

        else:  # American/General
            if main_protein == 'chicken':
                if has_mushroom:
                    return "Chicken and Mushroom Skillet"
                elif has_broccoli:
                    return "Chicken and Broccoli Casserole"
                elif has_pepper and has_onion:
                    return "Chicken Fajita Skillet"
                else:
                    return "Pan-Seared Chicken"
            elif main_protein == 'beef':
                if has_mushroom:
                    return "Beef Stroganoff"
                elif has_pepper and has_onion:
                    return "Pepper Steak"
                else:
                    return "Beef and Vegetable Skillet"
            elif main_protein == 'egg':
                if has_pepper or has_onion or has_mushroom:
                    return "Vegetable Omelet"
                else:
                    return "Scrambled Eggs"
            else:
                return "Garden Vegetable Medley"

        # Fallback
        return f"{cuisine} Style Mixed Dish"
    
    def _select_optimal_cooking_method(self, ingredients: List[str], cooking_style: Dict) -> str:
        """AI selects the best cooking method based on ingredients"""
        ingredient_text = ' '.join(ingredients).lower()
        
        # Analyze ingredients to determine best cooking method
        if any(meat in ingredient_text for meat in ['chicken', 'beef', 'pork', 'fish']):
            if 'tender' in ingredient_text or 'breast' in ingredient_text:
                return random.choice(['sauté', 'grill', 'pan-fry'])
            else:
                return random.choice(['braise', 'roast', 'slow-cook'])
        
        elif any(veg in ingredient_text for veg in ['vegetable', 'pepper', 'onion', 'mushroom']):
            return random.choice(['stir-fry', 'sauté', 'roast'])
        
        elif any(grain in ingredient_text for grain in ['rice', 'pasta', 'noodle']):
            return random.choice(['boil', 'steam', 'simmer'])
        
        else:
            return random.choice(cooking_style['cooking_methods'])
    
    def _format_ingredients_with_quantities(self, ingredients: List[str], servings: int) -> List[str]:
        """Format ingredients with proper quantities for servings"""
        formatted_ingredients = []
        
        for ingredient in ingredients:
            # If ingredient already has quantity, scale it
            if re.search(r'\d+', ingredient):
                formatted_ingredients.append(self._scale_ingredient_quantity(ingredient, servings))
            else:
                # Add appropriate quantity based on ingredient type
                formatted_ingredients.append(self._add_smart_quantity(ingredient, servings))
        
        return formatted_ingredients
    
    def _scale_ingredient_quantity(self, ingredient: str, servings: int) -> str:
        """Scale ingredient quantity based on servings"""
        # Extract number from ingredient
        numbers = re.findall(r'\d+(?:\.\d+)?', ingredient)
        if numbers:
            original_qty = float(numbers[0])
            scaled_qty = original_qty * (servings / 2)  # Base is 2 servings
            
            # Replace the number in the ingredient string
            scaled_ingredient = re.sub(r'\d+(?:\.\d+)?', str(int(scaled_qty) if scaled_qty.is_integer() else f"{scaled_qty:.1f}"), ingredient, count=1)
            return scaled_ingredient
        
        return ingredient
    
    def _add_smart_quantity(self, ingredient: str, servings: int) -> str:
        """Add precise, realistic quantities to ingredients"""
        ingredient_lower = ingredient.lower().strip()

        # Proteins - precise amounts
        if 'chicken breast' in ingredient_lower:
            qty = servings * 6  # 6 oz per serving
            return f"{qty} oz boneless, skinless chicken breast"
        elif 'ground beef' in ingredient_lower:
            qty = servings * 0.25  # 1/4 lb per serving
            return f"{qty:.1f} lbs ground beef (80/20)"
        elif 'chicken' in ingredient_lower:
            qty = servings * 6
            return f"{qty} oz chicken"
        elif 'beef' in ingredient_lower:
            qty = servings * 0.25
            return f"{qty:.1f} lbs beef"
        elif 'fish' in ingredient_lower or 'salmon' in ingredient_lower:
            qty = servings * 5
            return f"{qty} oz fresh fish fillet"
        elif 'shrimp' in ingredient_lower:
            qty = servings * 0.25
            return f"{qty:.1f} lbs large shrimp, peeled and deveined"
        elif 'turkey' in ingredient_lower:
            qty = servings * 6
            return f"{qty} oz turkey breast"

        # Vegetables - realistic portions
        elif 'onion' in ingredient_lower:
            qty = "1 large" if servings <= 3 else "2 medium"
            return f"{qty} yellow onion, diced"
        elif 'tomato' in ingredient_lower:
            qty = max(2, servings)
            return f"{qty} medium tomatoes, chopped"
        elif 'bell pepper' in ingredient_lower or ('pepper' in ingredient_lower and 'black' not in ingredient_lower):
            qty = "1 large" if servings <= 3 else "2 medium"
            return f"{qty} bell pepper, sliced"
        elif 'mushroom' in ingredient_lower:
            qty = servings * 4
            return f"{qty} oz mushrooms, sliced"
        elif 'broccoli' in ingredient_lower:
            qty = "1 large head" if servings <= 3 else "2 medium heads"
            return f"{qty} broccoli, cut into florets"
        elif 'spinach' in ingredient_lower:
            qty = servings * 2
            return f"{qty} cups fresh spinach leaves"
        elif 'carrot' in ingredient_lower:
            qty = servings
            return f"{qty} medium carrots, sliced"

        # Aromatics
        elif 'garlic' in ingredient_lower:
            qty = max(2, int(servings * 1.5))
            return f"{qty} cloves garlic, minced"
        elif 'ginger' in ingredient_lower:
            return "1 inch piece fresh ginger, minced"

        # Grains and starches
        elif 'rice' in ingredient_lower:
            qty = servings * 0.5
            return f"{qty:.1f} cups long-grain white rice"
        elif 'pasta' in ingredient_lower:
            qty = servings * 3
            return f"{qty} oz pasta"
        elif 'quinoa' in ingredient_lower:
            qty = servings * 0.375
            return f"{qty:.1f} cups quinoa"

        # Dairy
        elif 'cheese' in ingredient_lower:
            if 'parmesan' in ingredient_lower:
                qty = servings * 2
                return f"{qty} tbsp grated Parmesan cheese"
            else:
                qty = servings * 2
                return f"{qty} oz cheese, shredded"
        elif 'milk' in ingredient_lower:
            qty = servings * 0.25
            return f"{qty:.1f} cups whole milk"
        elif 'butter' in ingredient_lower:
            qty = servings * 2
            return f"{qty} tbsp unsalted butter"

        # Oils and fats
        elif 'olive oil' in ingredient_lower:
            qty = servings * 2
            return f"{qty} tbsp extra virgin olive oil"
        elif 'oil' in ingredient_lower:
            qty = servings * 2
            return f"{qty} tbsp cooking oil"

        # Herbs and spices
        elif 'basil' in ingredient_lower:
            qty = servings * 2
            return f"{qty} tbsp fresh basil, chopped"
        elif 'oregano' in ingredient_lower:
            qty = max(1, servings)
            return f"{qty} tsp dried oregano"
        elif 'thyme' in ingredient_lower:
            qty = max(1, servings)
            return f"{qty} tsp fresh thyme leaves"
        elif 'salt' in ingredient_lower:
            return "Salt to taste"
        elif 'pepper' in ingredient_lower and 'bell' not in ingredient_lower:
            return "Black pepper to taste"
        elif 'paprika' in ingredient_lower:
            qty = max(1, int(servings * 0.5))
            return f"{qty} tsp paprika"
        elif 'cumin' in ingredient_lower:
            qty = max(1, int(servings * 0.5))
            return f"{qty} tsp ground cumin"

        # Eggs
        elif 'egg' in ingredient_lower:
            qty = max(2, servings)
            return f"{qty} large eggs"

        # Condiments and sauces
        elif 'soy sauce' in ingredient_lower:
            qty = servings * 2
            return f"{qty} tbsp low-sodium soy sauce"
        elif 'vinegar' in ingredient_lower:
            qty = max(1, servings)
            return f"{qty} tbsp vinegar"
        elif 'broth' in ingredient_lower or 'stock' in ingredient_lower:
            qty = servings * 0.5
            return f"{qty:.1f} cups chicken broth"

        # Default for unrecognized ingredients
        else:
            qty = servings * 0.5
            return f"{qty:.1f} cups {ingredient}"
    
    def _generate_ai_instructions(self, ingredients: List[str], cooking_method: str, cuisine: str, servings: int) -> List[str]:
        """Generate detailed, precise cooking instructions with specific steps and timing"""
        instructions = []
        ingredient_text = ' '.join(ingredients).lower()

        # Preparation phase with specific steps
        instructions.append("PREPARATION (10-15 minutes):")
        instructions.append("1. Read through the entire recipe before starting")
        instructions.append("2. Gather all ingredients and measure them out")

        # Specific prep instructions based on ingredients
        if 'onion' in ingredient_text:
            instructions.append("3. Dice onions into ¼-inch pieces for even cooking")
        if 'garlic' in ingredient_text:
            instructions.append("4. Mince garlic cloves finely (avoid crushing to prevent bitterness)")
        if any(meat in ingredient_text for meat in ['chicken', 'beef', 'pork', 'fish']):
            instructions.append("5. Pat meat completely dry with paper towels")
            instructions.append("6. Season meat with salt and pepper, let sit 10-15 minutes at room temperature")
        if 'pepper' in ingredient_text and 'bell' in ingredient_text:
            instructions.append("7. Remove seeds and white pith from bell peppers, slice into strips")
        if 'mushroom' in ingredient_text:
            instructions.append("8. Clean mushrooms with damp paper towel, slice evenly")
        if 'tomato' in ingredient_text:
            instructions.append("9. Core tomatoes and chop into ½-inch pieces")

        # Cooking phase with precise timing and temperatures
        instructions.append(f"\nCOOKING ({cooking_method.upper()}):")

        if cooking_method in ['sauté', 'stir-fry']:
            instructions.append("1. Heat a large skillet or wok over medium-high heat (about 375°F) for 2-3 minutes")
            instructions.append("2. Add oil and swirl to coat - oil should shimmer but not smoke")

            if any(meat in ingredient_text for meat in ['chicken', 'beef', 'pork']):
                instructions.append("3. Add seasoned meat in single layer, don't overcrowd - cook 3-4 minutes without moving")
                instructions.append("4. Flip meat and cook another 3-4 minutes until golden brown and internal temp reaches 165°F")
                instructions.append("5. Remove meat to a plate and set aside")
                instructions.append("6. Add aromatics (garlic, onions) to same pan, cook 1-2 minutes until fragrant")
                instructions.append("7. Add vegetables in order of cooking time - hardest vegetables first")
                instructions.append("8. Stir-fry vegetables for 3-5 minutes until crisp-tender")
                instructions.append("9. Return meat to pan, toss everything together for 1-2 minutes until heated through")
            else:
                instructions.append("3. Add aromatics (garlic, onions) first, cook 1-2 minutes until fragrant")
                instructions.append("4. Add vegetables in order of cooking time - hardest first")
                instructions.append("5. Stir-fry for 5-7 minutes until vegetables are crisp-tender")

        elif cooking_method in ['roast', 'bake']:
            instructions.append("1. Preheat oven to 425°F (220°C)")
            instructions.append("2. Line a large baking sheet with parchment paper")
            instructions.append("3. Toss ingredients with oil, salt, and pepper in a large bowl")
            instructions.append("4. Arrange in single layer on prepared baking sheet - don't overcrowd")
            if any(meat in ingredient_text for meat in ['chicken', 'beef', 'pork']):
                instructions.append("5. Roast for 20-25 minutes until meat reaches internal temperature of 165°F")
            else:
                instructions.append("5. Roast for 15-20 minutes until vegetables are golden and tender")
            instructions.append("6. Flip ingredients halfway through cooking for even browning")

        elif cooking_method in ['simmer', 'braise']:
            instructions.append("1. Heat oil in a heavy-bottomed pot over medium heat")
            instructions.append("2. Add aromatics and cook until fragrant, about 2 minutes")
            if any(meat in ingredient_text for meat in ['chicken', 'beef', 'pork']):
                instructions.append("3. Add meat and brown on all sides, about 8-10 minutes total")
                instructions.append("4. Add liquid (broth/water) to cover meat by 1 inch")
                instructions.append("5. Bring to a gentle simmer, reduce heat to low")
                instructions.append("6. Cover and simmer for 45-60 minutes until meat is tender")
            else:
                instructions.append("3. Add vegetables and liquid, bring to a simmer")
                instructions.append("4. Reduce heat to low, cover and simmer 20-25 minutes")
            instructions.append("7. Stir occasionally and add more liquid if needed")

        # Cuisine-specific finishing touches
        if cuisine == 'Italian':
            instructions.append("\nITALIAN FINISHING TOUCHES:")
            instructions.append("• Drizzle with high-quality extra virgin olive oil")
            instructions.append("• Add fresh herbs (basil, parsley) in the last minute of cooking")
            instructions.append("• Finish with freshly grated Parmesan if desired")

        elif cuisine == 'Chinese' or cuisine == 'Asian':
            instructions.append("\nASIAN FINISHING TOUCHES:")
            instructions.append("• Add soy sauce and any final seasonings in last 30 seconds")
            instructions.append("• Garnish with sliced green onions or sesame seeds")
            instructions.append("• Serve immediately over steamed rice")

        elif cuisine == 'Mexican':
            instructions.append("\nMEXICAN FINISHING TOUCHES:")
            instructions.append("• Squeeze fresh lime juice over the dish")
            instructions.append("• Garnish with fresh cilantro and diced avocado")
            instructions.append("• Serve with warm tortillas or over rice")

        elif cuisine == 'Indian':
            instructions.append("\nINDIAN FINISHING TOUCHES:")
            instructions.append("• Adjust spice levels to taste in final minutes")
            instructions.append("• Garnish with fresh cilantro and a dollop of yogurt")
            instructions.append("• Serve with basmati rice or naan bread")

        # Final serving instructions
        instructions.append(f"\nSERVING:")
        instructions.append("• Taste and adjust seasoning with salt and pepper")
        instructions.append("• Let rest for 2-3 minutes to allow flavors to meld")
        instructions.append(f"• Serve immediately while hot")
        instructions.append(f"• This recipe serves {servings} people generously")

        return instructions
        
        return instructions
    
    def _calculate_prep_time(self, ingredients: List[str], cooking_method: str) -> str:
        """AI calculates prep time based on ingredients complexity"""
        base_time = 10  # Base 10 minutes
        
        # Add time based on ingredient complexity
        for ingredient in ingredients:
            if any(complex_ing in ingredient.lower() for complex_ing in ['meat', 'chicken', 'beef']):
                base_time += 5
            elif any(veg in ingredient.lower() for veg in ['onion', 'garlic', 'vegetable']):
                base_time += 3
        
        # Adjust for cooking method
        if cooking_method in ['roast', 'braise']:
            base_time += 5
        
        return f"{base_time} minutes"
    
    def _calculate_cook_time(self, ingredients: List[str], cooking_method: str) -> str:
        """AI calculates cooking time based on method and ingredients"""
        time_map = {
            'sauté': 15,
            'stir-fry': 10,
            'roast': 35,
            'braise': 45,
            'simmer': 25,
            'grill': 20
        }
        
        base_time = time_map.get(cooking_method, 20)
        
        # Adjust for protein content
        if any(meat in ' '.join(ingredients).lower() for meat in ['chicken', 'beef', 'pork']):
            base_time += 10
        
        return f"{base_time} minutes"
    
    def _assess_difficulty(self, ingredients: List[str], cooking_method: str) -> str:
        """AI assesses recipe difficulty"""
        complexity_score = 0
        
        # Score based on ingredients
        complexity_score += len(ingredients) * 0.5
        
        # Score based on cooking method
        method_difficulty = {
            'sauté': 1, 'stir-fry': 2, 'roast': 1,
            'braise': 3, 'simmer': 2, 'grill': 2
        }
        complexity_score += method_difficulty.get(cooking_method, 2)
        
        if complexity_score <= 3:
            return 'Easy'
        elif complexity_score <= 6:
            return 'Medium'
        else:
            return 'Hard'
    
    def _estimate_calories(self, ingredients: List[str], servings: int) -> int:
        """AI estimates calories based on ingredients"""
        total_calories = 0
        
        for ingredient in ingredients:
            ingredient_lower = ingredient.lower()
            
            # Calorie estimation per ingredient type
            if any(meat in ingredient_lower for meat in ['chicken', 'beef', 'pork']):
                total_calories += 200
            elif any(veg in ingredient_lower for veg in ['vegetable', 'onion', 'tomato']):
                total_calories += 25
            elif any(grain in ingredient_lower for grain in ['rice', 'pasta']):
                total_calories += 150
            elif any(fat in ingredient_lower for fat in ['oil', 'butter']):
                total_calories += 100
            else:
                total_calories += 50
        
        return int(total_calories / servings)
    
    def _determine_diet_type(self, ingredients: List[str]) -> str:
        """Determine diet type based on ingredients"""
        ingredient_text = ' '.join(ingredients).lower()
        
        if any(meat in ingredient_text for meat in ['chicken', 'beef', 'pork', 'fish', 'meat']):
            return 'Non-Vegetarian'
        elif any(dairy in ingredient_text for dairy in ['cheese', 'milk', 'butter', 'yogurt']):
            return 'Vegetarian'
        else:
            return 'Vegan'
    
    def _generate_ai_cooking_tips(self, ingredients: List[str], cooking_method: str, cuisine: str) -> List[str]:
        """Generate AI-powered cooking tips"""
        tips = []
        
        # Method-specific tips
        if cooking_method == 'sauté':
            tips.append("Don't overcrowd the pan - cook in batches if needed")
        elif cooking_method == 'roast':
            tips.append("Let ingredients come to room temperature before roasting")
        
        # Ingredient-specific tips
        if any('garlic' in ing.lower() for ing in ingredients):
            tips.append("Add garlic towards the end to prevent burning")
        
        if any('meat' in ing.lower() or 'chicken' in ing.lower() for ing in ingredients):
            tips.append("Use a meat thermometer for perfect doneness")
        
        # Cuisine-specific tips
        if cuisine == 'Italian':
            tips.append("Fresh herbs added at the end preserve their flavor")
        elif cuisine == 'Chinese':
            tips.append("Have all ingredients prepped before you start cooking")
        
        return tips
    
    def _analyze_flavor_profile(self, ingredients: List[str], cuisine: str) -> str:
        """Analyze the flavor profile of the dish"""
        ingredient_text = ' '.join(ingredients).lower()
        
        flavor_indicators = {
            'spicy': ['chili', 'pepper', 'hot', 'spice'],
            'savory': ['garlic', 'onion', 'meat', 'cheese'],
            'fresh': ['herb', 'basil', 'cilantro', 'lemon'],
            'rich': ['butter', 'cream', 'oil', 'cheese'],
            'aromatic': ['ginger', 'garlic', 'spice', 'herb']
        }
        
        detected_flavors = []
        for flavor, indicators in flavor_indicators.items():
            if any(indicator in ingredient_text for indicator in indicators):
                detected_flavors.append(flavor)
        
        if detected_flavors:
            return ', '.join(detected_flavors[:3])  # Top 3 flavors
        else:
            return 'balanced and flavorful'
