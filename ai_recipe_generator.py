"""
AI Recipe Generator for MealMind
Uses AI to generate custom recipes based on user ingredients
"""

import json
import random
import re
from typing import List, Dict, Any

class AIRecipeGenerator:
    def __init__(self):
        """Initialize the AI Recipe Generator"""
        self.cuisine_styles = {
            'Italian': {
                'cooking_methods': ['sauté', 'simmer', 'roast', 'grill'],
                'flavor_profiles': ['garlic', 'herbs', 'olive oil', 'tomato-based'],
                'techniques': ['al dente', 'soffritto', 'risotto method']
            },
            'Chinese': {
                'cooking_methods': ['stir-fry', 'steam', 'braise', 'deep-fry'],
                'flavor_profiles': ['ginger', 'garlic', 'soy-based', 'sweet and sour'],
                'techniques': ['wok hei', 'velvet coating', 'blanching']
            },
            'Indian': {
                'cooking_methods': ['curry', 'tandoor', 'steam', 'sauté'],
                'flavor_profiles': ['spicy', 'aromatic', 'curry-based', 'yogurt-based'],
                'techniques': ['tempering', 'dum cooking', 'marination']
            },
            'American': {
                'cooking_methods': ['grill', 'roast', 'fry', 'bake'],
                'flavor_profiles': ['hearty', 'comfort food', 'barbecue', 'cheese-based'],
                'techniques': ['caramelization', 'smoking', 'grilling']
            },
            'Mexican': {
                'cooking_methods': ['sauté', 'grill', 'simmer', 'roast'],
                'flavor_profiles': ['spicy', 'lime-based', 'chili-based', 'fresh herbs'],
                'techniques': ['charring', 'slow cooking', 'fresh preparation']
            }
        }
        
    def generate_ai_recipe(self, ingredients: List[str], servings: int = 2) -> Dict[str, Any]:
        """Generate a custom recipe using AI-like logic"""
        
        # Analyze ingredients to determine cuisine and cooking style
        cuisine = self._determine_optimal_cuisine(ingredients)
        cooking_style = self.cuisine_styles.get(cuisine, self.cuisine_styles['American'])
        
        # Generate recipe components
        recipe_name = self._generate_creative_recipe_name(ingredients, cuisine)
        cooking_method = self._select_optimal_cooking_method(ingredients, cooking_style)
        
        # Create detailed recipe structure
        recipe = {
            'id': f'ai_generated_{random.randint(10000, 99999)}',
            'name': recipe_name,
            'cuisine': cuisine,
            'servings': servings,
            'ingredients': self._format_ingredients_with_quantities(ingredients, servings),
            'instructions': self._generate_ai_instructions(ingredients, cooking_method, cuisine, servings),
            'prep_time': self._calculate_prep_time(ingredients, cooking_method),
            'cook_time': self._calculate_cook_time(ingredients, cooking_method),
            'difficulty': self._assess_difficulty(ingredients, cooking_method),
            'calories': self._estimate_calories(ingredients, servings),
            'diet_type': self._determine_diet_type(ingredients),
            'cooking_tips': self._generate_ai_cooking_tips(ingredients, cooking_method, cuisine),
            'flavor_profile': self._analyze_flavor_profile(ingredients, cuisine),
            'is_ai_generated': True
        }
        
        return recipe
    
    def _determine_optimal_cuisine(self, ingredients: List[str]) -> str:
        """AI logic to determine the best cuisine for given ingredients"""
        ingredient_text = ' '.join(ingredients).lower()
        
        # Cuisine scoring based on ingredient analysis
        cuisine_scores = {}
        
        for cuisine, style in self.cuisine_styles.items():
            score = 0
            
            # Score based on typical ingredients for each cuisine
            if cuisine == 'Italian':
                if any(ing in ingredient_text for ing in ['tomato', 'basil', 'cheese', 'pasta', 'olive']):
                    score += 3
                if any(ing in ingredient_text for ing in ['garlic', 'onion']):
                    score += 2
                    
            elif cuisine == 'Chinese':
                if any(ing in ingredient_text for ing in ['ginger', 'soy', 'rice', 'sesame']):
                    score += 3
                if any(ing in ingredient_text for ing in ['garlic', 'onion', 'vegetable']):
                    score += 2
                    
            elif cuisine == 'Indian':
                if any(ing in ingredient_text for ing in ['curry', 'turmeric', 'cumin', 'garam']):
                    score += 3
                if any(ing in ingredient_text for ing in ['onion', 'garlic', 'ginger', 'tomato']):
                    score += 2
                    
            elif cuisine == 'Mexican':
                if any(ing in ingredient_text for ing in ['lime', 'cilantro', 'chili', 'cumin']):
                    score += 3
                if any(ing in ingredient_text for ing in ['onion', 'tomato', 'pepper']):
                    score += 2
                    
            elif cuisine == 'American':
                if any(ing in ingredient_text for ing in ['cheese', 'beef', 'potato', 'bacon']):
                    score += 3
                if any(ing in ingredient_text for ing in ['onion', 'garlic']):
                    score += 1
            
            cuisine_scores[cuisine] = score
        
        # Return cuisine with highest score, default to fusion if tie
        best_cuisine = max(cuisine_scores, key=cuisine_scores.get)
        return best_cuisine if cuisine_scores[best_cuisine] > 0 else 'Fusion'
    
    def _generate_creative_recipe_name(self, ingredients: List[str], cuisine: str) -> str:
        """Generate creative, AI-inspired recipe names"""
        
        # Extract main ingredients
        main_ingredients = []
        for ing in ingredients[:3]:  # Focus on first 3 ingredients
            clean_ing = re.sub(r'\d+.*?(cup|tbsp|tsp|gram|oz|lb)s?\s*', '', ing).strip()
            main_ingredients.append(clean_ing.title())
        
        # Cuisine-specific naming patterns
        naming_patterns = {
            'Italian': [
                f"{' '.join(main_ingredients)} Rustico",
                f"Tuscan {main_ingredients[0]} Medley",
                f"{main_ingredients[0]} alla {main_ingredients[1] if len(main_ingredients) > 1 else 'Casa'}",
                f"Nonna's {' '.join(main_ingredients)} Special"
            ],
            'Chinese': [
                f"{main_ingredients[0]} {main_ingredients[1] if len(main_ingredients) > 1 else 'Garden'} Stir-Fry",
                f"Szechuan {' '.join(main_ingredients)} Delight",
                f"Golden {main_ingredients[0]} Harmony",
                f"Dragon's {' '.join(main_ingredients)} Bowl"
            ],
            'Indian': [
                f"{main_ingredients[0]} Masala Supreme",
                f"Royal {' '.join(main_ingredients)} Curry",
                f"Mumbai {main_ingredients[0]} Express",
                f"Spiced {' '.join(main_ingredients)} Delight"
            ],
            'Mexican': [
                f"{main_ingredients[0]} Fiesta Bowl",
                f"Aztec {' '.join(main_ingredients)} Fusion",
                f"Cantina {main_ingredients[0]} Special",
                f"Salsa Verde {' '.join(main_ingredients)}"
            ],
            'American': [
                f"Hearty {' '.join(main_ingredients)} Skillet",
                f"Country {main_ingredients[0]} Comfort",
                f"All-American {' '.join(main_ingredients)}",
                f"Farmhouse {main_ingredients[0]} Feast"
            ]
        }
        
        # Fusion patterns for unrecognized cuisines
        fusion_patterns = [
            f"Gourmet {' '.join(main_ingredients)} Creation",
            f"Chef's {main_ingredients[0]} Masterpiece",
            f"Artisan {' '.join(main_ingredients)} Blend",
            f"Signature {main_ingredients[0]} Fusion"
        ]
        
        patterns = naming_patterns.get(cuisine, fusion_patterns)
        return random.choice(patterns)
    
    def _select_optimal_cooking_method(self, ingredients: List[str], cooking_style: Dict) -> str:
        """AI selects the best cooking method based on ingredients"""
        ingredient_text = ' '.join(ingredients).lower()
        
        # Analyze ingredients to determine best cooking method
        if any(meat in ingredient_text for meat in ['chicken', 'beef', 'pork', 'fish']):
            if 'tender' in ingredient_text or 'breast' in ingredient_text:
                return random.choice(['sauté', 'grill', 'pan-fry'])
            else:
                return random.choice(['braise', 'roast', 'slow-cook'])
        
        elif any(veg in ingredient_text for veg in ['vegetable', 'pepper', 'onion', 'mushroom']):
            return random.choice(['stir-fry', 'sauté', 'roast'])
        
        elif any(grain in ingredient_text for grain in ['rice', 'pasta', 'noodle']):
            return random.choice(['boil', 'steam', 'simmer'])
        
        else:
            return random.choice(cooking_style['cooking_methods'])
    
    def _format_ingredients_with_quantities(self, ingredients: List[str], servings: int) -> List[str]:
        """Format ingredients with proper quantities for servings"""
        formatted_ingredients = []
        
        for ingredient in ingredients:
            # If ingredient already has quantity, scale it
            if re.search(r'\d+', ingredient):
                formatted_ingredients.append(self._scale_ingredient_quantity(ingredient, servings))
            else:
                # Add appropriate quantity based on ingredient type
                formatted_ingredients.append(self._add_smart_quantity(ingredient, servings))
        
        return formatted_ingredients
    
    def _scale_ingredient_quantity(self, ingredient: str, servings: int) -> str:
        """Scale ingredient quantity based on servings"""
        # Extract number from ingredient
        numbers = re.findall(r'\d+(?:\.\d+)?', ingredient)
        if numbers:
            original_qty = float(numbers[0])
            scaled_qty = original_qty * (servings / 2)  # Base is 2 servings
            
            # Replace the number in the ingredient string
            scaled_ingredient = re.sub(r'\d+(?:\.\d+)?', str(int(scaled_qty) if scaled_qty.is_integer() else f"{scaled_qty:.1f}"), ingredient, count=1)
            return scaled_ingredient
        
        return ingredient
    
    def _add_smart_quantity(self, ingredient: str, servings: int) -> str:
        """Add smart quantities to ingredients without quantities"""
        ingredient_lower = ingredient.lower()
        
        # Quantity mapping based on ingredient type
        if any(meat in ingredient_lower for meat in ['chicken', 'beef', 'pork', 'fish']):
            base_qty = 6 * servings  # 6 oz per serving
            return f"{base_qty} oz {ingredient}"
        
        elif any(veg in ingredient_lower for veg in ['onion', 'tomato', 'pepper']):
            qty = 1 * servings if servings <= 2 else servings // 2 + 1
            return f"{qty} medium {ingredient}"
        
        elif any(herb in ingredient_lower for herb in ['garlic', 'ginger']):
            qty = 2 * servings
            return f"{qty} cloves {ingredient}" if 'garlic' in ingredient_lower else f"{qty} tsp fresh {ingredient}"
        
        elif any(spice in ingredient_lower for spice in ['salt', 'pepper', 'cumin', 'paprika']):
            return f"1 tsp {ingredient}"
        
        elif any(liquid in ingredient_lower for liquid in ['oil', 'sauce', 'broth']):
            qty = 2 * servings
            return f"{qty} tbsp {ingredient}"
        
        else:
            return f"1 cup {ingredient}"
    
    def _generate_ai_instructions(self, ingredients: List[str], cooking_method: str, cuisine: str, servings: int) -> List[str]:
        """Generate detailed AI-powered cooking instructions"""
        instructions = []
        
        # Preparation phase
        instructions.append("🔪 PREPARATION:")
        instructions.append("• Gather all ingredients and read through the entire recipe before starting")
        instructions.append("• Prepare your workspace with all necessary tools and equipment")
        
        # Ingredient prep based on AI analysis
        if any('onion' in ing.lower() for ing in ingredients):
            instructions.append("• Dice onions finely for even cooking and better flavor distribution")
        
        if any('garlic' in ing.lower() for ing in ingredients):
            instructions.append("• Mince garlic fresh - avoid pre-minced for best flavor")
        
        if any(meat in ' '.join(ingredients).lower() for meat in ['chicken', 'beef', 'pork']):
            instructions.append("• Pat meat dry and season 15 minutes before cooking for better flavor")
        
        # Cooking phase based on method
        instructions.append(f"\n🔥 COOKING ({cooking_method.upper()}):")
        
        if cooking_method in ['sauté', 'stir-fry']:
            instructions.append("• Heat pan over medium-high heat until hot but not smoking")
            instructions.append("• Add oil and swirl to coat the pan evenly")
            instructions.append("• Cook ingredients in order of cooking time - longest first")
            instructions.append("• Keep ingredients moving for even cooking")
        
        elif cooking_method in ['roast', 'bake']:
            instructions.append(f"• Preheat oven to 400°F (200°C)")
            instructions.append("• Arrange ingredients in a single layer for even cooking")
            instructions.append("• Roast until golden brown and cooked through")
        
        elif cooking_method in ['simmer', 'braise']:
            instructions.append("• Bring liquid to a gentle simmer, not a rolling boil")
            instructions.append("• Cover and cook low and slow for tender results")
            instructions.append("• Stir occasionally to prevent sticking")
        
        # Cuisine-specific techniques
        if cuisine == 'Italian':
            instructions.append("• Finish with a drizzle of good quality olive oil")
            instructions.append("• Taste and adjust seasoning - Italian cooking is about balance")
        
        elif cuisine == 'Chinese':
            instructions.append("• Cook over high heat for authentic 'wok hei' flavor")
            instructions.append("• Add aromatics (ginger, garlic) first to infuse oil")
        
        elif cuisine == 'Indian':
            instructions.append("• Toast spices briefly to release their essential oils")
            instructions.append("• Build flavors in layers - spices, aromatics, then main ingredients")
        
        # Final steps
        instructions.append("\n✨ FINISHING:")
        instructions.append("• Taste and adjust seasoning as needed")
        instructions.append("• Let rest for 2-3 minutes before serving")
        instructions.append(f"• Serve immediately while hot (serves {servings})")
        
        return instructions
    
    def _calculate_prep_time(self, ingredients: List[str], cooking_method: str) -> str:
        """AI calculates prep time based on ingredients complexity"""
        base_time = 10  # Base 10 minutes
        
        # Add time based on ingredient complexity
        for ingredient in ingredients:
            if any(complex_ing in ingredient.lower() for complex_ing in ['meat', 'chicken', 'beef']):
                base_time += 5
            elif any(veg in ingredient.lower() for veg in ['onion', 'garlic', 'vegetable']):
                base_time += 3
        
        # Adjust for cooking method
        if cooking_method in ['roast', 'braise']:
            base_time += 5
        
        return f"{base_time} minutes"
    
    def _calculate_cook_time(self, ingredients: List[str], cooking_method: str) -> str:
        """AI calculates cooking time based on method and ingredients"""
        time_map = {
            'sauté': 15,
            'stir-fry': 10,
            'roast': 35,
            'braise': 45,
            'simmer': 25,
            'grill': 20
        }
        
        base_time = time_map.get(cooking_method, 20)
        
        # Adjust for protein content
        if any(meat in ' '.join(ingredients).lower() for meat in ['chicken', 'beef', 'pork']):
            base_time += 10
        
        return f"{base_time} minutes"
    
    def _assess_difficulty(self, ingredients: List[str], cooking_method: str) -> str:
        """AI assesses recipe difficulty"""
        complexity_score = 0
        
        # Score based on ingredients
        complexity_score += len(ingredients) * 0.5
        
        # Score based on cooking method
        method_difficulty = {
            'sauté': 1, 'stir-fry': 2, 'roast': 1,
            'braise': 3, 'simmer': 2, 'grill': 2
        }
        complexity_score += method_difficulty.get(cooking_method, 2)
        
        if complexity_score <= 3:
            return 'Easy'
        elif complexity_score <= 6:
            return 'Medium'
        else:
            return 'Hard'
    
    def _estimate_calories(self, ingredients: List[str], servings: int) -> int:
        """AI estimates calories based on ingredients"""
        total_calories = 0
        
        for ingredient in ingredients:
            ingredient_lower = ingredient.lower()
            
            # Calorie estimation per ingredient type
            if any(meat in ingredient_lower for meat in ['chicken', 'beef', 'pork']):
                total_calories += 200
            elif any(veg in ingredient_lower for veg in ['vegetable', 'onion', 'tomato']):
                total_calories += 25
            elif any(grain in ingredient_lower for grain in ['rice', 'pasta']):
                total_calories += 150
            elif any(fat in ingredient_lower for fat in ['oil', 'butter']):
                total_calories += 100
            else:
                total_calories += 50
        
        return int(total_calories / servings)
    
    def _determine_diet_type(self, ingredients: List[str]) -> str:
        """Determine diet type based on ingredients"""
        ingredient_text = ' '.join(ingredients).lower()
        
        if any(meat in ingredient_text for meat in ['chicken', 'beef', 'pork', 'fish', 'meat']):
            return 'Non-Vegetarian'
        elif any(dairy in ingredient_text for dairy in ['cheese', 'milk', 'butter', 'yogurt']):
            return 'Vegetarian'
        else:
            return 'Vegan'
    
    def _generate_ai_cooking_tips(self, ingredients: List[str], cooking_method: str, cuisine: str) -> List[str]:
        """Generate AI-powered cooking tips"""
        tips = []
        
        # Method-specific tips
        if cooking_method == 'sauté':
            tips.append("Don't overcrowd the pan - cook in batches if needed")
        elif cooking_method == 'roast':
            tips.append("Let ingredients come to room temperature before roasting")
        
        # Ingredient-specific tips
        if any('garlic' in ing.lower() for ing in ingredients):
            tips.append("Add garlic towards the end to prevent burning")
        
        if any('meat' in ing.lower() or 'chicken' in ing.lower() for ing in ingredients):
            tips.append("Use a meat thermometer for perfect doneness")
        
        # Cuisine-specific tips
        if cuisine == 'Italian':
            tips.append("Fresh herbs added at the end preserve their flavor")
        elif cuisine == 'Chinese':
            tips.append("Have all ingredients prepped before you start cooking")
        
        return tips
    
    def _analyze_flavor_profile(self, ingredients: List[str], cuisine: str) -> str:
        """Analyze the flavor profile of the dish"""
        ingredient_text = ' '.join(ingredients).lower()
        
        flavor_indicators = {
            'spicy': ['chili', 'pepper', 'hot', 'spice'],
            'savory': ['garlic', 'onion', 'meat', 'cheese'],
            'fresh': ['herb', 'basil', 'cilantro', 'lemon'],
            'rich': ['butter', 'cream', 'oil', 'cheese'],
            'aromatic': ['ginger', 'garlic', 'spice', 'herb']
        }
        
        detected_flavors = []
        for flavor, indicators in flavor_indicators.items():
            if any(indicator in ingredient_text for indicator in indicators):
                detected_flavors.append(flavor)
        
        if detected_flavors:
            return ', '.join(detected_flavors[:3])  # Top 3 flavors
        else:
            return 'balanced and flavorful'
