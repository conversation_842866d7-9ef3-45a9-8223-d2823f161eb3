"""
AI Recipe Generator for MealMind
Uses AI to generate custom recipes based on user ingredients
"""

import json
import random
import re
from typing import List, Dict, Any

class AIRecipeGenerator:
    def __init__(self):
        """Initialize the AI Recipe Generator"""
        self.cuisine_styles = {
            'Italian': {
                'cooking_methods': ['sauté', 'simmer', 'roast', 'grill'],
                'flavor_profiles': ['garlic', 'herbs', 'olive oil', 'tomato-based'],
                'techniques': ['al dente', 'soffritto', 'risotto method']
            },
            'Chinese': {
                'cooking_methods': ['stir-fry', 'steam', 'braise', 'deep-fry'],
                'flavor_profiles': ['ginger', 'garlic', 'soy-based', 'sweet and sour'],
                'techniques': ['wok hei', 'velvet coating', 'blanching']
            },
            'Indian': {
                'cooking_methods': ['curry', 'tandoor', 'steam', 'sauté'],
                'flavor_profiles': ['spicy', 'aromatic', 'curry-based', 'yogurt-based'],
                'techniques': ['tempering', 'dum cooking', 'marination']
            },
            'American': {
                'cooking_methods': ['grill', 'roast', 'fry', 'bake'],
                'flavor_profiles': ['hearty', 'comfort food', 'barbecue', 'cheese-based'],
                'techniques': ['caramelization', 'smoking', 'grilling']
            },
            'Mexican': {
                'cooking_methods': ['sauté', 'grill', 'simmer', 'roast'],
                'flavor_profiles': ['spicy', 'lime-based', 'chili-based', 'fresh herbs'],
                'techniques': ['charring', 'slow cooking', 'fresh preparation']
            }
        }
        
    def generate_ai_recipe(self, ingredients: List[str], servings: int = 2) -> Dict[str, Any]:
        """Generate a custom recipe using AI-like logic"""
        
        # Analyze ingredients to determine cuisine and cooking style
        cuisine = self._determine_optimal_cuisine(ingredients)
        cooking_style = self.cuisine_styles.get(cuisine, self.cuisine_styles['American'])
        
        # Generate recipe components
        recipe_name = self._generate_creative_recipe_name(ingredients, cuisine)
        cooking_method = self._select_optimal_cooking_method(ingredients, cooking_style)
        
        # Create detailed recipe structure
        recipe = {
            'id': f'ai_generated_{random.randint(10000, 99999)}',
            'name': recipe_name,
            'cuisine': cuisine,
            'servings': servings,
            'ingredients': self._format_ingredients_with_quantities(ingredients, servings),
            'instructions': self._generate_ai_instructions(ingredients, cooking_method, cuisine, servings),
            'prep_time': self._calculate_prep_time(ingredients, cooking_method),
            'cook_time': self._calculate_cook_time(ingredients, cooking_method),
            'difficulty': self._assess_difficulty(ingredients, cooking_method),
            'calories': self._estimate_calories(ingredients, servings),
            'diet_type': self._determine_diet_type(ingredients),
            'cooking_tips': self._generate_ai_cooking_tips(ingredients, cooking_method, cuisine),
            'flavor_profile': self._analyze_flavor_profile(ingredients, cuisine),
            'is_ai_generated': True
        }
        
        return recipe
    
    def _determine_optimal_cuisine(self, ingredients: List[str]) -> str:
        """AI logic to determine the best cuisine for given ingredients"""
        ingredient_text = ' '.join(ingredients).lower()
        
        # Cuisine scoring based on ingredient analysis
        cuisine_scores = {}
        
        for cuisine, style in self.cuisine_styles.items():
            score = 0
            
            # Score based on typical ingredients for each cuisine
            if cuisine == 'Italian':
                if any(ing in ingredient_text for ing in ['tomato', 'basil', 'cheese', 'pasta', 'olive']):
                    score += 3
                if any(ing in ingredient_text for ing in ['garlic', 'onion']):
                    score += 2
                    
            elif cuisine == 'Chinese':
                if any(ing in ingredient_text for ing in ['ginger', 'soy', 'rice', 'sesame']):
                    score += 3
                if any(ing in ingredient_text for ing in ['garlic', 'onion', 'vegetable']):
                    score += 2
                    
            elif cuisine == 'Indian':
                if any(ing in ingredient_text for ing in ['curry', 'turmeric', 'cumin', 'garam']):
                    score += 3
                if any(ing in ingredient_text for ing in ['onion', 'garlic', 'ginger', 'tomato']):
                    score += 2
                    
            elif cuisine == 'Mexican':
                if any(ing in ingredient_text for ing in ['lime', 'cilantro', 'chili', 'cumin']):
                    score += 3
                if any(ing in ingredient_text for ing in ['onion', 'tomato', 'pepper']):
                    score += 2
                    
            elif cuisine == 'American':
                if any(ing in ingredient_text for ing in ['cheese', 'beef', 'potato', 'bacon']):
                    score += 3
                if any(ing in ingredient_text for ing in ['onion', 'garlic']):
                    score += 1
            
            cuisine_scores[cuisine] = score
        
        # Return cuisine with highest score, default to fusion if tie
        best_cuisine = max(cuisine_scores, key=cuisine_scores.get)
        return best_cuisine if cuisine_scores[best_cuisine] > 0 else 'Fusion'
    
    def _generate_creative_recipe_name(self, ingredients: List[str], cuisine: str) -> str:
        """Generate realistic recipe names based on actual dishes"""
        ingredient_text = ' '.join(ingredients).lower()

        # Detect main protein
        main_protein = None
        if 'chicken' in ingredient_text:
            main_protein = 'chicken'
        elif 'beef' in ingredient_text:
            main_protein = 'beef'
        elif 'pork' in ingredient_text:
            main_protein = 'pork'
        elif 'fish' in ingredient_text or 'salmon' in ingredient_text:
            main_protein = 'fish'
        elif 'shrimp' in ingredient_text:
            main_protein = 'shrimp'
        elif 'turkey' in ingredient_text:
            main_protein = 'turkey'
        elif 'egg' in ingredient_text:
            main_protein = 'egg'

        # Detect key vegetables and ingredients
        has_tomato = 'tomato' in ingredient_text
        has_onion = 'onion' in ingredient_text
        has_pepper = 'pepper' in ingredient_text
        has_mushroom = 'mushroom' in ingredient_text
        has_broccoli = 'broccoli' in ingredient_text
        has_spinach = 'spinach' in ingredient_text
        has_garlic = 'garlic' in ingredient_text
        has_basil = 'basil' in ingredient_text
        has_rice = 'rice' in ingredient_text
        has_pasta = 'pasta' in ingredient_text

        # Generate realistic names based on cuisine and ingredients
        if cuisine == 'Italian':
            if main_protein == 'chicken':
                if has_tomato and has_basil:
                    return "Chicken Margherita"
                elif has_mushroom:
                    return "Chicken Marsala"
                elif has_pepper:
                    return "Chicken Cacciatore"
                elif has_spinach:
                    return "Chicken Florentine"
                else:
                    return "Italian Herb Chicken"
            elif main_protein == 'beef':
                if has_tomato:
                    return "Beef Marinara"
                else:
                    return "Italian Beef Stew"
            elif has_pasta:
                if has_tomato and has_basil:
                    return "Pasta Marinara"
                elif has_mushroom:
                    return "Mushroom Pasta"
                else:
                    return "Pasta Primavera"
            else:
                return "Italian Vegetable Medley"

        elif cuisine == 'Chinese' or cuisine == 'Asian':
            if main_protein == 'chicken':
                if has_broccoli:
                    return "Chicken and Broccoli"
                elif has_pepper:
                    return "Chicken Bell Pepper Stir Fry"
                elif has_mushroom:
                    return "Chicken Mushroom Stir Fry"
                else:
                    return "General Tso's Chicken"
            elif main_protein == 'beef':
                if has_broccoli:
                    return "Beef and Broccoli"
                elif has_pepper:
                    return "Pepper Steak"
                else:
                    return "Mongolian Beef"
            elif main_protein == 'shrimp':
                if has_rice:
                    return "Shrimp Fried Rice"
                else:
                    return "Honey Garlic Shrimp"
            elif has_rice:
                return "Vegetable Fried Rice"
            else:
                return "Mixed Vegetable Stir Fry"

        elif cuisine == 'Mexican':
            if main_protein == 'chicken':
                if has_pepper and has_onion:
                    return "Chicken Fajitas"
                else:
                    return "Mexican Chicken Bowl"
            elif main_protein == 'beef':
                if has_pepper and has_onion:
                    return "Beef Fajitas"
                else:
                    return "Mexican Beef Skillet"
            else:
                return "Vegetarian Burrito Bowl"

        elif cuisine == 'Indian':
            if main_protein == 'chicken':
                if has_tomato:
                    return "Chicken Tikka Masala"
                else:
                    return "Chicken Curry"
            elif main_protein == 'beef':
                return "Beef Curry"
            else:
                return "Vegetable Curry"

        else:  # American/General
            if main_protein == 'chicken':
                if has_mushroom:
                    return "Chicken and Mushroom Skillet"
                elif has_broccoli:
                    return "Chicken and Broccoli Casserole"
                elif has_pepper and has_onion:
                    return "Chicken Fajita Skillet"
                else:
                    return "Pan-Seared Chicken"
            elif main_protein == 'beef':
                if has_mushroom:
                    return "Beef Stroganoff"
                elif has_pepper and has_onion:
                    return "Pepper Steak"
                else:
                    return "Beef and Vegetable Skillet"
            elif main_protein == 'egg':
                if has_pepper or has_onion or has_mushroom:
                    return "Vegetable Omelet"
                else:
                    return "Scrambled Eggs"
            else:
                return "Garden Vegetable Medley"

        # Fallback
        return f"{cuisine} Style Mixed Dish"
    
    def _select_optimal_cooking_method(self, ingredients: List[str], cooking_style: Dict) -> str:
        """AI selects the best cooking method based on ingredients"""
        ingredient_text = ' '.join(ingredients).lower()
        
        # Analyze ingredients to determine best cooking method
        if any(meat in ingredient_text for meat in ['chicken', 'beef', 'pork', 'fish']):
            if 'tender' in ingredient_text or 'breast' in ingredient_text:
                return random.choice(['sauté', 'grill', 'pan-fry'])
            else:
                return random.choice(['braise', 'roast', 'slow-cook'])
        
        elif any(veg in ingredient_text for veg in ['vegetable', 'pepper', 'onion', 'mushroom']):
            return random.choice(['stir-fry', 'sauté', 'roast'])
        
        elif any(grain in ingredient_text for grain in ['rice', 'pasta', 'noodle']):
            return random.choice(['boil', 'steam', 'simmer'])
        
        else:
            return random.choice(cooking_style['cooking_methods'])
    
    def _format_ingredients_with_quantities(self, ingredients: List[str], servings: int) -> List[str]:
        """Format ingredients with proper quantities for servings"""
        formatted_ingredients = []
        
        for ingredient in ingredients:
            # If ingredient already has quantity, scale it
            if re.search(r'\d+', ingredient):
                formatted_ingredients.append(self._scale_ingredient_quantity(ingredient, servings))
            else:
                # Add appropriate quantity based on ingredient type
                formatted_ingredients.append(self._add_smart_quantity(ingredient, servings))
        
        return formatted_ingredients
    
    def _scale_ingredient_quantity(self, ingredient: str, servings: int) -> str:
        """Scale ingredient quantity based on servings"""
        # Extract number from ingredient
        numbers = re.findall(r'\d+(?:\.\d+)?', ingredient)
        if numbers:
            original_qty = float(numbers[0])
            scaled_qty = original_qty * (servings / 2)  # Base is 2 servings
            
            # Replace the number in the ingredient string
            scaled_ingredient = re.sub(r'\d+(?:\.\d+)?', str(int(scaled_qty) if scaled_qty.is_integer() else f"{scaled_qty:.1f}"), ingredient, count=1)
            return scaled_ingredient
        
        return ingredient
    
    def _add_smart_quantity(self, ingredient: str, servings: int) -> str:
        """Add precise, realistic quantities to ingredients with expanded database"""
        ingredient_lower = ingredient.lower().strip()

        # Proteins - precise amounts
        if 'chicken breast' in ingredient_lower:
            qty = servings * 6  # 6 oz per serving
            return f"{qty} oz boneless, skinless chicken breast"
        elif 'ground beef' in ingredient_lower:
            qty = servings * 0.25  # 1/4 lb per serving
            return f"{qty:.1f} lbs ground beef (80/20)"
        elif 'chicken' in ingredient_lower:
            qty = servings * 6
            return f"{qty} oz chicken"
        elif 'beef' in ingredient_lower:
            qty = servings * 0.25
            return f"{qty:.1f} lbs beef"
        elif 'fish' in ingredient_lower or 'salmon' in ingredient_lower:
            qty = servings * 5
            return f"{qty} oz fresh fish fillet"
        elif 'shrimp' in ingredient_lower or 'prawns' in ingredient_lower:
            qty = servings * 0.25
            return f"{qty:.1f} lbs large shrimp, peeled and deveined"
        elif 'turkey' in ingredient_lower:
            qty = servings * 6
            return f"{qty} oz turkey breast"
        elif 'paneer' in ingredient_lower:
            qty = servings * 4
            return f"{qty} oz paneer (Indian cottage cheese), cubed"
        elif 'tofu' in ingredient_lower:
            qty = servings * 4
            return f"{qty} oz firm tofu, cubed"
        elif 'mutton' in ingredient_lower or 'lamb' in ingredient_lower:
            qty = servings * 0.25
            return f"{qty:.1f} lbs mutton/lamb, cut into pieces"

        # Vegetables - realistic portions with expanded database
        elif 'onion' in ingredient_lower:
            qty = "1 large" if servings <= 3 else "2 medium"
            return f"{qty} yellow onion, finely diced"
        elif 'tomato' in ingredient_lower:
            qty = max(2, servings)
            return f"{qty} medium tomatoes, chopped"
        elif 'capsicum' in ingredient_lower or 'bell pepper' in ingredient_lower or ('pepper' in ingredient_lower and 'black' not in ingredient_lower and 'chili' not in ingredient_lower):
            qty = "1 large" if servings <= 3 else "2 medium"
            return f"{qty} capsicum/bell pepper, sliced into strips"
        elif 'mushroom' in ingredient_lower:
            qty = servings * 4
            return f"{qty} oz button mushrooms, sliced"
        elif 'broccoli' in ingredient_lower:
            qty = "1 large head" if servings <= 3 else "2 medium heads"
            return f"{qty} broccoli, cut into bite-sized florets"
        elif 'spinach' in ingredient_lower:
            qty = servings * 2
            return f"{qty} cups fresh spinach leaves, washed and chopped"
        elif 'carrot' in ingredient_lower:
            qty = servings
            return f"{qty} medium carrots, peeled and sliced"
        elif 'potato' in ingredient_lower:
            qty = servings
            return f"{qty} medium potatoes, peeled and cubed"
        elif 'cauliflower' in ingredient_lower:
            qty = "1 medium head" if servings <= 3 else "1 large head"
            return f"{qty} cauliflower, cut into florets"
        elif 'eggplant' in ingredient_lower or 'brinjal' in ingredient_lower:
            qty = "1 medium" if servings <= 3 else "2 small"
            return f"{qty} eggplant/brinjal, cubed"
        elif 'okra' in ingredient_lower or 'bhindi' in ingredient_lower:
            qty = servings * 4
            return f"{qty} oz fresh okra/bhindi, trimmed and sliced"
        elif 'green beans' in ingredient_lower:
            qty = servings * 4
            return f"{qty} oz fresh green beans, trimmed"
        elif 'peas' in ingredient_lower:
            qty = servings * 0.5
            return f"{qty:.1f} cups green peas (fresh or frozen)"
        elif 'corn' in ingredient_lower:
            qty = servings * 0.5
            return f"{qty:.1f} cups corn kernels"

        # Aromatics
        elif 'garlic' in ingredient_lower:
            qty = max(2, int(servings * 1.5))
            return f"{qty} cloves garlic, minced"
        elif 'ginger' in ingredient_lower:
            return "1 inch piece fresh ginger, minced"

        # Grains and starches
        elif 'rice' in ingredient_lower:
            qty = servings * 0.5
            return f"{qty:.1f} cups long-grain white rice"
        elif 'pasta' in ingredient_lower:
            qty = servings * 3
            return f"{qty} oz pasta"
        elif 'quinoa' in ingredient_lower:
            qty = servings * 0.375
            return f"{qty:.1f} cups quinoa"

        # Dairy
        elif 'cheese' in ingredient_lower:
            if 'parmesan' in ingredient_lower:
                qty = servings * 2
                return f"{qty} tbsp grated Parmesan cheese"
            else:
                qty = servings * 2
                return f"{qty} oz cheese, shredded"
        elif 'milk' in ingredient_lower:
            qty = servings * 0.25
            return f"{qty:.1f} cups whole milk"
        elif 'butter' in ingredient_lower:
            qty = servings * 2
            return f"{qty} tbsp unsalted butter"

        # Oils and fats
        elif 'olive oil' in ingredient_lower:
            qty = servings * 2
            return f"{qty} tbsp extra virgin olive oil"
        elif 'oil' in ingredient_lower:
            qty = servings * 2
            return f"{qty} tbsp cooking oil"

        # Herbs and spices - expanded Indian and international
        elif 'basil' in ingredient_lower:
            qty = servings * 2
            return f"{qty} tbsp fresh basil leaves, chopped"
        elif 'oregano' in ingredient_lower:
            qty = max(1, servings)
            return f"{qty} tsp dried oregano"
        elif 'thyme' in ingredient_lower:
            qty = max(1, servings)
            return f"{qty} tsp fresh thyme leaves"
        elif 'salt' in ingredient_lower:
            return "Salt to taste"
        elif 'pepper' in ingredient_lower and 'bell' not in ingredient_lower and 'capsicum' not in ingredient_lower:
            return "Black pepper powder to taste"
        elif 'paprika' in ingredient_lower:
            qty = max(1, int(servings * 0.5))
            return f"{qty} tsp paprika powder"
        elif 'cumin' in ingredient_lower or 'jeera' in ingredient_lower:
            qty = max(1, int(servings * 0.5))
            return f"{qty} tsp cumin seeds (jeera)"
        elif 'coriander' in ingredient_lower and 'leaves' not in ingredient_lower:
            qty = max(1, int(servings * 0.5))
            return f"{qty} tsp coriander seeds (dhania)"
        elif 'turmeric' in ingredient_lower or 'haldi' in ingredient_lower:
            qty = max(1, int(servings * 0.25))
            return f"{qty} tsp turmeric powder (haldi)"
        elif 'garam masala' in ingredient_lower:
            qty = max(1, int(servings * 0.5))
            return f"{qty} tsp garam masala powder"
        elif 'red chili' in ingredient_lower or 'chili powder' in ingredient_lower:
            return "Red chili powder to taste"
        elif 'cinnamon' in ingredient_lower:
            return "1 inch cinnamon stick"
        elif 'cardamom' in ingredient_lower:
            qty = max(2, servings)
            return f"{qty} green cardamom pods"
        elif 'bay leaf' in ingredient_lower or 'bay leaves' in ingredient_lower:
            return "2 bay leaves"
        elif 'mustard seeds' in ingredient_lower:
            return "1 tsp mustard seeds"
        elif 'curry leaves' in ingredient_lower:
            return "8-10 fresh curry leaves"
        elif 'cilantro' in ingredient_lower or 'coriander leaves' in ingredient_lower:
            qty = servings * 2
            return f"{qty} tbsp fresh cilantro/coriander leaves, chopped"

        # Eggs
        elif 'egg' in ingredient_lower:
            qty = max(2, servings)
            return f"{qty} large eggs"

        # Condiments and sauces
        elif 'soy sauce' in ingredient_lower:
            qty = servings * 2
            return f"{qty} tbsp low-sodium soy sauce"
        elif 'vinegar' in ingredient_lower:
            qty = max(1, servings)
            return f"{qty} tbsp vinegar"
        elif 'broth' in ingredient_lower or 'stock' in ingredient_lower:
            qty = servings * 0.5
            return f"{qty:.1f} cups chicken broth"

        # Default for unrecognized ingredients
        else:
            qty = servings * 0.5
            return f"{qty:.1f} cups {ingredient}"
    
    def _generate_ai_instructions(self, ingredients: List[str], cooking_method: str, cuisine: str, servings: int) -> List[str]:
        """Generate detailed chef-level instructions for beginners with every step explained"""
        instructions = []
        ingredient_text = ' '.join(ingredients).lower()

        # MISE EN PLACE - Professional preparation
        instructions.append("🔪 MISE EN PLACE (Preparation - 15-20 minutes):")
        instructions.append("1. Read through the entire recipe twice before starting - this prevents mistakes")
        instructions.append("2. Wash your hands thoroughly and tie back long hair")
        instructions.append("3. Gather all ingredients on your counter - this is called 'mise en place'")
        instructions.append("4. Get out all required equipment: cutting board, sharp knife, measuring cups, cooking pan")
        instructions.append("5. Fill a small bowl with water to rinse your hands while cooking")

        # Detailed ingredient preparation
        instructions.append("\n📋 INGREDIENT PREPARATION:")

        if 'rice' in ingredient_text:
            instructions.append("• RICE PREPARATION:")
            instructions.append("  - Rinse rice in cold water 3-4 times until water runs clear")
            instructions.append("  - This removes excess starch and prevents sticky rice")
            instructions.append("  - Drain rice completely in a fine mesh strainer")

        if 'onion' in ingredient_text:
            instructions.append("• ONION PREPARATION:")
            instructions.append("  - Cut off both ends of the onion")
            instructions.append("  - Remove the papery outer skin")
            instructions.append("  - Cut onion in half from top to bottom")
            instructions.append("  - Place flat side down, make horizontal cuts (don't cut all the way through)")
            instructions.append("  - Make vertical cuts, then dice into ¼-inch pieces")
            instructions.append("  - Keep pieces uniform for even cooking")

        if 'garlic' in ingredient_text:
            instructions.append("• GARLIC PREPARATION:")
            instructions.append("  - Separate cloves from the bulb")
            instructions.append("  - Place flat side of knife on clove, press down to crush slightly")
            instructions.append("  - Remove papery skin easily")
            instructions.append("  - Mince finely with rocking motion - don't crush or it becomes bitter")

        if 'tomato' in ingredient_text:
            instructions.append("• TOMATO PREPARATION:")
            instructions.append("  - Wash tomatoes under cold running water")
            instructions.append("  - Remove the core (green part) with a small knife")
            instructions.append("  - Cut into ½-inch pieces for even cooking")
            instructions.append("  - Save any juices - they add flavor!")

        if 'capsicum' in ingredient_text or 'bell pepper' in ingredient_text or ('pepper' in ingredient_text and 'black' not in ingredient_text):
            instructions.append("• CAPSICUM/BELL PEPPER PREPARATION:")
            instructions.append("  - Wash and dry the capsicum")
            instructions.append("  - Cut off the top and bottom")
            instructions.append("  - Remove all seeds and white pith (they're bitter)")
            instructions.append("  - Cut into even strips or pieces as needed")

        if 'paneer' in ingredient_text:
            instructions.append("• PANEER PREPARATION:")
            instructions.append("  - Remove paneer from package and pat dry")
            instructions.append("  - Cut into 1-inch cubes for best texture")
            instructions.append("  - Keep cubes uniform so they cook evenly")

        if any(meat in ingredient_text for meat in ['chicken', 'beef', 'pork', 'fish', 'mutton', 'lamb']):
            instructions.append("• MEAT PREPARATION:")
            instructions.append("  - Remove meat from refrigerator 15-20 minutes before cooking")
            instructions.append("  - Pat completely dry with paper towels - moisture prevents browning")
            instructions.append("  - Season generously with salt and pepper on both sides")
            instructions.append("  - Let seasoning penetrate for 10-15 minutes")
            instructions.append("  - Cut against the grain for tender pieces")

        if 'mushroom' in ingredient_text:
            instructions.append("• MUSHROOM PREPARATION:")
            instructions.append("  - Never wash mushrooms - they absorb water like sponges")
            instructions.append("  - Clean with damp paper towel to remove dirt")
            instructions.append("  - Trim off any dark or slimy parts")
            instructions.append("  - Slice evenly so they cook at the same rate")

        # COOKING PHASE - Step by step like a chef teaching
        instructions.append(f"\n🔥 COOKING PROCESS ({cooking_method.upper()}):")

        # Rice cooking if present
        if 'rice' in ingredient_text:
            instructions.append("\n🍚 COOKING RICE FIRST (15-20 minutes):")
            instructions.append("1. In a heavy-bottomed pot, add 1 cup rice to 2 cups water")
            instructions.append("2. Add a pinch of salt and 1 tsp oil (prevents sticking)")
            instructions.append("3. Bring to a rolling boil over high heat")
            instructions.append("4. Once boiling, immediately reduce heat to lowest setting")
            instructions.append("5. Cover tightly with lid - no peeking! Steam does the work")
            instructions.append("6. Cook for exactly 18 minutes for white rice")
            instructions.append("7. Turn off heat, let sit covered for 5 minutes")
            instructions.append("8. Fluff with fork before serving")

        if cooking_method in ['sauté', 'stir-fry']:
            instructions.append("\n🥘 MAIN COOKING PROCESS:")
            instructions.append("1. Choose a large, heavy-bottomed pan or wok")
            instructions.append("2. Heat pan over medium-high heat for 2-3 minutes")
            instructions.append("3. Test heat: sprinkle water drops - they should sizzle and evaporate")
            instructions.append("4. Add oil and swirl to coat entire bottom")
            instructions.append("5. Oil should shimmer but NOT smoke (if smoking, reduce heat)")

            if any(meat in ingredient_text for meat in ['chicken', 'beef', 'pork', 'paneer']):
                instructions.append("\n🥩 COOKING PROTEIN:")
                instructions.append("6. Add seasoned meat/paneer in single layer")
                instructions.append("7. DON'T move pieces for 3-4 minutes - let them develop golden crust")
                instructions.append("8. You'll hear sizzling - this is good!")
                instructions.append("9. Flip each piece carefully with tongs")
                instructions.append("10. Cook another 3-4 minutes until golden brown")
                if 'chicken' in ingredient_text:
                    instructions.append("11. Check internal temperature reaches 165°F (75°C)")
                instructions.append("12. Remove to a clean plate and cover with foil")

                instructions.append("\n🧄 BUILDING FLAVORS:")
                instructions.append("13. In same pan (don't clean!), add garlic and onions")
                instructions.append("14. Cook 1-2 minutes until fragrant - you'll smell the aroma")
                instructions.append("15. Stir constantly to prevent burning")

                instructions.append("\n🥕 COOKING VEGETABLES:")
                instructions.append("16. Add hardest vegetables first (carrots, potatoes)")
                instructions.append("17. Cook 2-3 minutes, stirring occasionally")
                instructions.append("18. Add medium vegetables (capsicum, mushrooms)")
                instructions.append("19. Cook 2-3 minutes more")
                instructions.append("20. Add soft vegetables last (tomatoes, spinach)")
                instructions.append("21. Stir-fry everything for 3-5 minutes until crisp-tender")
                instructions.append("22. Vegetables should be bright in color")

                instructions.append("\n🔄 BRINGING IT TOGETHER:")
                instructions.append("23. Return cooked meat/paneer to pan")
                instructions.append("24. Toss everything gently for 1-2 minutes")
                instructions.append("25. Add any sauces or final seasonings")
                instructions.append("26. Cook 1 more minute to heat through")

            else:
                instructions.append("\n🥬 VEGETARIAN COOKING:")
                instructions.append("6. Add garlic and onions first - they're the flavor base")
                instructions.append("7. Cook 1-2 minutes until fragrant")
                instructions.append("8. Add vegetables in order of cooking time:")
                instructions.append("   - Hard vegetables first (carrots, potatoes)")
                instructions.append("   - Medium vegetables next (capsicum, mushrooms)")
                instructions.append("   - Soft vegetables last (tomatoes, spinach)")
                instructions.append("9. Stir-fry for 5-7 minutes until crisp-tender")
                instructions.append("10. Taste and adjust seasoning")

        elif cooking_method in ['roast', 'bake']:
            instructions.append("1. Preheat oven to 425°F (220°C)")
            instructions.append("2. Line a large baking sheet with parchment paper")
            instructions.append("3. Toss ingredients with oil, salt, and pepper in a large bowl")
            instructions.append("4. Arrange in single layer on prepared baking sheet - don't overcrowd")
            if any(meat in ingredient_text for meat in ['chicken', 'beef', 'pork']):
                instructions.append("5. Roast for 20-25 minutes until meat reaches internal temperature of 165°F")
            else:
                instructions.append("5. Roast for 15-20 minutes until vegetables are golden and tender")
            instructions.append("6. Flip ingredients halfway through cooking for even browning")

        elif cooking_method in ['simmer', 'braise']:
            instructions.append("1. Heat oil in a heavy-bottomed pot over medium heat")
            instructions.append("2. Add aromatics and cook until fragrant, about 2 minutes")
            if any(meat in ingredient_text for meat in ['chicken', 'beef', 'pork']):
                instructions.append("3. Add meat and brown on all sides, about 8-10 minutes total")
                instructions.append("4. Add liquid (broth/water) to cover meat by 1 inch")
                instructions.append("5. Bring to a gentle simmer, reduce heat to low")
                instructions.append("6. Cover and simmer for 45-60 minutes until meat is tender")
            else:
                instructions.append("3. Add vegetables and liquid, bring to a simmer")
                instructions.append("4. Reduce heat to low, cover and simmer 20-25 minutes")
            instructions.append("7. Stir occasionally and add more liquid if needed")

        # Cuisine-specific finishing touches
        if cuisine == 'Italian':
            instructions.append("\nITALIAN FINISHING TOUCHES:")
            instructions.append("• Drizzle with high-quality extra virgin olive oil")
            instructions.append("• Add fresh herbs (basil, parsley) in the last minute of cooking")
            instructions.append("• Finish with freshly grated Parmesan if desired")

        elif cuisine == 'Chinese' or cuisine == 'Asian':
            instructions.append("\nASIAN FINISHING TOUCHES:")
            instructions.append("• Add soy sauce and any final seasonings in last 30 seconds")
            instructions.append("• Garnish with sliced green onions or sesame seeds")
            instructions.append("• Serve immediately over steamed rice")

        elif cuisine == 'Mexican':
            instructions.append("\nMEXICAN FINISHING TOUCHES:")
            instructions.append("• Squeeze fresh lime juice over the dish")
            instructions.append("• Garnish with fresh cilantro and diced avocado")
            instructions.append("• Serve with warm tortillas or over rice")

        elif cuisine == 'Indian':
            instructions.append("\nINDIAN FINISHING TOUCHES:")
            instructions.append("• Adjust spice levels to taste in final minutes")
            instructions.append("• Garnish with fresh cilantro and a dollop of yogurt")
            instructions.append("• Serve with basmati rice or naan bread")

        # Final serving instructions
        instructions.append(f"\nSERVING:")
        instructions.append("• Taste and adjust seasoning with salt and pepper")
        instructions.append("• Let rest for 2-3 minutes to allow flavors to meld")
        instructions.append(f"• Serve immediately while hot")
        instructions.append(f"• This recipe serves {servings} people generously")

        return instructions
        
        return instructions
    
    def _calculate_prep_time(self, ingredients: List[str], cooking_method: str) -> str:
        """AI calculates prep time based on ingredients complexity"""
        base_time = 10  # Base 10 minutes
        
        # Add time based on ingredient complexity
        for ingredient in ingredients:
            if any(complex_ing in ingredient.lower() for complex_ing in ['meat', 'chicken', 'beef']):
                base_time += 5
            elif any(veg in ingredient.lower() for veg in ['onion', 'garlic', 'vegetable']):
                base_time += 3
        
        # Adjust for cooking method
        if cooking_method in ['roast', 'braise']:
            base_time += 5
        
        return f"{base_time} minutes"
    
    def _calculate_cook_time(self, ingredients: List[str], cooking_method: str) -> str:
        """AI calculates cooking time based on method and ingredients"""
        time_map = {
            'sauté': 15,
            'stir-fry': 10,
            'roast': 35,
            'braise': 45,
            'simmer': 25,
            'grill': 20
        }
        
        base_time = time_map.get(cooking_method, 20)
        
        # Adjust for protein content
        if any(meat in ' '.join(ingredients).lower() for meat in ['chicken', 'beef', 'pork']):
            base_time += 10
        
        return f"{base_time} minutes"
    
    def _assess_difficulty(self, ingredients: List[str], cooking_method: str) -> str:
        """AI assesses recipe difficulty"""
        complexity_score = 0
        
        # Score based on ingredients
        complexity_score += len(ingredients) * 0.5
        
        # Score based on cooking method
        method_difficulty = {
            'sauté': 1, 'stir-fry': 2, 'roast': 1,
            'braise': 3, 'simmer': 2, 'grill': 2
        }
        complexity_score += method_difficulty.get(cooking_method, 2)
        
        if complexity_score <= 3:
            return 'Easy'
        elif complexity_score <= 6:
            return 'Medium'
        else:
            return 'Hard'
    
    def _estimate_calories(self, ingredients: List[str], servings: int) -> int:
        """AI estimates calories based on ingredients"""
        total_calories = 0
        
        for ingredient in ingredients:
            ingredient_lower = ingredient.lower()
            
            # Calorie estimation per ingredient type
            if any(meat in ingredient_lower for meat in ['chicken', 'beef', 'pork']):
                total_calories += 200
            elif any(veg in ingredient_lower for veg in ['vegetable', 'onion', 'tomato']):
                total_calories += 25
            elif any(grain in ingredient_lower for grain in ['rice', 'pasta']):
                total_calories += 150
            elif any(fat in ingredient_lower for fat in ['oil', 'butter']):
                total_calories += 100
            else:
                total_calories += 50
        
        return int(total_calories / servings)
    
    def _determine_diet_type(self, ingredients: List[str]) -> str:
        """Determine diet type based on ingredients"""
        ingredient_text = ' '.join(ingredients).lower()
        
        if any(meat in ingredient_text for meat in ['chicken', 'beef', 'pork', 'fish', 'meat']):
            return 'Non-Vegetarian'
        elif any(dairy in ingredient_text for dairy in ['cheese', 'milk', 'butter', 'yogurt']):
            return 'Vegetarian'
        else:
            return 'Vegan'
    
    def _generate_ai_cooking_tips(self, ingredients: List[str], cooking_method: str, cuisine: str) -> List[str]:
        """Generate AI-powered cooking tips"""
        tips = []
        
        # Method-specific tips
        if cooking_method == 'sauté':
            tips.append("Don't overcrowd the pan - cook in batches if needed")
        elif cooking_method == 'roast':
            tips.append("Let ingredients come to room temperature before roasting")
        
        # Ingredient-specific tips
        if any('garlic' in ing.lower() for ing in ingredients):
            tips.append("Add garlic towards the end to prevent burning")
        
        if any('meat' in ing.lower() or 'chicken' in ing.lower() for ing in ingredients):
            tips.append("Use a meat thermometer for perfect doneness")
        
        # Cuisine-specific tips
        if cuisine == 'Italian':
            tips.append("Fresh herbs added at the end preserve their flavor")
        elif cuisine == 'Chinese':
            tips.append("Have all ingredients prepped before you start cooking")
        
        return tips
    
    def _analyze_flavor_profile(self, ingredients: List[str], cuisine: str) -> str:
        """Analyze the flavor profile of the dish"""
        ingredient_text = ' '.join(ingredients).lower()
        
        flavor_indicators = {
            'spicy': ['chili', 'pepper', 'hot', 'spice'],
            'savory': ['garlic', 'onion', 'meat', 'cheese'],
            'fresh': ['herb', 'basil', 'cilantro', 'lemon'],
            'rich': ['butter', 'cream', 'oil', 'cheese'],
            'aromatic': ['ginger', 'garlic', 'spice', 'herb']
        }
        
        detected_flavors = []
        for flavor, indicators in flavor_indicators.items():
            if any(indicator in ingredient_text for indicator in indicators):
                detected_flavors.append(flavor)
        
        if detected_flavors:
            return ', '.join(detected_flavors[:3])  # Top 3 flavors
        else:
            return 'balanced and flavorful'
