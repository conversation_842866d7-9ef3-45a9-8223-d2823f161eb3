#!/usr/bin/env python3
"""
Test script for MealMind ML implementation
Tests all ML components and provides usage examples
"""

import sys
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_configuration():
    """Test ML configuration loading"""
    logger.info("Testing ML configuration...")
    try:
        from ml_config import VisionConfig, NLPConfig, RecommendationConfig, APIConfig
        
        vision_config = VisionConfig()
        nlp_config = NLPConfig()
        rec_config = RecommendationConfig()
        api_config = APIConfig()
        
        logger.info(f"✓ Vision config loaded - YOLO threshold: {vision_config.YOLO_CONFIDENCE_THRESHOLD}")
        logger.info(f"✓ NLP config loaded - Fuzzy threshold: {nlp_config.FUZZY_MATCH_THRESHOLD}")
        logger.info(f"✓ Recommendation config loaded - Max recommendations: {rec_config.MAX_RECOMMENDATIONS}")
        logger.info(f"✓ API config loaded")
        
        return True
    except Exception as e:
        logger.error(f"Configuration test failed: {e}")
        return False

def test_image_processor():
    """Test image processing capabilities"""
    logger.info("Testing image processor...")
    try:
        from image_processor import create_image_processor
        
        processor = create_image_processor()
        
        # Test with a dummy image path (will fail gracefully)
        test_image = "test_image.jpg"
        is_valid = processor.validate_image(test_image)
        logger.info(f"✓ Image processor created - Validation test: {is_valid}")
        
        return True
    except Exception as e:
        logger.error(f"Image processor test failed: {e}")
        return False

def test_nlp_processor():
    """Test NLP processing capabilities"""
    logger.info("Testing NLP processor...")
    try:
        from nlp_processor import create_ingredient_standardizer
        
        standardizer = create_ingredient_standardizer()
        
        # Test ingredient standardization
        test_ingredients = [
            "2 cups fresh tomatoes, diced",
            "1 lb ground beef",
            "3 cloves garlic, minced",
            "1/2 cup olive oil"
        ]
        
        for ingredient in test_ingredients:
            result = standardizer.standardize_ingredient(ingredient)
            logger.info(f"  '{ingredient}' -> '{result['standardized_name']}' "
                       f"({result['quantity']} {result['unit']}) [{result['category']}]")
        
        # Test fuzzy matching
        similar = standardizer.find_similar_ingredients("tomato", limit=3)
        logger.info(f"✓ Similar to 'tomato': {[s[0] for s in similar]}")
        
        return True
    except Exception as e:
        logger.error(f"NLP processor test failed: {e}")
        return False

def test_computer_vision():
    """Test computer vision capabilities"""
    logger.info("Testing computer vision...")
    try:
        from computer_vision import create_ingredient_detector
        
        # Test in mock mode
        detector = create_ingredient_detector(use_mock=True)
        
        # Test detection with dummy image
        test_image = "test_fridge_image.jpg"
        detections = detector.detect_ingredients(test_image)
        
        logger.info(f"✓ Mock detection results ({len(detections)} ingredients):")
        for detection in detections[:3]:  # Show first 3
            logger.info(f"  - {detection['name']} (confidence: {detection['confidence']:.2f}, "
                       f"category: {detection['category']})")
        
        return True
    except Exception as e:
        logger.error(f"Computer vision test failed: {e}")
        return False

def test_recommendation_system():
    """Test recommendation system"""
    logger.info("Testing recommendation system...")
    try:
        from recommendation_system import create_recommendation_system
        
        recommender = create_recommendation_system()
        
        # Create sample recipe data
        sample_recipes = [
            {
                'id': 1,
                'name': 'Spaghetti Carbonara',
                'ingredients': {
                    'main': ['spaghetti pasta', 'eggs', 'pancetta'],
                    'dairy': ['parmesan cheese', 'heavy cream'],
                    'seasonings': ['black pepper', 'salt', 'olive oil']
                },
                'cuisine': 'Italian',
                'difficulty': 'Medium',
                'diet_type': 'Non-Veg',
                'prep_time': 30,
                'calories': 450
            },
            {
                'id': 2,
                'name': 'Vegetable Stir Fry',
                'ingredients': {
                    'vegetables': ['broccoli', 'carrots', 'bell peppers'],
                    'seasonings': ['soy sauce', 'garlic', 'ginger'],
                    'oils': ['sesame oil']
                },
                'cuisine': 'Chinese',
                'difficulty': 'Easy',
                'diet_type': 'Vegan',
                'prep_time': 15,
                'calories': 200
            }
        ]
        
        # Test recipe preparation
        df = recommender.prepare_recipe_data(sample_recipes)
        logger.info(f"✓ Prepared {len(df)} recipes for recommendation")
        
        # Test recommendations
        user_ingredients = ['pasta', 'eggs', 'cheese']
        user_preferences = {
            'dietary_preferences': ['vegetarian'],
            'favorite_cuisines': ['Italian'],
            'cooking_skill': 'Intermediate'
        }
        
        recommendations = recommender.get_content_based_recommendations(
            user_ingredients, user_preferences, limit=5
        )
        
        logger.info(f"✓ Generated {len(recommendations)} recommendations:")
        for rec in recommendations:
            score = rec.get('recommendation_score', 0)
            match_pct = rec.get('match_details', {}).get('match_percentage', 0)
            logger.info(f"  - {rec['name']} (score: {score:.2f}, match: {match_pct}%)")
        
        return True
    except Exception as e:
        logger.error(f"Recommendation system test failed: {e}")
        return False

def test_integrated_ml():
    """Test integrated ML functionality"""
    logger.info("Testing integrated ML functionality...")
    try:
        from ingredient_ml import (
            identify_ingredients_from_image,
            get_ingredient_suggestions,
            get_recipe_recommendations_for_ingredients,
            analyze_ingredient_image
        )
        
        # Test image analysis
        test_image = "sample_fridge.jpg"
        analysis = analyze_ingredient_image(test_image, use_advanced_ml=True)
        logger.info(f"✓ Image analysis completed - Method: {analysis.get('method', 'unknown')}")
        logger.info(f"  Detected {len(analysis['detected_ingredients'])} ingredients")
        
        # Test ingredient suggestions
        suggestions = get_ingredient_suggestions("tom", use_advanced_ml=True)
        logger.info(f"✓ Suggestions for 'tom': {suggestions[:3]}")
        
        # Test recipe recommendations
        sample_recipes = [
            {'id': 1, 'name': 'Tomato Pasta', 'ingredients': ['tomatoes', 'pasta'], 
             'cuisine': 'Italian', 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
            {'id': 2, 'name': 'Chicken Curry', 'ingredients': ['chicken', 'onions'], 
             'cuisine': 'Indian', 'difficulty': 'Medium', 'diet_type': 'Non-Veg'}
        ]
        
        recommendations = get_recipe_recommendations_for_ingredients(
            ['tomatoes', 'pasta'], sample_recipes, use_advanced_ml=True
        )
        logger.info(f"✓ Recipe recommendations: {len(recommendations)} recipes")
        
        return True
    except Exception as e:
        logger.error(f"Integrated ML test failed: {e}")
        return False

def test_fallback_mode():
    """Test fallback mode functionality"""
    logger.info("Testing fallback mode...")
    try:
        # Force fallback mode by using basic functions
        from ingredient_ml import identify_ingredients, get_ingredient_suggestions
        
        # Test basic ingredient identification
        text_ingredients = identify_ingredients("2 cups tomatoes, 1 onion, garlic cloves")
        logger.info(f"✓ Basic ingredient identification: {len(text_ingredients)} found")
        
        # Test basic suggestions
        basic_suggestions = get_ingredient_suggestions("tom", use_advanced_ml=False)
        logger.info(f"✓ Basic suggestions: {basic_suggestions[:3]}")
        
        return True
    except Exception as e:
        logger.error(f"Fallback mode test failed: {e}")
        return False

def run_performance_test():
    """Run basic performance tests"""
    logger.info("Running performance tests...")
    try:
        import time
        from nlp_processor import create_ingredient_standardizer
        
        standardizer = create_ingredient_standardizer()
        
        # Test batch processing
        test_ingredients = [
            "2 cups tomatoes", "1 lb chicken", "3 cloves garlic",
            "1/2 cup olive oil", "1 onion", "salt and pepper"
        ] * 10  # 60 ingredients total
        
        start_time = time.time()
        results = standardizer.batch_standardize(test_ingredients)
        end_time = time.time()
        
        processing_time = end_time - start_time
        rate = len(test_ingredients) / processing_time
        
        logger.info(f"✓ Processed {len(test_ingredients)} ingredients in {processing_time:.2f}s")
        logger.info(f"✓ Processing rate: {rate:.1f} ingredients/second")
        
        return True
    except Exception as e:
        logger.error(f"Performance test failed: {e}")
        return False

def main():
    """Run all tests"""
    logger.info("🧪 Starting MealMind ML Implementation Tests")
    logger.info("=" * 50)
    
    tests = [
        ("Configuration", test_configuration),
        ("Image Processor", test_image_processor),
        ("NLP Processor", test_nlp_processor),
        ("Computer Vision", test_computer_vision),
        ("Recommendation System", test_recommendation_system),
        ("Integrated ML", test_integrated_ml),
        ("Fallback Mode", test_fallback_mode),
        ("Performance", run_performance_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 Testing {test_name}...")
        try:
            if test_func():
                logger.info(f"✅ {test_name} - PASSED")
                passed += 1
            else:
                logger.error(f"❌ {test_name} - FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} - ERROR: {e}")
    
    logger.info("\n" + "=" * 50)
    logger.info(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! ML implementation is working correctly.")
    elif passed > total // 2:
        logger.info("⚠️  Most tests passed. Some advanced features may not be available.")
    else:
        logger.warning("❗ Many tests failed. Check your installation and dependencies.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
