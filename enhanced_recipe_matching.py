"""
Enhanced Recipe Matching System for MealMind
Uses ML-based semantic matching and fuzzy string matching for better ingredient matching
"""

from difflib import SequenceMatcher
import re
from typing import List, Dict, <PERSON><PERSON>

def calculate_enhanced_recipe_match(user_ingredients, recipe_ingredients):
    """Calculate match percentage and missing ingredients for a recipe using enhanced ML-based matching"""
    user_ingredients_lower = [ing.lower().strip() for ing in user_ingredients]

    # Handle both old format (list) and new format (dict with categories)
    if isinstance(recipe_ingredients, dict):
        # Flatten categorized ingredients
        all_recipe_ingredients = []
        for category, items in recipe_ingredients.items():
            all_recipe_ingredients.extend(items)
        recipe_ingredients_lower = [ing.lower().strip() for ing in all_recipe_ingredients]
    else:
        recipe_ingredients_lower = [ing.lower().strip() for ing in recipe_ingredients]

    matched_ingredients = []
    missing_ingredients = []
    partial_matches = []

    # Enhanced matching with semantic similarity and fuzzy matching
    for recipe_ing in recipe_ingredients_lower:
        found = False
        best_match_score = 0
        best_match_ingredient = None
        
        for user_ing in user_ingredients_lower:
            # Calculate similarity score using multiple methods
            similarity_score = calculate_ingredient_similarity(user_ing, recipe_ing)
            
            # Consider it a match if similarity is above threshold
            if similarity_score > 0.6:  # 60% similarity threshold
                matched_ingredients.append({
                    'recipe_ingredient': recipe_ing,
                    'user_ingredient': user_ing,
                    'similarity': similarity_score
                })
                found = True
                break
            elif similarity_score > best_match_score:
                best_match_score = similarity_score
                best_match_ingredient = user_ing

        if not found:
            if best_match_score > 0.3:  # Partial match threshold
                partial_matches.append({
                    'recipe_ingredient': recipe_ing,
                    'best_user_match': best_match_ingredient,
                    'similarity': best_match_score
                })
            missing_ingredients.append(recipe_ing)

    # Calculate enhanced match percentage with partial matches
    base_match_percentage = (len(matched_ingredients) / len(recipe_ingredients_lower)) * 100 if recipe_ingredients_lower else 0
    
    # Apply ML-based semantic boost if available
    semantic_boost = calculate_semantic_ingredient_match(user_ingredients, recipe_ingredients_lower)
    
    # Partial match bonus (up to 10% additional)
    partial_bonus = min(10, (len(partial_matches) / len(recipe_ingredients_lower)) * 20) if recipe_ingredients_lower else 0
    
    # Final match percentage with semantic enhancement
    final_match_percentage = min(100, base_match_percentage + semantic_boost + partial_bonus)

    return {
        'match_percentage': round(final_match_percentage, 1),
        'matched_ingredients': [m['recipe_ingredient'] for m in matched_ingredients],
        'missing_ingredients': missing_ingredients,
        'partial_matches': partial_matches,
        'total_ingredients': len(recipe_ingredients_lower),
        'semantic_boost': round(semantic_boost, 1),
        'partial_bonus': round(partial_bonus, 1),
        'detailed_matches': matched_ingredients
    }

def calculate_ingredient_similarity(user_ingredient, recipe_ingredient):
    """Calculate similarity between two ingredients using multiple methods"""
    user_ing = user_ingredient.lower().strip()
    recipe_ing = recipe_ingredient.lower().strip()
    
    # Exact match
    if user_ing == recipe_ing:
        return 1.0
    
    # Substring match
    if user_ing in recipe_ing or recipe_ing in user_ing:
        return 0.9
    
    # Word-level matching
    user_words = set(re.findall(r'\b\w+\b', user_ing))
    recipe_words = set(re.findall(r'\b\w+\b', recipe_ing))
    
    if user_words & recipe_words:  # Common words
        word_similarity = len(user_words & recipe_words) / len(user_words | recipe_words)
        if word_similarity > 0.5:
            return 0.8 * word_similarity
    
    # Fuzzy string matching
    sequence_similarity = SequenceMatcher(None, user_ing, recipe_ing).ratio()
    if sequence_similarity > 0.7:
        return 0.7 * sequence_similarity
    
    # Ingredient category matching (e.g., "tomato" matches "cherry tomatoes")
    ingredient_categories = {
        'tomato': ['tomato', 'tomatoes', 'cherry tomato', 'roma tomato', 'plum tomato'],
        'onion': ['onion', 'onions', 'red onion', 'white onion', 'yellow onion', 'green onion', 'scallion'],
        'chicken': ['chicken', 'chicken breast', 'chicken thigh', 'chicken leg', 'poultry'],
        'beef': ['beef', 'ground beef', 'beef steak', 'beef roast', 'steak'],
        'cheese': ['cheese', 'cheddar', 'mozzarella', 'parmesan', 'swiss', 'gouda'],
        'pepper': ['pepper', 'bell pepper', 'red pepper', 'green pepper', 'black pepper'],
        'oil': ['oil', 'olive oil', 'vegetable oil', 'canola oil', 'coconut oil'],
        'pasta': ['pasta', 'spaghetti', 'penne', 'fettuccine', 'linguine', 'macaroni'],
        'rice': ['rice', 'basmati rice', 'jasmine rice', 'brown rice', 'white rice'],
        'flour': ['flour', 'all-purpose flour', 'wheat flour', 'bread flour'],
        'milk': ['milk', 'whole milk', 'skim milk', '2% milk', 'almond milk'],
        'garlic': ['garlic', 'garlic cloves', 'minced garlic', 'garlic powder'],
        'ginger': ['ginger', 'fresh ginger', 'ground ginger', 'ginger root']
    }
    
    for category, variants in ingredient_categories.items():
        if any(variant in user_ing for variant in variants) and any(variant in recipe_ing for variant in variants):
            return 0.8
    
    return 0.0

def calculate_semantic_ingredient_match(user_ingredients, recipe_ingredients):
    """Calculate semantic boost using ingredient relationships"""
    # Semantic ingredient relationships
    semantic_groups = {
        'proteins': ['chicken', 'beef', 'pork', 'fish', 'turkey', 'lamb', 'tofu', 'eggs', 'beans', 'lentils'],
        'vegetables': ['tomato', 'onion', 'carrot', 'celery', 'pepper', 'mushroom', 'spinach', 'broccoli', 'potato', 'corn'],
        'herbs_spices': ['basil', 'oregano', 'thyme', 'garlic', 'ginger', 'cumin', 'paprika', 'salt', 'pepper', 'cilantro'],
        'dairy': ['milk', 'cheese', 'butter', 'cream', 'yogurt', 'sour cream'],
        'grains': ['rice', 'pasta', 'bread', 'flour', 'quinoa', 'oats', 'barley'],
        'oils_fats': ['oil', 'olive oil', 'butter', 'coconut oil', 'vegetable oil'],
        'aromatics': ['onion', 'garlic', 'ginger', 'shallot', 'leek'],
        'citrus': ['lemon', 'lime', 'orange', 'grapefruit'],
        'nuts_seeds': ['almonds', 'walnuts', 'sesame', 'sunflower', 'peanuts']
    }
    
    user_groups = set()
    recipe_groups = set()
    
    # Categorize user ingredients
    for ingredient in user_ingredients:
        for group, items in semantic_groups.items():
            if any(item in ingredient.lower() for item in items):
                user_groups.add(group)
    
    # Categorize recipe ingredients
    for ingredient in recipe_ingredients:
        for group, items in semantic_groups.items():
            if any(item in ingredient.lower() for item in items):
                recipe_groups.add(group)
    
    # Calculate semantic overlap
    common_groups = user_groups & recipe_groups
    total_groups = user_groups | recipe_groups
    
    if total_groups:
        semantic_similarity = len(common_groups) / len(total_groups)
        return semantic_similarity * 15  # Up to 15% boost for semantic matching
    
    return 0.0

def get_recipe_compatibility_score(user_ingredients, recipe):
    """Get overall compatibility score for a recipe"""
    match_data = calculate_enhanced_recipe_match(user_ingredients, recipe['ingredients'])
    
    # Base score from ingredient matching
    base_score = match_data['match_percentage']
    
    # Cuisine preference boost (if user has ingredients typical of a cuisine)
    cuisine_boost = calculate_cuisine_compatibility(user_ingredients, recipe.get('cuisine', ''))
    
    # Difficulty penalty (easier recipes get slight boost)
    difficulty_factor = {
        'Easy': 1.05,
        'Medium': 1.0,
        'Hard': 0.95
    }.get(recipe.get('difficulty', 'Medium'), 1.0)
    
    # Final compatibility score
    final_score = (base_score + cuisine_boost) * difficulty_factor
    
    return min(100, final_score)

def calculate_cuisine_compatibility(user_ingredients, recipe_cuisine):
    """Calculate how well user ingredients match a specific cuisine"""
    cuisine_indicators = {
        'Italian': ['tomato', 'basil', 'oregano', 'mozzarella', 'parmesan', 'olive oil', 'pasta', 'garlic'],
        'Chinese': ['soy sauce', 'ginger', 'garlic', 'rice', 'sesame oil', 'green onion', 'bok choy'],
        'Indian': ['cumin', 'turmeric', 'garam masala', 'ginger', 'garlic', 'onion', 'tomato', 'cilantro'],
        'American': ['beef', 'cheese', 'potato', 'corn', 'bacon', 'butter', 'milk'],
        'Mexican': ['cumin', 'chili', 'lime', 'cilantro', 'onion', 'tomato', 'cheese', 'beans']
    }
    
    if recipe_cuisine not in cuisine_indicators:
        return 0
    
    indicators = cuisine_indicators[recipe_cuisine]
    user_ingredients_lower = [ing.lower() for ing in user_ingredients]
    
    matches = 0
    for indicator in indicators:
        if any(indicator in user_ing for user_ing in user_ingredients_lower):
            matches += 1
    
    if matches > 0:
        return (matches / len(indicators)) * 5  # Up to 5% boost for cuisine compatibility
    
    return 0
