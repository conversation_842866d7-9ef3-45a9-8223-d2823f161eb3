"""
Computer Vision Module for Ingredient Detection
Implements multiple CV approaches: YOLOv8, Google Vision API, and custom models
"""

import cv2
import numpy as np
import logging
from typing import List, Dict, Tuple, Optional, Union
from pathlib import Path
import json
import requests
from PIL import Image

from ml_config import VisionConfig, APIConfig, DevConfig
from image_processor import ImageProcessor

# Set up logging
logger = logging.getLogger(__name__)

class IngredientDetector:
    """Main class for ingredient detection using computer vision"""
    
    def __init__(self, use_mock: bool = None):
        self.config = VisionConfig()
        self.api_config = APIConfig()
        self.image_processor = ImageProcessor()
        
        # Use mock mode if specified or if in development
        self.use_mock = use_mock if use_mock is not None else DevConfig.USE_MOCK_VISION_API
        
        # Initialize models
        self.yolo_model = None
        self.food_classifier = None
        
        if not self.use_mock:
            self._initialize_models()
    
    def _initialize_models(self):
        """Initialize computer vision models"""
        try:
            # Try to initialize YOLOv8
            self._initialize_yolo()
            
            # Try to initialize food classifier
            self._initialize_food_classifier()
            
        except Exception as e:
            logger.warning(f"Failed to initialize some models: {e}")
            logger.info("Falling back to mock mode")
            self.use_mock = True
    
    def _initialize_yolo(self):
        """Initialize YOLOv8 model"""
        try:
            from ultralytics import YOLO
            
            model_path = self.config.YOLO_MODEL_PATH
            
            # Download model if not exists
            if not model_path.exists():
                logger.info("Downloading YOLOv8 model...")
                self._download_yolo_model()
            
            # Load model
            self.yolo_model = YOLO(str(model_path))
            logger.info("YOLOv8 model initialized successfully")
            
        except ImportError:
            logger.warning("ultralytics not installed, YOLOv8 unavailable")
        except Exception as e:
            logger.error(f"Failed to initialize YOLOv8: {e}")
    
    def _download_yolo_model(self):
        """Download YOLOv8 model if not present"""
        try:
            import urllib.request
            from ml_config import ModelPaths
            
            model_path = self.config.YOLO_MODEL_PATH
            model_path.parent.mkdir(parents=True, exist_ok=True)
            
            urllib.request.urlretrieve(ModelPaths.YOLO_MODEL_URL, str(model_path))
            logger.info(f"Downloaded YOLOv8 model to {model_path}")
            
        except Exception as e:
            logger.error(f"Failed to download YOLOv8 model: {e}")
            raise
    
    def _initialize_food_classifier(self):
        """Initialize food classification model"""
        try:
            from transformers import pipeline
            
            self.food_classifier = pipeline(
                "image-classification",
                model="nateraw/food",
                return_top_k=5
            )
            logger.info("Food classifier initialized successfully")
            
        except ImportError:
            logger.warning("transformers not installed, food classifier unavailable")
        except Exception as e:
            logger.error(f"Failed to initialize food classifier: {e}")
    
    def detect_ingredients(self, image_path: Union[str, Path]) -> List[Dict]:
        """
        Main method to detect ingredients from image
        
        Args:
            image_path: Path to image file
            
        Returns:
            List of detected ingredients with confidence scores
        """
        if self.use_mock:
            return self._mock_detection(image_path)
        
        try:
            # Preprocess image
            processed_image = self.image_processor.preprocess_image(image_path)
            if processed_image is None:
                logger.error("Failed to preprocess image")
                return []
            
            # Try multiple detection methods
            detections = []
            
            # Method 1: YOLOv8 object detection
            if self.yolo_model:
                yolo_detections = self._detect_with_yolo(processed_image)
                detections.extend(yolo_detections)
            
            # Method 2: Food classifier
            if self.food_classifier:
                classifier_detections = self._detect_with_classifier(processed_image)
                detections.extend(classifier_detections)
            
            # Method 3: Google Vision API (if configured)
            if self.api_config.GOOGLE_VISION_API_KEY:
                vision_detections = self._detect_with_google_vision(image_path)
                detections.extend(vision_detections)
            
            # Combine and filter detections
            final_detections = self._combine_detections(detections)
            
            logger.info(f"Detected {len(final_detections)} ingredients")
            return final_detections
            
        except Exception as e:
            logger.error(f"Ingredient detection failed: {e}")
            return self._mock_detection(image_path)
    
    def _detect_with_yolo(self, image: np.ndarray) -> List[Dict]:
        """Detect objects using YOLOv8"""
        try:
            # Run inference
            results = self.yolo_model(image, conf=self.config.YOLO_CONFIDENCE_THRESHOLD)
            
            detections = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Get class name
                        class_id = int(box.cls[0])
                        class_name = self.yolo_model.names[class_id]
                        confidence = float(box.conf[0])
                        
                        # Filter for food-related classes
                        if self._is_food_related(class_name):
                            # Convert to ingredient name
                            ingredient_name = self._object_to_ingredient(class_name)
                            
                            detection = {
                                'name': ingredient_name,
                                'confidence': confidence,
                                'method': 'yolo',
                                'category': self._categorize_ingredient(ingredient_name),
                                'bbox': box.xyxy[0].tolist()  # Bounding box coordinates
                            }
                            detections.append(detection)
            
            logger.debug(f"YOLOv8 detected {len(detections)} objects")
            return detections
            
        except Exception as e:
            logger.error(f"YOLOv8 detection failed: {e}")
            return []
    
    def _detect_with_classifier(self, image: np.ndarray) -> List[Dict]:
        """Detect food items using food classifier"""
        try:
            # Convert to PIL Image
            pil_image = Image.fromarray(image)
            
            # Run classification
            results = self.food_classifier(pil_image)
            
            detections = []
            for result in results:
                label = result['label']
                confidence = result['score']
                
                # Convert label to ingredient name
                ingredient_name = self._label_to_ingredient(label)
                
                detection = {
                    'name': ingredient_name,
                    'confidence': confidence,
                    'method': 'classifier',
                    'category': self._categorize_ingredient(ingredient_name)
                }
                detections.append(detection)
            
            logger.debug(f"Food classifier detected {len(detections)} items")
            return detections
            
        except Exception as e:
            logger.error(f"Food classifier detection failed: {e}")
            return []
    
    def _detect_with_google_vision(self, image_path: Union[str, Path]) -> List[Dict]:
        """Detect objects using Google Vision API"""
        try:
            from google.cloud import vision
            
            client = vision.ImageAnnotatorClient()
            
            # Read image
            with open(image_path, 'rb') as image_file:
                content = image_file.read()
            
            image = vision.Image(content=content)
            
            # Detect objects
            response = client.object_localization(image=image)
            objects = response.localized_object_annotations
            
            detections = []
            for obj in objects:
                if self._is_food_related(obj.name):
                    ingredient_name = self._object_to_ingredient(obj.name)
                    
                    detection = {
                        'name': ingredient_name,
                        'confidence': obj.score,
                        'method': 'google_vision',
                        'category': self._categorize_ingredient(ingredient_name)
                    }
                    detections.append(detection)
            
            logger.debug(f"Google Vision detected {len(detections)} objects")
            return detections
            
        except Exception as e:
            logger.error(f"Google Vision detection failed: {e}")
            return []
    
    def _mock_detection(self, image_path: Union[str, Path]) -> List[Dict]:
        """Enhanced mock ingredient detection for development/testing"""
        import random

        # Enhanced ingredient database for better detection
        enhanced_ingredients = {
            'vegetables': ['tomato', 'onion', 'garlic', 'carrot', 'bell pepper', 'broccoli', 'spinach', 'lettuce', 'cucumber', 'mushroom'],
            'proteins': ['chicken breast', 'ground beef', 'salmon', 'eggs', 'tofu', 'bacon', 'shrimp'],
            'grains': ['rice', 'pasta', 'bread', 'quinoa', 'oats'],
            'dairy': ['cheese', 'milk', 'butter', 'yogurt', 'cream'],
            'herbs_spices': ['basil', 'oregano', 'thyme', 'salt', 'pepper', 'garlic powder', 'paprika'],
            'fruits': ['lemon', 'lime', 'apple', 'banana', 'orange'],
            'pantry': ['olive oil', 'soy sauce', 'vinegar', 'flour', 'sugar']
        }

        # Simulate realistic detection based on image name or random selection
        image_name = Path(image_path).stem.lower()

        # Try to infer ingredients from filename with better matching
        detected_ingredients = []
        for category, ingredients in enhanced_ingredients.items():
            for ingredient in ingredients:
                # Check for partial matches and common variations
                ingredient_words = ingredient.split()
                if any(word in image_name for word in ingredient_words) or ingredient in image_name:
                    detected_ingredients.append({
                        'name': ingredient,
                        'category': category,
                        'confidence': random.uniform(0.85, 0.98)  # Higher confidence for filename matches
                    })

        # If no matches from filename, simulate realistic kitchen ingredients
        if not detected_ingredients:
            # Simulate a typical kitchen scenario with common ingredient combinations
            scenarios = [
                # Italian cooking
                ['tomato', 'basil', 'garlic', 'onion', 'olive oil', 'cheese'],
                # Asian stir-fry
                ['chicken breast', 'bell pepper', 'onion', 'garlic', 'soy sauce', 'rice'],
                # Breakfast items
                ['eggs', 'bacon', 'bread', 'butter', 'milk'],
                # Salad ingredients
                ['lettuce', 'tomato', 'cucumber', 'carrot', 'olive oil'],
                # Mexican cuisine
                ['ground beef', 'onion', 'bell pepper', 'tomato', 'cheese'],
                # Soup ingredients
                ['carrot', 'onion', 'garlic', 'broccoli', 'chicken breast']
            ]

            # Select a random scenario
            selected_scenario = random.choice(scenarios)

            # Add some random additional ingredients
            all_ingredients = []
            for ingredients in enhanced_ingredients.values():
                all_ingredients.extend(ingredients)

            additional = random.sample([ing for ing in all_ingredients if ing not in selected_scenario],
                                     random.randint(1, 3))
            selected_scenario.extend(additional)

            for ingredient in selected_scenario:
                # Find category
                category = 'other'
                for cat, ingredients in enhanced_ingredients.items():
                    if ingredient in ingredients:
                        category = cat
                        break

                detected_ingredients.append({
                    'name': ingredient,
                    'category': category,
                    'confidence': random.uniform(0.70, 0.90)
                })

        # Create detection results with enhanced metadata
        detections = []
        for item in detected_ingredients:
            detection = {
                'name': item['name'],
                'confidence': item['confidence'],
                'method': 'enhanced_mock',
                'category': item['category'].replace('_', ' ').title(),
                'quantity': self._estimate_quantity(item['name']),
                'unit': self._get_default_unit(item['name']),
                'freshness': random.choice(['fresh', 'good', 'excellent']),
                'color_analysis': self._simulate_color_analysis(item['name'])
            }
            detections.append(detection)

        # Sort by confidence
        detections.sort(key=lambda x: x['confidence'], reverse=True)

        logger.info(f"Enhanced mock detection: {[d['name'] for d in detections[:8]]}")
        return detections[:8]  # Return top 8 detections

    def _estimate_quantity(self, ingredient_name: str) -> str:
        """Estimate quantity based on ingredient type"""
        import random

        if any(meat in ingredient_name.lower() for meat in ['chicken', 'beef', 'pork', 'fish']):
            return f"{random.randint(1, 3)} lbs"
        elif any(veg in ingredient_name.lower() for veg in ['tomato', 'onion', 'pepper']):
            return f"{random.randint(2, 5)} pieces"
        elif 'egg' in ingredient_name.lower():
            return f"{random.randint(4, 12)} pieces"
        elif any(grain in ingredient_name.lower() for grain in ['rice', 'pasta']):
            return f"{random.randint(1, 3)} cups"
        else:
            return f"{random.randint(1, 2)} cups"

    def _get_default_unit(self, ingredient_name: str) -> str:
        """Get default unit for ingredient"""
        if any(meat in ingredient_name.lower() for meat in ['chicken', 'beef', 'pork', 'fish']):
            return 'lbs'
        elif any(veg in ingredient_name.lower() for veg in ['tomato', 'onion', 'pepper']):
            return 'pieces'
        elif 'egg' in ingredient_name.lower():
            return 'pieces'
        elif any(spice in ingredient_name.lower() for spice in ['salt', 'pepper', 'basil', 'oregano']):
            return 'tsp'
        else:
            return 'cups'

    def _simulate_color_analysis(self, ingredient_name: str) -> str:
        """Simulate color analysis for ingredients"""
        color_map = {
            'tomato': 'red',
            'carrot': 'orange',
            'bell pepper': 'red/yellow/green',
            'broccoli': 'green',
            'spinach': 'dark green',
            'lettuce': 'light green',
            'onion': 'white/yellow',
            'garlic': 'white',
            'lemon': 'yellow',
            'lime': 'green',
            'chicken': 'pink/white',
            'beef': 'red',
            'cheese': 'yellow/white'
        }

        return color_map.get(ingredient_name.lower(), 'mixed colors')
    
    def _is_food_related(self, class_name: str) -> bool:
        """Check if detected class is food-related"""
        food_keywords = [
            'apple', 'banana', 'orange', 'broccoli', 'carrot', 'pizza', 'sandwich',
            'hot dog', 'donut', 'cake', 'bowl', 'cup', 'bottle', 'dining table'
        ]
        return class_name.lower() in food_keywords or any(keyword in class_name.lower() for keyword in food_keywords)
    
    def _object_to_ingredient(self, object_name: str) -> str:
        """Convert detected object name to ingredient name"""
        # Mapping from object names to ingredient names
        object_to_ingredient_map = {
            'apple': 'apple',
            'banana': 'banana',
            'orange': 'orange',
            'broccoli': 'broccoli',
            'carrot': 'carrot',
            'pizza': 'cheese',  # Assume cheese from pizza
            'sandwich': 'bread',
            'hot dog': 'sausage',
            'bowl': 'bowl',  # Container, might contain ingredients
            'bottle': 'oil',  # Assume cooking oil
            'dining table': 'various ingredients'
        }
        
        return object_to_ingredient_map.get(object_name.lower(), object_name)
    
    def _label_to_ingredient(self, label: str) -> str:
        """Convert classifier label to ingredient name"""
        # Clean up label (remove underscores, etc.)
        ingredient = label.replace('_', ' ').lower()
        
        # Map specific labels to common ingredient names
        label_mappings = {
            'chicken_breast': 'chicken breast',
            'ground_beef': 'ground beef',
            'cherry_tomatoes': 'tomatoes',
            'bell_pepper': 'bell pepper'
        }
        
        return label_mappings.get(ingredient, ingredient)
    
    def _categorize_ingredient(self, ingredient_name: str) -> str:
        """Categorize ingredient into food groups"""
        from ml_config import NLPConfig
        
        ingredient_lower = ingredient_name.lower()
        
        for category, ingredients in NLPConfig.INGREDIENT_CATEGORIES.items():
            if any(ing in ingredient_lower for ing in ingredients):
                return category
        
        return 'other'
    
    def _combine_detections(self, detections: List[Dict]) -> List[Dict]:
        """Combine detections from multiple methods and remove duplicates"""
        # Group detections by ingredient name
        grouped = {}
        for detection in detections:
            name = detection['name'].lower()
            if name not in grouped:
                grouped[name] = []
            grouped[name].append(detection)
        
        # Combine detections for each ingredient
        combined = []
        for name, group in grouped.items():
            if len(group) == 1:
                combined.append(group[0])
            else:
                # Take the detection with highest confidence
                best_detection = max(group, key=lambda x: x['confidence'])
                
                # Average confidence if multiple methods agree
                avg_confidence = sum(d['confidence'] for d in group) / len(group)
                best_detection['confidence'] = avg_confidence
                best_detection['methods'] = [d['method'] for d in group]
                
                combined.append(best_detection)
        
        # Sort by confidence and return top detections
        combined.sort(key=lambda x: x['confidence'], reverse=True)
        return combined[:10]  # Return top 10 detections

# Factory function
def create_ingredient_detector(use_mock: bool = None) -> IngredientDetector:
    """Create IngredientDetector instance"""
    return IngredientDetector(use_mock=use_mock)

# Utility functions
def detect_ingredients_from_image(image_path: Union[str, Path], 
                                use_mock: bool = None) -> List[Dict]:
    """Convenience function to detect ingredients from a single image"""
    detector = create_ingredient_detector(use_mock=use_mock)
    return detector.detect_ingredients(image_path)
