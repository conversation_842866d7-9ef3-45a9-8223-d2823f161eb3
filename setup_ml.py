#!/usr/bin/env python3
"""
Setup script for MealMind ML components
Downloads models and sets up the ML environment
"""

import os
import sys
import subprocess
import urllib.request
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        logger.error("Python 3.8 or higher is required")
        return False
    logger.info(f"Python version: {sys.version}")
    return True

def install_requirements():
    """Install required packages"""
    logger.info("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        logger.info("Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install requirements: {e}")
        return False

def download_spacy_model():
    """Download spaCy English model"""
    logger.info("Downloading spaCy English model...")
    try:
        subprocess.check_call([sys.executable, "-m", "spacy", "download", "en_core_web_sm"])
        logger.info("spaCy model downloaded successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.warning(f"Failed to download spaCy model: {e}")
        logger.info("You can download it manually later with: python -m spacy download en_core_web_sm")
        return False

def create_directories():
    """Create necessary directories"""
    directories = [
        'models',
        'data',
        'uploads',
        'logs',
        'test_images'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        logger.info(f"Created directory: {directory}")

def download_yolo_model():
    """Download YOLOv8 model"""
    logger.info("Downloading YOLOv8 model...")
    try:
        models_dir = Path('models')
        models_dir.mkdir(exist_ok=True)
        
        yolo_path = models_dir / 'yolov8n.pt'
        if not yolo_path.exists():
            url = 'https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt'
            urllib.request.urlretrieve(url, str(yolo_path))
            logger.info("YOLOv8 model downloaded successfully")
        else:
            logger.info("YOLOv8 model already exists")
        return True
    except Exception as e:
        logger.warning(f"Failed to download YOLOv8 model: {e}")
        logger.info("YOLOv8 will be downloaded automatically when first used")
        return False

def test_ml_components():
    """Test if ML components are working"""
    logger.info("Testing ML components...")
    
    try:
        # Test basic imports
        from ml_config import VisionConfig, NLPConfig, RecommendationConfig
        logger.info("✓ ML configuration loaded")
        
        # Test image processor
        from image_processor import create_image_processor
        processor = create_image_processor()
        logger.info("✓ Image processor initialized")
        
        # Test NLP processor
        from nlp_processor import create_ingredient_standardizer
        standardizer = create_ingredient_standardizer()
        test_result = standardizer.standardize_ingredient("2 cups fresh tomatoes")
        logger.info(f"✓ NLP processor working: {test_result['standardized_name']}")
        
        # Test computer vision (mock mode)
        from computer_vision import create_ingredient_detector
        detector = create_ingredient_detector(use_mock=True)
        logger.info("✓ Computer vision detector initialized")
        
        # Test recommendation system
        from recommendation_system import create_recommendation_system
        recommender = create_recommendation_system()
        logger.info("✓ Recommendation system initialized")
        
        logger.info("All ML components are working correctly!")
        return True
        
    except Exception as e:
        logger.error(f"ML component test failed: {e}")
        return False

def create_sample_test_data():
    """Create sample test data"""
    logger.info("Creating sample test data...")
    
    # Create sample user preferences
    sample_preferences = {
        'dietary_preferences': ['vegetarian'],
        'favorite_cuisines': ['Italian', 'Indian'],
        'cooking_skill': 'Intermediate',
        'allergies': []
    }
    
    # Save to data directory
    import json
    data_dir = Path('data')
    with open(data_dir / 'sample_preferences.json', 'w') as f:
        json.dump(sample_preferences, f, indent=2)
    
    logger.info("Sample test data created")

def setup_environment_variables():
    """Setup environment variables"""
    logger.info("Setting up environment variables...")
    
    env_template = """
# MealMind ML Configuration
# Copy this to .env and fill in your API keys

# Google Cloud Vision API (optional)
GOOGLE_VISION_API_KEY=your_google_vision_api_key_here
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json

# Azure Computer Vision (optional)
AZURE_VISION_ENDPOINT=your_azure_vision_endpoint_here
AZURE_VISION_KEY=your_azure_vision_key_here

# Hugging Face API (optional)
HUGGINGFACE_API_KEY=your_huggingface_api_key_here

# Spoonacular API (for recipe data)
SPOONACULAR_API_KEY=your_spoonacular_api_key_here
"""
    
    with open('.env.template', 'w') as f:
        f.write(env_template)
    
    logger.info("Environment template created (.env.template)")
    logger.info("Copy .env.template to .env and add your API keys")

def main():
    """Main setup function"""
    logger.info("Starting MealMind ML setup...")
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Install requirements
    if not install_requirements():
        logger.warning("Some requirements failed to install. Please install manually.")
    
    # Download spaCy model
    download_spacy_model()
    
    # Download YOLOv8 model
    download_yolo_model()
    
    # Create sample data
    create_sample_test_data()
    
    # Setup environment variables
    setup_environment_variables()
    
    # Test ML components
    if test_ml_components():
        logger.info("🎉 MealMind ML setup completed successfully!")
        logger.info("You can now run the application with: python app.py")
    else:
        logger.warning("Setup completed with some issues. Check the logs above.")
        logger.info("The application will work in fallback mode without advanced ML features.")

if __name__ == "__main__":
    main()
