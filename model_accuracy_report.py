#!/usr/bin/env python3
"""
Comprehensive Model Accuracy Report for MealMind ML System
Analyzes performance metrics, accuracy scores, and benchmarks
"""

import time
import json
from typing import Dict, List, Tuple
import random

def analyze_nlp_accuracy():
    """Analyze NLP ingredient standardization accuracy"""
    print("🧠 NLP INGREDIENT STANDARDIZATION ACCURACY")
    print("=" * 60)
    
    try:
        from nlp_processor import create_ingredient_standardizer
        standardizer = create_ingredient_standardizer()
        
        # Test cases with expected results
        test_cases = [
            # (input, expected_standardized, expected_category)
            ("2 cups fresh tomatoes", "tomato", "vegetables"),
            ("1 lb ground beef", "beef", "proteins"),
            ("3 cloves garlic minced", "garlic", "spices"),
            ("1/2 cup olive oil", "oil", "other"),
            ("2 medium onions diced", "onion", "vegetables"),
            ("1 cup basmati rice", "rice", "grains"),
            ("8 oz paneer cubed", "paneer", "proteins"),
            ("2 large capsicum sliced", "capsicum", "vegetables"),
            ("1 tsp garam masala", "garam masala", "spices"),
            ("500g chicken breast", "chicken", "proteins")
        ]
        
        correct_standardization = 0
        correct_categorization = 0
        total_tests = len(test_cases)
        
        print("Test Results:")
        for input_text, expected_name, expected_category in test_cases:
            result = standardizer.standardize_ingredient(input_text)
            
            name_correct = expected_name.lower() in result['standardized_name'].lower()
            category_correct = result['category'] == expected_category
            
            if name_correct:
                correct_standardization += 1
            if category_correct:
                correct_categorization += 1
            
            status_name = "✅" if name_correct else "❌"
            status_cat = "✅" if category_correct else "❌"
            
            print(f"  {status_name} '{input_text}' → '{result['standardized_name']}' [{result['category']}] {status_cat}")
        
        standardization_accuracy = (correct_standardization / total_tests) * 100
        categorization_accuracy = (correct_categorization / total_tests) * 100
        
        print(f"\n📊 NLP ACCURACY METRICS:")
        print(f"  • Ingredient Standardization: {standardization_accuracy:.1f}%")
        print(f"  • Category Classification: {categorization_accuracy:.1f}%")
        print(f"  • Overall NLP Accuracy: {(standardization_accuracy + categorization_accuracy) / 2:.1f}%")
        
        return {
            'standardization_accuracy': standardization_accuracy,
            'categorization_accuracy': categorization_accuracy,
            'overall_accuracy': (standardization_accuracy + categorization_accuracy) / 2
        }
        
    except Exception as e:
        print(f"❌ NLP accuracy test failed: {e}")
        return {'standardization_accuracy': 0, 'categorization_accuracy': 0, 'overall_accuracy': 0}

def analyze_recipe_matching_accuracy():
    """Analyze recipe matching algorithm accuracy"""
    print("\n🔍 RECIPE MATCHING ACCURACY")
    print("=" * 60)
    
    try:
        from enhanced_recipe_matching import calculate_enhanced_recipe_match
        
        # Test cases: (user_ingredients, recipe_ingredients, expected_match_range)
        test_cases = [
            # Perfect match
            (["chicken", "tomato", "onion"], {"proteins": ["chicken"], "vegetables": ["tomato", "onion"]}, (90, 100)),
            # Partial match
            (["chicken", "tomato"], {"proteins": ["chicken"], "vegetables": ["tomato", "onion", "garlic"]}, (60, 80)),
            # Poor match
            (["beef"], {"proteins": ["chicken"], "vegetables": ["tomato", "onion"]}, (0, 30)),
            # Synonym match
            (["tomatoes", "onions"], {"vegetables": ["tomato", "onion"]}, (80, 100)),
            # Mixed match
            (["chicken breast", "bell pepper"], {"proteins": ["chicken"], "vegetables": ["pepper", "onion"]}, (70, 90))
        ]
        
        correct_predictions = 0
        total_tests = len(test_cases)
        
        print("Recipe Matching Test Results:")
        for user_ings, recipe_ings, expected_range in test_cases:
            result = calculate_enhanced_recipe_match(user_ings, recipe_ings)
            match_percentage = result['match_percentage']
            
            is_correct = expected_range[0] <= match_percentage <= expected_range[1]
            if is_correct:
                correct_predictions += 1
            
            status = "✅" if is_correct else "❌"
            print(f"  {status} {user_ings} vs {recipe_ings}")
            print(f"      Match: {match_percentage:.1f}% (expected: {expected_range[0]}-{expected_range[1]}%)")
        
        matching_accuracy = (correct_predictions / total_tests) * 100
        
        print(f"\n📊 RECIPE MATCHING ACCURACY:")
        print(f"  • Algorithm Accuracy: {matching_accuracy:.1f}%")
        print(f"  • Similarity Threshold: 60% (configurable)")
        print(f"  • Partial Match Threshold: 30%")
        
        return {'matching_accuracy': matching_accuracy}
        
    except Exception as e:
        print(f"❌ Recipe matching accuracy test failed: {e}")
        return {'matching_accuracy': 0}

def analyze_performance_metrics():
    """Analyze system performance metrics"""
    print("\n⚡ PERFORMANCE METRICS")
    print("=" * 60)
    
    try:
        from nlp_processor import create_ingredient_standardizer
        standardizer = create_ingredient_standardizer()
        
        # Performance test data
        test_ingredients = [
            "2 cups tomatoes", "1 lb chicken", "3 cloves garlic",
            "1/2 cup olive oil", "1 onion", "salt and pepper",
            "1 cup rice", "2 tbsp butter", "1 tsp cumin"
        ] * 20  # 180 ingredients total
        
        # Single ingredient processing
        start_time = time.time()
        for ingredient in test_ingredients[:10]:
            standardizer.standardize_ingredient(ingredient)
        single_time = time.time() - start_time
        single_rate = 10 / single_time
        
        # Batch processing
        start_time = time.time()
        results = standardizer.batch_standardize(test_ingredients)
        batch_time = time.time() - start_time
        batch_rate = len(test_ingredients) / batch_time
        
        print(f"📈 PROCESSING PERFORMANCE:")
        print(f"  • Single Processing: {single_rate:.1f} ingredients/second")
        print(f"  • Batch Processing: {batch_rate:.1f} ingredients/second")
        print(f"  • Batch Efficiency: {(batch_rate/single_rate):.1f}x faster")
        print(f"  • Memory Usage: Optimized for {len(test_ingredients)} ingredients")
        
        return {
            'single_rate': single_rate,
            'batch_rate': batch_rate,
            'efficiency_gain': batch_rate/single_rate
        }
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return {'single_rate': 0, 'batch_rate': 0, 'efficiency_gain': 0}

def analyze_model_configurations():
    """Analyze current model configuration and thresholds"""
    print("\n⚙️ MODEL CONFIGURATIONS & THRESHOLDS")
    print("=" * 60)
    
    try:
        from ml_config import VisionConfig, NLPConfig, RecommendationConfig
        
        vision_config = VisionConfig()
        nlp_config = NLPConfig()
        rec_config = RecommendationConfig()
        
        print("🎯 ACCURACY THRESHOLDS:")
        print(f"  • YOLO Confidence Threshold: {vision_config.YOLO_CONFIDENCE_THRESHOLD} (50%)")
        print(f"  • YOLO IOU Threshold: {vision_config.YOLO_IOU_THRESHOLD} (45%)")
        print(f"  • NLP Fuzzy Match Threshold: {nlp_config.FUZZY_MATCH_THRESHOLD}% (80%)")
        print(f"  • Recipe Match Threshold: 60% (in enhanced_recipe_matching.py)")
        print(f"  • Partial Match Threshold: 30%")
        
        print(f"\n🔧 MODEL SETTINGS:")
        print(f"  • Max Image Size: {vision_config.MAX_IMAGE_SIZE}")
        print(f"  • Image Quality: {vision_config.IMAGE_QUALITY}%")
        print(f"  • Max Recommendations: {rec_config.MAX_RECOMMENDATIONS}")
        print(f"  • Ingredient Match Weight: {rec_config.PREFERENCE_WEIGHTS['ingredient_match']}")
        
        print(f"\n📚 DATABASE SIZE:")
        print(f"  • Ingredient Synonyms: {len(nlp_config.INGREDIENT_SYNONYMS)} base ingredients")
        print(f"  • Ingredient Categories: {len(nlp_config.INGREDIENT_CATEGORIES)} categories")
        print(f"  • Food Detection Classes: {len(vision_config.FOOD_CLASSES)} classes")
        
        return {
            'yolo_confidence': vision_config.YOLO_CONFIDENCE_THRESHOLD,
            'fuzzy_threshold': nlp_config.FUZZY_MATCH_THRESHOLD,
            'synonym_count': len(nlp_config.INGREDIENT_SYNONYMS),
            'category_count': len(nlp_config.INGREDIENT_CATEGORIES)
        }
        
    except Exception as e:
        print(f"❌ Configuration analysis failed: {e}")
        return {}

def generate_accuracy_summary():
    """Generate comprehensive accuracy summary"""
    print("\n📋 COMPREHENSIVE ACCURACY REPORT")
    print("=" * 60)
    
    # Run all accuracy tests
    nlp_results = analyze_nlp_accuracy()
    matching_results = analyze_recipe_matching_accuracy()
    performance_results = analyze_performance_metrics()
    config_results = analyze_model_configurations()
    
    # Calculate overall system accuracy
    component_accuracies = [
        nlp_results.get('overall_accuracy', 0),
        matching_results.get('matching_accuracy', 0)
    ]
    
    overall_accuracy = sum(component_accuracies) / len(component_accuracies)
    
    print(f"\n🎯 OVERALL SYSTEM ACCURACY: {overall_accuracy:.1f}%")
    print(f"\n📊 COMPONENT BREAKDOWN:")
    print(f"  • NLP Standardization: {nlp_results.get('standardization_accuracy', 0):.1f}%")
    print(f"  • Category Classification: {nlp_results.get('categorization_accuracy', 0):.1f}%")
    print(f"  • Recipe Matching: {matching_results.get('matching_accuracy', 0):.1f}%")
    print(f"  • Processing Speed: {performance_results.get('batch_rate', 0):.1f} ingredients/sec")
    
    print(f"\n🏆 SYSTEM GRADE:")
    if overall_accuracy >= 90:
        grade = "A+ (Excellent)"
    elif overall_accuracy >= 80:
        grade = "A (Very Good)"
    elif overall_accuracy >= 70:
        grade = "B (Good)"
    elif overall_accuracy >= 60:
        grade = "C (Fair)"
    else:
        grade = "D (Needs Improvement)"
    
    print(f"  • Overall Grade: {grade}")
    
    return {
        'overall_accuracy': overall_accuracy,
        'nlp_results': nlp_results,
        'matching_results': matching_results,
        'performance_results': performance_results,
        'config_results': config_results,
        'grade': grade
    }

if __name__ == "__main__":
    print("🧪 MEALMIND ML MODEL ACCURACY ANALYSIS")
    print("=" * 70)
    print("Analyzing all ML components for accuracy and performance...")
    print()
    
    # Generate comprehensive report
    summary = generate_accuracy_summary()
    
    print("\n" + "=" * 70)
    print("📄 ACCURACY REPORT COMPLETE")
    print(f"Overall System Accuracy: {summary['overall_accuracy']:.1f}%")
    print(f"System Grade: {summary['grade']}")
    print("\n💡 RECOMMENDATIONS:")
    
    if summary['overall_accuracy'] < 80:
        print("  • Consider installing spaCy and fuzzywuzzy for better NLP accuracy")
        print("  • Add more ingredient synonyms to the database")
        print("  • Fine-tune similarity thresholds")
    else:
        print("  • System is performing well!")
        print("  • Consider adding more test cases for edge cases")
        print("  • Monitor performance with real user data")
